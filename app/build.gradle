import groovy.json.JsonSlurper

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: "androidx.navigation.safeargs.kotlin"
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'dagger.hilt.android.plugin'

def readEnv() {
    final taskNames = gradle.startParameter.taskNames.join().toLowerCase()
    if (taskNames.contains("stg")) {
        return "stg"
    } else if (taskNames.contains("prod")) {
        return "prod"
    } else {
        return "dev"
    }
}

def readKeys() {
    final json = new JsonSlurper()
    final env = readEnv()
    final file = file("../keys-${env}.json").text
    return json.parseText(file).toMapString()
}

android {
    aaptOptions {
        noCompress "webp"
    }
    buildFeatures {
        viewBinding true
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        unitTests.all {
            systemProperty 'robolectric.dependency.repo.url', 'https://repo1.maven.org/maven2'
        }
    }
    compileSdkVersion buildConfig.compileSdk
    testOptions {
        animationsDisabled = true
    }

    dataBinding {
        enabled = true
    }

    defaultConfig {
        multiDexEnabled true

        applicationId "com.bukuwarung"
        minSdkVersion buildConfig.minSdk
        targetSdkVersion buildConfig.targetSdk
        versionCode buildConfig.versionCode
        versionName buildConfig.versionName
        testInstrumentationRunner "com.bukuwarung.base.runner.HiltTestRunner"

        resConfigs "en_US", "id_ID"

        //TODO :TEST REMOVING IT
        vectorDrawables.useSupportLibrary = true

        buildConfigField("String", "PLAYSTORE_URL", '"https://play.google.com/store/apps/details?id=com.bukuwarung"')

        javaCompileOptions {
            annotationProcessorOptions {
                arguments["dagger.hilt.disableModulesHaveInstallInCheck"] = "true"
                arguments["stealth.keys"] = readKeys()
            }
        }
    }
    buildTypes {
        debug {
            minifyEnabled false
            resValue "string", "clear_text_config", "true"
        }
        release {
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "false"
        }
    }
    flavorDimensions "default"
    productFlavors {
        dev {
            applicationIdSuffix ".dev"
            versionNameSuffix "-DEV"

            def contentProviderAuthority = defaultConfig.applicationId + applicationIdSuffix + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarungdev.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod.dev',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: 'TEST-RKW-RZW-4K6Z',
                    CLEVERTAP_TOKEN: 'TEST-012-032',
                    FCM_SENDER_ID:'id:*************',
                    MAPS_SDK_KEY:'AIzaSyBeIRIzqGU32sX-dJJD7iFxExu82kW5mo4'
            ]

            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod.dev"')
            buildConfigField("String", "MOEAPI", '"79FTM8WDYZWGX5BM3X6DXUPH"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungdev.page.link"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungdev.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungdev.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/p?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-dev.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-dev.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-dev.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-dev.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-dev.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-dev.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://2yvdmltuje.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-dev.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-dev.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-dev.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-dev.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-dev.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-dev.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-dev.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "S3_BUCKET", '"https://bukuwarungac-image-dev.s3.ap-southeast-1.amazonaws.com"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "true")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen.dev"')
            buildConfigField("String", "HMAC_SECRET", '"test-secret"')
        }
        stg {
            applicationIdSuffix ".staging"
            versionNameSuffix "-STG"

            def contentProviderAuthority = defaultConfig.applicationId+ applicationIdSuffix + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarungstg.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod.staging',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: 'TEST-WR5-78Z-846Z',
                    CLEVERTAP_TOKEN: 'TEST-1ba-520',
                    FCM_SENDER_ID:'id:************',
                    MAPS_SDK_KEY:'AIzaSyBeIRIzqGU32sX-dJJD7iFxExu82kW5mo4'
            ]
            buildConfigField("String", "MOEAPI", '"GRAFCYG32R1FS54ME39HGO8S"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod.staging"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungstg.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-staging-v1.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-staging-v1.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-staging-v1.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-staging-v1.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-staging-v1.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-staging-v1.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-staging-v1.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-staging-v1.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-staging-v1.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "true")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen.staging"')
            buildConfigField("String", "HMAC_SECRET", '"3a4cc7f4467c6cace7a23fb540eb07e82e20e476f2f0abd93353284e98bbc95f"')
        }
        prod {
            def contentProviderAuthority = defaultConfig.applicationId + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarung.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: '6K5-K78-9R6Z',
                    CLEVERTAP_TOKEN: 'ba3-536',
                    FCM_SENDER_ID:'id:************',
                    MAPS_SDK_KEY:'AIzaSyCtZTXmvBGj0M4F_F_WDNzucA6_SMSoo04'
            ]
            buildConfigField("String", "MOEAPI", '"GRAFCYG32R1FS54ME39HGO8S"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarung.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-v4.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-v4.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-v4.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-v4.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-v4.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-v4.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.60.0-bud"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-v4.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-v4.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-v4.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-v4.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-v4.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-v4.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-v4.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "false")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen"')
            buildConfigField("String", "HMAC_SECRET", '"1cb25c8679c149f41a54148c71d0f35a1125c4954e9efb8485b4b045b5f8d954"')
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled = true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        freeCompilerArgs = [
            "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-Xopt-in=kotlinx.coroutines.FlowPreview",
            "-Xopt-in=kotlinx.coroutines.InternalCoroutinesApi",
        ]

        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }

    packagingOptions {
        exclude 'lib/*/libnative-imagetranscoder.so'
        exclude 'lib/*/libnative-filters.so'
        exclude 'META-INF/gradle/incremental.annotation.processors'
    }
    namespace 'com.bukuwarung'

    //tmp disable AndroidTest due to mismatch with the UI
    tasks.whenTaskAdded { task ->
        if (task.name.contains("AndroidTest")) {
            task.enabled = false
        }
    }
}

dependencies {

//    implementation fileTree(include: ['*.jar', 'LightCompressor-1.2.3.aar'], dir: 'libs')
    // use aar for webview
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

    // use project for webview
//    implementation project(':android-webview')
    implementation project(':base-android')
    implementation project(':neuro')
    implementation project(':privypass')
    implementation project(':ui-component')
    implementation project(':buku-bluetooth-printer')
    implementation project(':stealth')
    implementation project(':stealth-compiler')

    // bureau libraries
    implementation "id.bureau:device-intelligence:4.2.0"

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
    implementation "com.mixpanel.android:mixpanel-android:7.5.2"

    implementation "androidx.constraintlayout:constraintlayout:2.0.4"
    implementation "androidx.multidex:multidex:2.0.0"
    implementation "androidx.recyclerview:recyclerview:1.0.0"
    implementation "androidx.cardview:cardview:1.0.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
    implementation "androidx.activity:activity-ktx:1.1.0"
    implementation "androidx.core:core-ktx:1.6.0"

    implementation "androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02"
    implementation "androidx.navigation:navigation-ui-ktx:2.3.0-alpha02"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.7.10"

    // testing
    androidTestImplementation "androidx.test:runner:1.2.0"
    androidTestImplementation "androidx.test.espresso:espresso-core:3.4.0"
    androidTestImplementation "androidx.test.espresso:espresso-contrib:3.3.0-rc01"
    androidTestImplementation "androidx.test:rules:1.3.0-rc01"
    androidTestImplementation "androidx.test.uiautomator:uiautomator:2.2.0"
    androidTestImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.0"
    androidTestImplementation "androidx.arch.core:core-testing:2.1.0"
    androidTestImplementation "androidx.test:core-ktx:1.4.0"
    testImplementation "io.mockk:mockk:1.10.0"
    testImplementation "org.robolectric:robolectric:4.3"
    testImplementation "junit:junit:4.13.2"
    testImplementation "org.mockito:mockito-inline:4.2.0"
    testImplementation "org.mockito:mockito-core:2.1.0"
    testImplementation "androidx.arch.core:core-testing:2.1.0"
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.0"
    implementation "androidx.fragment:fragment-ktx:1.4.1"
    implementation "androidx.work:work-runtime-ktx:2.7.0"
    implementation "androidx.hilt:hilt-work:1.0.0"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation "androidx.lifecycle:lifecycle-livedata-core:2.5.1"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.5.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1"
    annotationProcessor "androidx.lifecycle:lifecycle-compiler:2.5.1"

    implementation "androidx.room:room-runtime:2.5.0"
    annotationProcessor "androidx.room:room-compiler:2.5.0"
    kapt "androidx.room:room-compiler:2.5.0"
    implementation "androidx.room:room-ktx:2.5.0"
    implementation "androidx.room:room-rxjava2:2.5.0"

    implementation "com.google.android.material:material:1.9.0"
    implementation "com.google.android.gms:play-services-location:21.0.1"
    implementation "com.google.android.gms:play-services-auth-api-phone:17.0.0"
    implementation "com.google.android.gms:play-services-base:17.0.0"
    implementation "com.google.android.gms:play-services-identity:17.0.0"
    implementation "com.google.android.gms:play-services-auth:17.0.0"
    implementation "com.google.android.gms:play-services-tasks:18.0.0"
    implementation "com.google.android.play:app-update:2.1.0"

    implementation platform("com.google.firebase:firebase-bom:30.2.0")
    implementation "com.google.firebase:firebase-messaging"
    implementation "com.google.firebase:firebase-auth:19.2.0"
    implementation "com.google.firebase:firebase-firestore-ktx:20.2.0"
    implementation "com.google.firebase:firebase-dynamic-links:19.1.0"
    implementation "com.google.firebase:firebase-analytics:17.5.0"
    implementation "com.google.firebase:firebase-storage-ktx:19.2.0"
    implementation "com.google.firebase:firebase-config-ktx:20.0.0"
    implementation "com.google.android.material:material:1.9.0"

    implementation "com.google.code.gson:gson:2.9.1"

    implementation "com.android.volley:volley:1.2.0"
    implementation "com.android.installreferrer:installreferrer:1.1.2"

    implementation "com.facebook.android:facebook-android-sdk:[5,9)"
    implementation "com.facebook.shimmer:shimmer:0.5.0"

    implementation "io.reactivex.rxjava2:rxkotlin:2.4.0"
    implementation "io.reactivex.rxjava2:rxandroid:2.1.1"

    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.8.1"
    implementation "com.squareup.okhttp3:logging-interceptor:4.8.1"

    implementation "com.amplitude:android-sdk:2.16.0"
    implementation "com.appsflyer:af-android-sdk:6.2.3"
    implementation "com.google.firebase:firebase-crashlytics"
    implementation "androidx.paging:paging-runtime-ktx:3.0.0"

    implementation "com.github.bumptech.glide:glide:4.9.0"
    annotationProcessor "com.github.bumptech.glide:compiler:4.9.0"

    implementation "com.hbb20:ccp:2.7.1"
    implementation "com.github.angga-bw:ShiftColorPicker:c71dbb1a99"
    implementation "com.github.angga-bw:android-simple-tooltip:0598b236e0"
    implementation "com.airbnb.android:lottie:3.4.2"
    implementation "com.github.skydoves:powerspinner:1.0.9"
    implementation "net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3"
    implementation "com.tbuonomo:dotsindicator:4.3"

    implementation("com.journeyapps:zxing-android-embedded:4.1.0") { transitive = false }
    implementation "com.google.zxing:core:3.3.0"

    implementation "com.github.PhilJay:MPAndroidChart:v3.0.3"

    implementation "com.github.razir.progressbutton:progressbutton:2.1.0"

    // webview dependencies
    implementation "androidx.camera:camera-camera2:1.1.0-beta02"
    implementation "androidx.camera:camera-lifecycle:1.1.0-beta02"
    implementation "androidx.camera:camera-view:1.1.0-beta02"
    implementation "androidx.camera:camera-video:1.1.0-beta02"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0"
    implementation "id.zelory:compressor:3.0.1"
    implementation "com.google.mlkit:face-detection:16.1.5"
    implementation "com.squareup.retrofit2:adapter-rxjava2:2.9.0"
    implementation "io.reactivex.rxjava2:rxjava:2.2.19"

    implementation "com.github.kal72:RackMonthPicker:1.6.1"
    implementation "com.github.dewinjm:monthyear-picker:1.0.2"
    implementation "com.github.mrmike:ok2curl:0.8.0"

    implementation "com.google.android.gms:play-services-maps:18.0.0"
    implementation "com.google.android.exoplayer:exoplayer:2.15.1"
    implementation "com.google.android.exoplayer:exoplayer-hls:2.15.1"
    implementation "com.google.android.exoplayer:exoplayer-ui:2.15.1"

    implementation "com.auth0.android:jwtdecode:2.0.1"

    // performance
    implementation "com.google.firebase:firebase-perf"
    implementation "com.google.firebase:firebase-perf-ktx"


    implementation "com.zoho.salesiq:mobilisten:7.1.1"

    //desugaring
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:1.1.5")

    implementation "com.google.dagger:dagger:2.31.2"
    implementation "com.google.dagger:dagger-android:2.31.2"
    implementation "com.google.dagger:dagger-android-support:2.21"
    kapt "com.google.dagger:dagger-android-processor:2.31.2"
    kapt "com.google.dagger:dagger-compiler:2.31.2"

    // Dagger - Hilt
    implementation "com.google.dagger:hilt-android:2.44"
    kapt "com.google.dagger:hilt-android-compiler:2.44"

    androidTestImplementation "com.google.dagger:hilt-android-testing:2.44"
    kaptAndroidTest "com.google.dagger:hilt-android-compiler:2.44"

    implementation "com.gu.android:toolargetool:0.3.0"
    implementation("com.survicate:survicate-sdk:1.7.6") {
        exclude group: 'com.google.android.material', module: 'material'
    }
}


// This needed to uncomment when webview project added
configurations.all {
    resolutionStrategy {
        force 'androidx.core:core:1.8.0'
    }
}
