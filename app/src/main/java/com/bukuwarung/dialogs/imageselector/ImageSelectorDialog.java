package com.bukuwarung.dialogs.imageselector;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.bukuwarung.R;
import com.bukuwarung.activities.customerprofile.CustomerDetailActivity;
import com.bukuwarung.dialogs.base.BaseDialog;
import com.bukuwarung.dialogs.base.BaseDialogType;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public class ImageSelectorDialog extends BaseDialog {
    public Context context;
    private Activity mActivity;
    private String mOperationType;

    public ImageSelectorDialog(Context context2, Activity activity, String str) {
        super(context2, BaseDialogType.POPUP);
        this.context = context2;
        this.mActivity = activity;
        this.mOperationType = str;
        setCancellable(true);
        setUseFullWidth(false);
    }

    @Override
    public int getResId() {
        return R.layout.dialog_image_selector;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);

        setupView();
    }

    private void setupView() {
        RelativeLayout galleryLayout = findViewById(R.id.rl_gallery_option);
        findViewById(R.id.rl_camera_option).setOnClickListener(this::onCameraClicked);
        galleryLayout.setOnClickListener(this::onGalleryClicked);
    }

    private void onCameraClicked(View v) {
        try {
            if (ImageSelectorDialog.this.mOperationType.equals("") && ImageSelectorDialog.this.mActivity.getClass().getSimpleName().equals("CustomerDetailActivity")) {
                ((CustomerDetailActivity) ImageSelectorDialog.this.mActivity).setImageSourceFromCamera();
            }
            ImageSelectorDialog.this.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            ImageSelectorDialog.this.dismiss();
        }
    }

    private void onGalleryClicked(View v) {
        try {
            if (ImageSelectorDialog.this.mOperationType.equals("") && ImageSelectorDialog.this.mActivity.getClass().getSimpleName().equals("CustomerDetailActivity")) {
                ((CustomerDetailActivity) ImageSelectorDialog.this.mActivity).setGalleryImageUri();
            }
            ImageSelectorDialog.this.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            ImageSelectorDialog.this.dismiss();
        }
    }
}