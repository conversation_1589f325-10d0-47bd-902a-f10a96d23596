package com.bukuwarung.dialogs.businessselector

import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType

class BusinessType : SelectableObject {
    var businessId: Int = 0
    var category_name_id: String? = null
    var category_name_en: String? = null

    constructor() : super() {
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: Int) : super() {
        this.businessId = id
    }

    constructor(id: Int, category_name_id: String?, category_name_en: String?) : super() {
        this.businessId = id
        this.category_name_id = category_name_id
        this.category_name_en = category_name_en
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: Int, categoryName: String?) : super() {
        this.businessId = id
        this.category_name_id = categoryName
        this.category_name_en = categoryName
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: Int, category_name_id: String?, type: Int) : super() {
        this.businessId = id
        this.category_name_id = category_name_id
        setType(type)
    }

    override fun getId(): Int = businessId

    override fun getName(): String? = category_name_id

    override fun setName(newName: String?) {
        this.category_name_id = newName
    }

    override fun getOrder(): Int = -1

    override fun equals(other: Any?): Boolean {
        return other is BusinessType && other.businessId == businessId
    }

    override fun hashCode(): Int = businessId


}
