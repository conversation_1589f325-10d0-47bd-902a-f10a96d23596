package com.bukuwarung.dialogs.businessselector;

import android.content.Context;

import com.bukuwarung.R;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectDialog;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType;
import com.bukuwarung.preference.AppConfigManager;

import java.util.List;

public class BusinessSelectorDialog extends SelectableObjectDialog {

    private BusinessSelectorDialog(Context context, List<BusinessType> data, OnObjectClickListener onObjectClickListener) {
        super(context, data,
                R.string.select_business, R.string.business_options, R.string.your_business_options,
                R.string.add_new_business_type, R.string.business_name_label);
        setOnObjectClickListener(onObjectClickListener);
    }

    public static BusinessSelectorDialog getInstance(Context context,
                                                     List<BusinessType> data,
                                                     BusinessSelectedListener businessSelectedListener) {

        // this to ensure predefined category will always be there no matter what
        List<BusinessType> defaultBusinessType = AppConst.defaultBusinessType(context);

        for (BusinessType businessType : defaultBusinessType) {
            // if predefined category doesn't exist for some reasons, we add it.
            if (!data.contains(businessType))
                data.add(businessType);
        }

        return new BusinessSelectorDialog(
                context,
                data,
                new SelectableObjectDialog.OnObjectClickListener() {
                    @Override
                    public void onObjectClicked(SelectableObjectDialog dialog, SelectableObject datum) {
                        businessSelectedListener.onBusinessSelected(datum);
//                        dialog.dismiss();
                    }

                    @Override
                    public void onNewObjectAdded(SelectableObjectDialog dialog, String newBusinessName) {
                        final BusinessType businessType = new BusinessType(
                                newBusinessName.hashCode(),
                                newBusinessName,
                                SelectableObjectViewHolderType.SECONDARY_CONTENT
                        );

                        dialog.addNewData(businessType);
                        AppConfigManager.getInstance().addNewBusinessType(businessType);
                        businessSelectedListener.onNewBusinessAdded(businessType);
                    }

                    @Override
                    public void onObjectNameChanged(SelectableObjectDialog dialog,
                                                    String newName, SelectableObject datum) {
                        dialog.changeExistingData(datum, newName);
                        AppConfigManager.getInstance().editBusinessType((BusinessType) datum, newName);
                        businessSelectedListener.onBusinessSelected(datum);
                    }

                    @Override
                    public void onObjectDeleted(SelectableObjectDialog dialog, SelectableObject datum) {
                        dialog.deleteData(datum);
                        AppConfigManager.getInstance().deleteBusinessType((BusinessType) datum);
                    }
                }
        );
    }

    public interface BusinessSelectedListener {
        void onBusinessSelected(SelectableObject datum);
        void onNewBusinessAdded(SelectableObject datum);
    }
}
