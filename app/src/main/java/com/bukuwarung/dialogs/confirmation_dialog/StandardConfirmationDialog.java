package com.bukuwarung.dialogs.confirmation_dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bukuwarung.R;
import com.google.android.material.button.MaterialButton;

public final class StandardConfirmationDialog extends Dialog implements View.OnClickListener {
    //dialog title
    private final String title;
    //warning message for delete operation
    private final String bodyText;

    public StandardConfirmationDialog(Context context, String title, String bodyText) {
        super(context);
        this.title = title;
        this.bodyText = bodyText;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_standard_confirm);
        Window window = getWindow();
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);;
        MaterialButton okTv =  findViewById(R.id.ok);
        TextView titleTv = findViewById(R.id.title);
        TextView bodyTv = findViewById(R.id.body);
        titleTv.setText(title);
        bodyTv.setText(bodyText);
        okTv.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ok:
                this.dismiss();
                return;
        }
    }
}
