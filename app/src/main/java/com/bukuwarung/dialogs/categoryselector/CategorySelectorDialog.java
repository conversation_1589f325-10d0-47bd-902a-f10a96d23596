package com.bukuwarung.dialogs.categoryselector;

import android.app.Activity;
import android.content.Context;

import com.bukuwarung.R;
import com.bukuwarung.activities.categorydetail.tasks.DeleteCashAsyncTask;
import com.bukuwarung.activities.categorydetail.tasks.UpdateCashAsyncTask;
import com.bukuwarung.activities.expense.NewCashTransactionActivity;
import com.bukuwarung.activities.expense.category.Category;
import com.bukuwarung.activities.expense.category.CategoryUtil;
import com.bukuwarung.activities.expense.dialog.DeleteConfirmationDialog;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectDialog;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;

import java.util.List;

public class CategorySelectorDialog extends SelectableObjectDialog {

    private static boolean isSelected = false;

    private CategorySelectorDialog(Context context, List<Category> data, OnObjectClickListener onObjectClickListener) {
        super(context, data,
                R.string.select_category, R.string.category_options, R.string.your_category_options,
                R.string.add_new_categor, R.string.enter_category_name);
        setOnObjectClickListener(onObjectClickListener);
    }

    public static CategorySelectorDialog getInstance(Context context,
                                                     List<Category> data,
                                                     int categoryType,
                                                     OnCategorySelectedListener onCategorySelectedListener) {

        // this to ensure predefined category will always be there no matter what
        List<Category> predefinedCategories = categoryType > 0 ? CategoryUtil.getCashInCategories()
                : CategoryUtil.getCashOutCategories();

        for (Category predefinedCategory : predefinedCategories) {
            // if predefined category doesn't exist for some reasons, we add it.
            if (!data.contains(predefinedCategory)) {
                data.add(predefinedCategory);
            }
        }

        return new CategorySelectorDialog(
                context,
                data,
                new SelectableObjectDialog.OnObjectClickListener() {
                    @Override
                    public void onObjectClicked(SelectableObjectDialog dialog, SelectableObject datum) {
                        onCategorySelectedListener.onCategorySelected((Category) datum, dialog);

                        if (context instanceof NewCashTransactionActivity) {
                            ((NewCashTransactionActivity)context).setSelectedCategory((Category) datum);
                        }
                    }

                    @Override
                    public void onNewObjectAdded(SelectableObjectDialog dialog, String newBusinessName) {
                        String id = AppIdGenerator.resourceUUID();
                        final Category category = new Category(
                                id,
                                newBusinessName,
                                newBusinessName,
                                categoryType,
                                SelectableObjectViewHolderType.SECONDARY_CONTENT
                        );
                        dialog.addNewData(
                                category
                        );

//                        AppConfigManager.getInstance().addNewCashCategory(category, categoryType);

                        dialog.setSelectedName(newBusinessName);
                        if (context instanceof NewCashTransactionActivity) {
                            ((NewCashTransactionActivity)context).setSelectedCategory(category);
                        }

                        dialog.dismiss();
                    }

                    @Override
                    public void onObjectNameChanged(SelectableObjectDialog dialog,
                                                    String newName, SelectableObject datum) {
                        boolean selectedBefore = dialog.getSelectedName() != null
                                && dialog.getSelectedName().equalsIgnoreCase(datum.getName());

                        dialog.changeExistingData(datum, newName);

                        CashCategoryEntity categoryEntity = CashRepository.getInstance(context)
                                .getCashCategoryById(datum.getIdString());

                        if (categoryEntity != null) {
                            categoryEntity.name = newName;

                            new UpdateCashAsyncTask()
                                    .execute(new CashCategoryEntity[]{categoryEntity});
                        }

//                        AppConfigManager.getInstance().editCashCategory(
//                                (Category) datum,
//                                newName,
//                                categoryType
//                        );

                        datum.setName(newName);

                        // chosen before, need to rechoose with new name
                        if (selectedBefore) {
                            dialog.setSelectedName(newName);

                            if (context instanceof NewCashTransactionActivity) {
                                ((NewCashTransactionActivity)context).setSelectedCategory((Category) datum);
                            }
                        }
                    }

                    @Override
                    public void onObjectDeleted(SelectableObjectDialog dialog, SelectableObject datum) {
                        String title = context.getString(R.string.category_change_title, datum.getName());
                        Category category = (Category) datum;
                        int trxWithThisCategory = CashRepository.getInstance(context)
                                .countAllCategoryTransactions(datum.getIdString(), User.getBusinessId());
                        String message = context.getString(R.string.category_change_body, datum.getName());

                        isSelected = false;
                        DeleteConfirmationDialog deleteConfirmationDialog = new DeleteConfirmationDialog( context,title,message,
                                (compoundButton, isChecked) -> {
                            isSelected = isChecked;
                            },view -> {
                            dialog.deleteData(datum);

                            CashCategoryEntity categoryEntity = CashRepository.getInstance(context)
                                    .getCashCategoryById(datum.getIdString());
                            Activity activity = null;
                            if(context instanceof Activity) {
                                activity = (Activity) context;
                            }
                            if (categoryEntity != null && activity != null) {
                                new DeleteCashAsyncTask(activity, false, isSelected).execute(
                                        new CashCategoryEntity[]{categoryEntity});
                            }

//                            AppConfigManager.getInstance().deleteCashCategory(
//                                    (Category) datum,
//                                    categoryType
//                            );

                            // chosen before, need to dismiss bcs transaction didn't exist anymore
                            if (dialog.getSelectedName() != null &&
                                    dialog.getSelectedName().equalsIgnoreCase(datum.getName())) {
                                MainActivity.startActivityAndClearTop(context);
                            }
                        });
                        dialog.getContext().setTheme(R.style.RoundDialog);
                        deleteConfirmationDialog.show();

                    }
                }
        );
    }



    public interface OnCategorySelectedListener {
        void onCategorySelected(Category category, SelectableObjectDialog dialog);
    }

}
