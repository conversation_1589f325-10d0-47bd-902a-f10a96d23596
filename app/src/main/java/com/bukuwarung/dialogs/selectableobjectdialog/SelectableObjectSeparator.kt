package com.bukuwarung.dialogs.selectableobjectdialog

class SelectableObjectSeparator(viewHolderType: Int) : SelectableObject() {

    init {
        setType(viewHolderType)
    }

    override fun getId(): Int {
        return -1
    }

    override fun getName(): String {
        return ""
    }

    override fun setName(newName: String) {
        // Empty implementation
    }

    override fun getOrder(): Int {
        return 0
    }

    companion object {
        val PRIMARY_SEPARATOR = SelectableObjectSeparator(SelectableObjectViewHolderType.PRIMARY_SEPARATOR)
        val SECONDARY_SEPARATOR = SelectableObjectSeparator(SelectableObjectViewHolderType.SECONDARY_SEPARATOR)
    }
}
