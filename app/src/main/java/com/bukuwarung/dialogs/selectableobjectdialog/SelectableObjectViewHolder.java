package com.bukuwarung.dialogs.selectableobjectdialog;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;


public final class SelectableObjectViewHolder extends RecyclerView.ViewHolder {

    private final ImageView moreBtn;
    private final ConstraintLayout container;
    private final AppCompatRadioButton radioButton;
    private final TextView titleTextView;

    final SelectableObjectAdapter adapter;
    private final SelectableObjectAdapter.OnItemClickListener onItemClickListener;

    public SelectableObjectViewHolder(SelectableObjectAdapter adapter,
                               View view, SelectableObjectAdapter.OnItemClickListener onItemClickListener) {
        super(view);
        this.adapter = adapter;
        this.onItemClickListener = onItemClickListener;
        this.container = view.findViewById(R.id.container);
        this.radioButton = view.findViewById(R.id.radioButton);
        this.titleTextView = view.findViewById(R.id.title);
        this.moreBtn = view.findViewById(R.id.optionalBtn);
    }

    public final void bind(SelectableObject object, boolean selected) {
        bindContainer(object);
        bindText(object);
        bindRadioButton(selected);
        bindMenuBtn(object);
    }

    private void bindMenuBtn(SelectableObject object) {
        if (object.getType() == SelectableObjectViewHolderType.SECONDARY_CONTENT) {
            moreBtn.setVisibility(View.VISIBLE);
            moreBtn.setOnClickListener(view -> onItemClickListener.onOptionsItemClick(view, object));
        } else {
            moreBtn.setVisibility(View.GONE);
        }
    }

    private void bindContainer(SelectableObject object) {
        container.setOnClickListener(view -> onItemClickListener.onItemClick(object));
    }

    private void bindText(SelectableObject object) {
        titleTextView.setText(object.getName());
    }

    private void bindRadioButton(boolean selected) {
        radioButton.setChecked(selected);
    }

}
