package com.bukuwarung.dialogs.selectableobjectdialog;


import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.utils.UtilsKt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class SelectableObjectAdapter<T extends SelectableObject>
        extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context context;
    public List<T> data;
    public int primarySeparatorResId;
    public int secondarySeparatorResId;
    public int selectedId;
    public String selectedName; // for workaround category id is string not int
    private OnItemClickListener onItemClickListener;

    public final Context getContext() {
        return this.context;
    }

    public SelectableObjectAdapter(List<T> data, Context context,
                                   OnItemClickListener onItemClickListener, int primarySeparatorResId,
                                   int secondarySeparatorResId) {
        this.data = data;
        this.context = context;
        this.onItemClickListener = onItemClickListener;
        this.primarySeparatorResId = primarySeparatorResId;
        this.secondarySeparatorResId = secondarySeparatorResId;

        addSeparator();
    }

    public void setSelectedId(int selectedId) {
        this.selectedId = selectedId;
    }

    public void setSelectedName(String selectedName) {
        this.selectedName = selectedName;
    }

    public void addSeparator() {
        // GROUP BY THE DATA BY TYPE
        Map<Integer, List<T>> map = new HashMap();
        for (T datum : data) {
            if (map.containsKey(datum.getType())) {
                map.get(datum.getType()).add(datum);
            } else {
                final List<T> list = new ArrayList<T>();
                list.add(datum);
                map.put(datum.getType(), list);
            }
        }

        // ADD SEPARATOR ON TOP OF EVERY DATA
        List<T> transformedData = new ArrayList();
        List<T> secondaryContents = map.get(SelectableObjectViewHolderType.SECONDARY_CONTENT);
        List<T> primaryContents = map.get(SelectableObjectViewHolderType.PRIMARY_CONTENT);

        if (secondaryContents != null)
            secondaryContents = UtilsKt.sortAlphabetically(secondaryContents);

        if (primaryContents != null)
            primaryContents = UtilsKt.sortAlphabetically(primaryContents);

        if (secondaryContents != null && !secondaryContents.isEmpty()) {
            transformedData.add((T) SelectableObjectSeparator.SECONDARY_SEPARATOR);
            transformedData.addAll(secondaryContents);
        }
        if (primaryContents != null && !primaryContents.isEmpty()) {
            transformedData.add((T) SelectableObjectSeparator.PRIMARY_SEPARATOR);
            transformedData.addAll(primaryContents);
        }

        this.data = transformedData;
    }

    public void notifyAdapterDataSetChanged() {
        addSeparator();

        super.notifyDataSetChanged();
    }

    @Override
    public int getItemViewType(int i) {
        return this.data.get(i).getType();
    }

    public long getItemId(int i) {
        return this.data.get(i).getId();
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        if (tag == SelectableObjectViewHolderType.PRIMARY_SEPARATOR) {
            return new SelectableSeparatorViewHolder(
                    LayoutInflater.from(
                            viewGroup.getContext()).inflate(R.layout.general_rv_separator,
                            viewGroup,
                            false
                    ),
                    primarySeparatorResId
            );
        } else if (tag == SelectableObjectViewHolderType.SECONDARY_SEPARATOR) {
            return new SelectableSeparatorViewHolder(
                    LayoutInflater.from(
                            viewGroup.getContext()).inflate(R.layout.general_rv_separator,
                            viewGroup,
                            false
                    ),
                    secondarySeparatorResId
            );
        } else {
            return new SelectableObjectViewHolder(
                    this,
                    LayoutInflater.from(
                            viewGroup.getContext()).inflate(R.layout.selectable_object_item,
                            viewGroup,
                            false
                    ),
                    onItemClickListener
            );
        }
    }

    public void onBindViewHolder(RecyclerView.ViewHolder holder, int i) {
        SelectableObject object = this.data.get(i);
        if (object.getType() == SelectableObjectViewHolderType.PRIMARY_CONTENT
                || object.getType() == SelectableObjectViewHolderType.SECONDARY_CONTENT)
            ((SelectableObjectViewHolder) holder).bind(object,
                    selectedName == null
                            ? selectedId == object.getId()
                            : selectedName.equals(object.getName())
            );
    }

    public int getItemCount() {
        return this.data.size();
    }

    public interface OnItemClickListener {
        void onItemClick(SelectableObject datum);
        void onOptionsItemClick(View view, SelectableObject datum);
    }
}
