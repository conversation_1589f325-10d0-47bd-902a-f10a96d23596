package com.bukuwarung.dialogs.selectableobjectdialog

abstract class SelectableObject {

    private var viewHoldertype: Int = SelectableObjectViewHolderType.PRIMARY_CONTENT

    abstract fun getId(): Int
    abstract fun getName(): String?
    abstract fun setName(newName: String?)
    abstract fun getOrder(): Int

    open fun getIdString(): String? = getName()

    fun getType(): Int = viewHoldertype

    fun setType(viewHoldertype: Int) {
        this.viewHoldertype = viewHoldertype
    }

    override fun toString(): String = getName().orEmpty()
}
