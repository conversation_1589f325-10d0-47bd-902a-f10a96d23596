package com.bukuwarung.dialogs.selectableobjectdialog.addnewdialog;

import android.content.Context;
import android.os.Bundle;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.dialogs.base.BaseDialog;
import com.bukuwarung.dialogs.base.BaseDialogType;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;

public class AddNewSelectableObjectDialog extends BaseDialog {
    private TextView title;
    private TextView btnCancel;
    private TextView btnSave;
    private TextInputEditText editText;
    private TextInputLayout inputLayout;
    private OnSaveListener onSaveListener;

    private String predefinedTitle;
    private int titleResId;
    private int hintResId;

    public AddNewSelectableObjectDialog(Context context, int titleResId, int hintResId) {
        super(context, BaseDialogType.POPUP);
        setCancellable(true);
        setUseFullWidth(false);

        this.titleResId = titleResId;
        this.hintResId = hintResId;
    }

    public void setPredefinedTitle(String predefinedTitle) {
        this.predefinedTitle = predefinedTitle;
    }

    public void setOnSaveListener(OnSaveListener onSaveListener) {
        this.onSaveListener = onSaveListener;
    }

    @Override
    public int getResId() {
        return R.layout.dialog_new_selectable;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);

        setupView();
    }

    private void setupView() {
        title = findViewById(R.id.title);
        btnCancel = findViewById(R.id.btn_cancel);
        btnSave = findViewById(R.id.btn_save);
        editText = findViewById(R.id.nameEditText);
        inputLayout = findViewById(R.id.inputLayout);

        title.setText(titleResId);
//        inputLayout.setHint(getContext().getResources().getString(hintResId));
        btnCancel.setOnClickListener(view -> dismiss());
        btnSave.setOnClickListener(view -> {
            InputUtils.hideKeyboard(getContext());
            if (onSaveListener != null) {
                String businessCategory = String.valueOf(editText.getText()).trim();
                if( businessCategory == null || businessCategory.length() == 0)
                    NotificationUtils.alertError(getContext().getString(R.string.kategori_usaha));
                else{
                    onSaveListener.onSave(businessCategory);
                    dismiss();
                }
            }
        });
        if (predefinedTitle != null) editText.setText(predefinedTitle);
        editText.requestFocus();
        InputUtils.showKeyboard(getContext());
    }

    public interface OnSaveListener {
        public void onSave(String text);
    }
}
