package com.bukuwarung.dialogs.selectableobjectdialog;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.dialogs.base.BaseDialog;
import com.bukuwarung.dialogs.base.BaseDialogType;
import com.bukuwarung.dialogs.selectableobjectdialog.addnewdialog.AddNewSelectableObjectDialog;
import com.bukuwarung.dialogs.selectableobjectdialog.alreadyexistdialog.AlreadyExistDialog;
import com.bukuwarung.utils.InputUtils;
import com.google.android.material.button.MaterialButton;

import java.util.ArrayList;
import java.util.List;

/**
 * Base class for selectable object dialog.
 * e.g. Business select dialog, category select dialog, etc.
 */
public class SelectableObjectDialog<T extends SelectableObject> extends BaseDialog {

    private TextView emptyTextView;
    private MaterialButton addMoreBtn;
    private RelativeLayout searchLayout;
    private RelativeLayout mainLayout;
    private EditText searchQueryBox;
    private ImageView clearSearch;
    private ImageView closeDialog;
    private ImageView openSearch;
    private TextView titleTextView;
    private RecyclerView mainRecyclerView;

    private Context context;
    private int selectedId;
    private String selectedName; // workaround for category id using strings while other use int
    private final int titleRes;
    private final int mainRVTitleRes;
    private final int secondaryRVTitleRes;
    private final int addNewTitleRes;
    private final int addNewHintRes;
    private List<T> data;
    private SelectableObjectAdapter adapter;
    private OnObjectClickListener onObjectClickListener;

    private boolean isSearchOpened = false;

    public SelectableObjectDialog(Context context, List<T> data,
                                  int titleRes, int mainRVTitleRes, int secondaryRVTitleRes,
                                  int addNewTitleRes, int addNewHintRes) {
        super(context, BaseDialogType.FULL_SCREEN);

        this.context = context;
        this.data = data;
        this.titleRes = titleRes;
        this.mainRVTitleRes = mainRVTitleRes;
        this.secondaryRVTitleRes = secondaryRVTitleRes;
        this.addNewTitleRes = addNewTitleRes;
        this.addNewHintRes = addNewHintRes;
    }

    public void setOnObjectClickListener(OnObjectClickListener onObjectClickListener) {
        this.onObjectClickListener = onObjectClickListener;
    }

    public void setSelectedId(int selectedId) {
        this.selectedId = selectedId;
    }
    public void setSelectedName(String selectedName) {
        this.selectedName = selectedName;
        if (adapter != null) adapter.setSelectedName(selectedName);
    }

    public int getSelectedId() {  return this.selectedId; }
    public String getSelectedName() {  return this.selectedName; }

    public void addNewData(T newData) {
        data.add(newData);
        adapter.data = data;
        adapter.notifyAdapterDataSetChanged();
    }

    public void changeExistingData(T datum, String newName) {
        int index = data.indexOf(datum);
        T datumFromDialog = data.get(index);
        datumFromDialog.setName(newName);

        adapter.data = data;
        adapter.notifyAdapterDataSetChanged();
    }

    public void deleteData(T datum) {
        data.remove(datum);

        adapter.data = data;
        adapter.notifyAdapterDataSetChanged();
    }

    @Override
    public int getResId() {
        return R.layout.dialog_selectable_object;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);

        setupView();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (isSearchOpened) {
            closeSearch();
        } else {
            dismiss();
        }
    }

    private void setupView() {
        setupCloseBtn();
        setupTitle();
        setupMainRV();
        setupSearch();
        setupAddMoreBtn();
    }

    private void setupAddMoreBtn() {
        this.addMoreBtn = findViewById(R.id.addMoreBtn);
        addMoreBtn.setOnClickListener(view -> openAddMoreBtnDialog());
        addMoreBtn.setText(addNewTitleRes);
    }

    private void openAddMoreBtnDialog() {
        final AddNewSelectableObjectDialog dialog = new AddNewSelectableObjectDialog(
                context, addNewTitleRes, addNewHintRes
        );
        dialog.setOnSaveListener(newObjectName -> {
            boolean contains = false;
            for (T datum : data) {
                String name = datum.getName();
                if (name != null && newObjectName != null
                        && newObjectName.trim().equalsIgnoreCase(name.trim())) {
                    contains = true;
                    break;
                }
            }

            if (contains) {
                try {
                    final AlreadyExistDialog alreadyExistDialog = new AlreadyExistDialog(
                            context
                    );
                    dialog.dismiss();
                    alreadyExistDialog.show();
                } catch (Exception ex) {
                    Log.e("SelectableObjectDialog", "Exception", ex);
                }
            } else {
                onObjectClickListener.onNewObjectAdded(this, newObjectName);
            }
        });
        dialog.show();
    }

    private void setupSearch() {
        this.emptyTextView = findViewById(R.id.textEmpty);
        this.mainLayout = findViewById(R.id.mainContainer);
        this.searchLayout = findViewById(R.id.searchLayout);
        this.clearSearch = findViewById(R.id.clear);
        this.openSearch = findViewById(R.id.openSearchBtn);
        this.searchQueryBox = findViewById(R.id.searchQueryBox);

        this.searchQueryBox.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) { }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) { }

            @Override
            public void afterTextChanged(Editable editable) {
                onSearchQueryChanged(String.valueOf(editable));
            }
        });
        this.openSearch.setOnClickListener(view -> openSearch());
        this.clearSearch.setOnClickListener(view -> closeSearch());
    }

    private void onSearchQueryChanged(String searchQuery) {
        if (searchQuery == null) return;
        List<SelectableObject> filteredData = new ArrayList<>();
        for(SelectableObject object : data){
            if ((object.getType() == SelectableObjectViewHolderType.PRIMARY_CONTENT
                    || object.getType() == SelectableObjectViewHolderType.SECONDARY_CONTENT)
                    && object.getName() != null && object.getName().toLowerCase().contains(searchQuery.toLowerCase()))
                filteredData.add(object);
        }
        if (filteredData.isEmpty()) {
            mainRecyclerView.setVisibility(View.GONE);
            emptyTextView.setVisibility(View.VISIBLE);
        } else {
            mainRecyclerView.setVisibility(View.VISIBLE);
            emptyTextView.setVisibility(View.GONE);
        }
        this.adapter.data = filteredData;
        this.adapter.notifyAdapterDataSetChanged();
    }

    private void openSearch() {
        InputUtils.showKeyboard(getContext());
        searchQueryBox.requestFocus();
        isSearchOpened = true;
        this.searchLayout.setVisibility(View.VISIBLE);
        this.mainLayout.setVisibility(View.GONE);
    }

    private void closeSearch() {
        InputUtils.hideKeyboard(getContext());
        isSearchOpened = false;
        this.searchLayout.setVisibility(View.GONE);
        this.mainLayout.setVisibility(View.VISIBLE);

        this.adapter.data = data;
        this.adapter.notifyAdapterDataSetChanged();
    }

    private void setupCloseBtn() {
        this.closeDialog = findViewById(R.id.closeDialog);
        this.closeDialog.setOnClickListener(view -> dismiss());
    }

    private void setupTitle() {
        this.titleTextView = findViewById(R.id.title);
        titleTextView.setText(titleRes);
    }

    private void setupMainRV() {
        this.mainRecyclerView = findViewById(R.id.mainRV);

        this.adapter = new SelectableObjectAdapter(
                data,
                getContext(),
                new SelectableObjectAdapter.OnItemClickListener() {
                    @Override
                    public void onItemClick(SelectableObject datum) {
                        onItemClickListener(datum);
                    }

                    @Override
                    public void onOptionsItemClick(View view, SelectableObject datum) {
                        onOptionsItemClickListener(view, datum);
                    }
                },
                mainRVTitleRes,
                secondaryRVTitleRes
        );
        this.adapter.setHasStableIds(true);
        this.adapter.setSelectedId(selectedId);
        if (selectedName != null) this.adapter.setSelectedName(selectedName);

        this.mainRecyclerView.setHasFixedSize(false);
        this.mainRecyclerView.setAdapter(this.adapter);

        this.mainRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.VERTICAL,
                false));
    }

    private void onItemClickListener(SelectableObject datum) {
        this.adapter.setSelectedId(datum.getId());
        this.adapter.notifyAdapterDataSetChanged();

        onObjectClickListener.onObjectClicked(this, datum);
    }

    private void onOptionsItemClickListener(View view, SelectableObject datum) {
        PopupMenu popup = new PopupMenu(context, view);
        popup.getMenuInflater().inflate(R.menu.popup_selectable, popup.getMenu());

        popup.setOnMenuItemClickListener(menuItem -> {
            switch (menuItem.getItemId()) {
                case R.id.change:
                    onChangeClick(datum);
                    break;
                case R.id.delete:
                    onDeleteClick(datum);
                    break;
            }
            return true;
        });

        popup.show();
    }

    private void onChangeClick(SelectableObject datum) {
        final AddNewSelectableObjectDialog dialog = new AddNewSelectableObjectDialog(
                context, R.string.add_new_business_type, R.string.business_name_label
        );
        dialog.setPredefinedTitle(datum.getName());
        dialog.setOnSaveListener(newObjectName -> {
            boolean contains = false;
            for (T datums : data) {
                String name = datums.getName();
                if (name != null && newObjectName != null
                        && newObjectName.trim().equalsIgnoreCase(name.trim())) {
                    contains = true;
                    break;
                }
            }

            if (contains) {
                try {
                    final AlreadyExistDialog alreadyExistDialog = new AlreadyExistDialog(
                            context
                    );
                    dialog.dismiss();
                    alreadyExistDialog.show();
                } catch (Exception ex) {
                    Log.e("SelectableObjectDialog", "Exception", ex);
                }
            } else {
                onObjectClickListener.onObjectNameChanged(this, newObjectName, datum);
            }
        });
        dialog.show();
    }

    private void onDeleteClick(SelectableObject datum) {
        onObjectClickListener.onObjectDeleted(this, datum);
    }

    public interface OnObjectClickListener {
        void onObjectClicked(SelectableObjectDialog dialog, SelectableObject datum);
        void onNewObjectAdded(SelectableObjectDialog dialog, String newObjectName);
        void onObjectNameChanged(SelectableObjectDialog dialog, String newObjectName, SelectableObject datum);
        void onObjectDeleted(SelectableObjectDialog dialog, SelectableObject datum);
    }
}
