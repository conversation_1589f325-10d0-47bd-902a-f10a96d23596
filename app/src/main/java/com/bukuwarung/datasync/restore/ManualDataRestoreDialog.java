package com.bukuwarung.datasync.restore;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import com.bukuwarung.R;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.datasync.AppBookSyncManager;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.datasync.AppTransSyncManager;
import com.bukuwarung.preference.SyncManager;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.CoroutineHelper;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public class ManualDataRestoreDialog extends Dialog {

    public static int UPDATE_PROGRESS = 1;
    private int target = -1;
    TextView statusTv;
    LinearLayout preparingSetupLayout;
    LinearLayout restoringDataLayout;
    LinearLayout restoredLayout;
    MaterialButton completeBtn;
    Activity activity;
    private SyncManager syncManager = SyncManager.getInstance();
    private CoroutineHelper coroutineHandler;

    public ManualDataRestoreDialog(Context context, Activity activity) {
        super(context);
        this.activity = activity;
        this.coroutineHandler = new CoroutineHelper();
    }

    public ManualDataRestoreDialog(Context context, Activity activity, int target) {
        super(context);
        this.activity = activity;
        this.target = target;
        this.coroutineHandler = new CoroutineHelper();
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_data_restore);
        Window window = getWindow();
        window.setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        getWindow().setGravity(Gravity.CENTER);
        this.setCancelable(false);
        ProgressBar progressBar = findViewById(R.id.progress_bar);
        preparingSetupLayout = findViewById(R.id.preparing_layout);
        completeBtn = findViewById(R.id.completeBtn);
        restoringDataLayout = findViewById(R.id.restoring_layout);
        restoredLayout = findViewById(R.id.recovered_layout);
        final Dialog dlg = this;
        completeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    dlg.dismiss();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
        statusTv = findViewById(R.id.statusTv);
        setRecoveringState();
//      boolean dataRestoreNeeded = syncManager.hasManualRestoredCustomer() || !syncManager.hasManualRestoredTransactions() || !syncManager.hasManualRestoredCashCategory() || !syncManager.hasManualRestoredCashTransactions() || !syncManager.hasManualRestoredProducts()
        if (target == -1) {
            //new AsyncManualDataRestore(this, activity, progressBar, statusTv).execute(new Void[0]);
            coroutineHandler.syncManualDataRestore(this, activity, progressBar);
        } else if (target == 1) {
            //new AsyncManualDataRestoreUtang(this, activity, progressBar, statusTv).execute(new Void[0]);
            coroutineHandler.syncManualDataRestoreUtang(this, activity, progressBar);
        } else if (target == 2) {
            //new AsyncManualDataRestoreExpense(this, activity, progressBar, statusTv).execute(new Void[0]);
            coroutineHandler.syncManualDataRestoreExpense(this, activity, progressBar);
        } else if (target == 3) {
            //new AsyncManualDataRestoreProduct(this, activity, progressBar, statusTv).execute(new Void[0]);
            coroutineHandler.syncManualDataRestoreProduct(this, activity, progressBar);
        }
    }

    @Override
    protected void onStop() {
        coroutineHandler.cancel();
        super.onStop();
    }

    public void updateStatus(final String statusText) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                statusTv.setText(statusText);
            }
        });
    }

    public void setRecoveringState() {
        ComponentUtil.show(preparingSetupLayout, false);
        ComponentUtil.show(restoringDataLayout, true);
    }

    public void setCompleteState() {
        ComponentUtil.show(preparingSetupLayout, false);
        ComponentUtil.show(restoringDataLayout, false);
        ComponentUtil.show(restoredLayout, true);
    }

    private void updateFirstSyncStatus(boolean skip) {
        try {
            SetupManager.getInstance().hasRestored(true);
            AppAnalytics.trackEvent("load_homescreen_after_restore",false,false,false);

            final Handler handler = new Handler();
            final Dialog dlg = this;
            if (skip) {
                try {
                    dlg.dismiss();
                }catch (Exception e){
                    e.printStackTrace();
                }
            } else {
                setCompleteState();
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            dlg.dismiss();
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }, 1000);
            }

        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        try {
            AppTransSyncManager.getInstance().setTransSyncTime(System.currentTimeMillis());
            AppBookSyncManager.getInstance().setBookSyncTime(System.currentTimeMillis());
            AppCustomerSyncManager.getInstance().setCustomerSyncTime(System.currentTimeMillis());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
