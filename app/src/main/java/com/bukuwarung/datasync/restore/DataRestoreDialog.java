package com.bukuwarung.datasync.restore;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.constants.FirestoreConst;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.CoroutineHelper;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.firestore.QuerySnapshot;

public class DataRestoreDialog extends Dialog {

    TextView statusTv;
    LinearLayout preparingSetupLayout;
    LinearLayout restoringDataLayout;
    LinearLayout restoredLayout;
    private String targetId;
    private String tableName;
    private String filterColumn;
    MaterialButton completeBtn;
    Activity activity;
    OnCompleteListener<QuerySnapshot> onCompleteListener;
    private CoroutineHelper coroutineHandler;

    public DataRestoreDialog(Context context, Activity activity, String targetId, String tableName, String filterColumn) {
        super(context);
        this.targetId = targetId;
        this.tableName = tableName;
        this.filterColumn = filterColumn;
        this.activity = activity;
        this.coroutineHandler = new CoroutineHelper();
    }

    public void setOnCompleteListener(OnCompleteListener<QuerySnapshot> onCompleteListener){
        this.onCompleteListener = onCompleteListener;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_data_restore);
        Window window = getWindow();
        window.setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        getWindow().setGravity(Gravity.CENTER);
        this.setCancelable(false);
        preparingSetupLayout = findViewById(R.id.preparing_layout);
        completeBtn = findViewById(R.id.completeBtn);
        restoringDataLayout = findViewById(R.id.restoring_layout);
        restoredLayout = findViewById(R.id.recovered_layout);
        statusTv = findViewById(R.id.statusTv);
        setRecoveringState();
        if(tableName.equals(FirestoreConst.COLLECTION_TRANSACTION_ITEM_STORE)){
            //new AsyncTransactionItemDataRestore(this, targetId, tableName, filterColumn).execute(new Void[0]);
            coroutineHandler.syncTransactionItemDataRestore(this, targetId, tableName);
        }
    }

    @Override
    protected void onStop() {
        coroutineHandler.cancel();
        super.onStop();
    }

    public void updateStatus(final String statusText) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                statusTv.setText(statusText);
            }
        });
    }

    public void setRecoveringState() {
        ComponentUtil.show(preparingSetupLayout, true);
        ComponentUtil.show(restoringDataLayout, false);
    }

    public void setInprogressState() {
        ComponentUtil.show(preparingSetupLayout, false);
        ComponentUtil.show(restoringDataLayout, true);
    }

    public void setCompleteState() {
        ComponentUtil.show(preparingSetupLayout, false);
        ComponentUtil.show(restoringDataLayout, false);
        ComponentUtil.show(restoredLayout, true);
    }

    public void resumeActivity(){
        if(tableName.equals(FirestoreConst.COLLECTION_TRANSACTION_ITEM_STORE)) {
            ((CashTransactionDetailActivity) activity).resumeAfterRestore(targetId);
        }
        this.dismiss();
    }
}
