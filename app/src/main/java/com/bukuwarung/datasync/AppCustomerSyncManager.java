package com.bukuwarung.datasync;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import androidx.annotation.NonNull;

import com.bukuwarung.Application;
import com.bukuwarung.database.FirestoreDataProvider;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.SmsEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.AccountingDataSyncHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppCustomerSyncManager {
    private static AppCustomerSyncManager appSyncManager;
    public static final String DATA_TABLE = "customer_store";
    private final String SYNC_TABLE = "customer_sync_seq";
    int PRIVATE_MODE = 0;
    private Context context;
    Editor editor;
    SharedPreferences pref;

    private FirestoreDataProvider mDataProvider;
    private FirebaseFirestore mFirestore;
    private Query mQuery;

    private AppCustomerSyncManager(Context context2) {
        try{
        this.context = context2;
        this.pref = context2.getSharedPreferences("BookSyncPref", this.PRIVATE_MODE);
        this.editor = this.pref.edit();
        try{
            mFirestore = FirebaseFirestore.getInstance();
        }catch (Exception e){
            e.printStackTrace();
        }
        }catch(Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }

    }

    public void setCustomerSyncTime(long timestamp) {
        this.editor.putLong("customer_sync_time", timestamp);
        this.editor.apply();
    }

    public long getCustomerSyncTime(){
        return this.pref.getLong("customer_sync_time", System.currentTimeMillis())- Constants.SYNC_WINDOW;
    }

    public void customerRestoreTime(long timeStamp) {
        this.editor.putLong("customer_restore_time", timeStamp);
        this.editor.apply();
    }

    public long customerRestoreTime() {
        return this.pref.getLong("customer_restore_time", 0L);
    }

    public static AppCustomerSyncManager getInstance() {
        if (appSyncManager == null) {
            appSyncManager = new AppCustomerSyncManager(Application.getAppContext());
        }
        return appSyncManager;
    }

    public void updateCustomer(CustomerEntity customerEntity){
        updateCustomer(customerEntity,false);
    }

    public void updateCustomer(CustomerEntity customerEntity,boolean setReminder){
        FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();

        // inserting customer entity data to FireStore
        try {
            customerEntity.dirty=1;
            Task<Void> task = mFirestore.collection(DATA_TABLE)
                    .document(customerEntity.customerId).set(customerEntity);
            task.addOnSuccessListener(aVoid -> {
                // inserting customer entity data to FireStore success
                FirebaseCrashlytics.getInstance().log("[FireStore][CustomerEntity]Success.ID:"+customerEntity.customerId);
            });
            task.addOnFailureListener(e -> {
                // inserting customer entity data to FireStore failed
                FirebaseCrashlytics.getInstance().log("[FireStore][CustomerEntity]Failed.ID:"+customerEntity.customerId+",Trace:"+e.toString());
            });
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),customerEntity, SyncTableEnum.CUSTOMER, customerEntity.customerId);
        }catch (Exception e){
            // inserting customer entity data to FireStore failed
            String customerId = "-";
            if (customerEntity != null) customerId = customerEntity.customerId;
            FirebaseCrashlytics.getInstance().log("[FireStore][CustomerEntity]Failed.ID:"+customerId+",Trace:"+e.toString());

            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        if(setReminder && !Utility.isBlank(customerEntity.dueDate)){
            updateReminderDate(customerEntity);
        }
    }

    private void updateReminderDate(CustomerEntity customerEntity){
        try{
            if(!Utility.isBlank(customerEntity.dueDate)) {
                String receiverPhone = customerEntity.countryCode + "" + customerEntity.phone;

                Map<String, String> reminderObj = new HashMap<>();
                reminderObj.put("receiverPhone", receiverPhone);
                reminderObj.put("bookId", User.getBusinessId());
                reminderObj.put("ownerId", User.getUserId());
                reminderObj.put("customerId", customerEntity.customerId);
                reminderObj.put("dueDate", customerEntity.dueDate);
                reminderObj.put("processed", "0");
                try{
                    BookEntity book = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
                    reminderObj.put("shop",book.businessName);
                }catch (Exception e){
                    e.printStackTrace();
                }

                if(!SessionManager.getInstance().isGuestUser()) {
                    mFirestore.collection("app_reminder_store")
                            .document(customerEntity.customerId).set(reminderObj);
                }
            }else{
                try{
                    mFirestore.collection("app_reminder_store")
                            .document(customerEntity.customerId).delete();
                }catch (Exception e){
                    e.printStackTrace();
                    FirebaseCrashlytics.getInstance().recordException(e);
                }
            }
        }catch(Exception se){
            se.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(se);
        }
    }

    public boolean backupAll(List<String> bookList){

        try {
            if (Utility.hasInternet()) {
                for(String bookId:bookList) {
                    List<CustomerEntity> customerEntities = CustomerRepository.getInstance(Application.getAppContext()).getCustomersListForContacts(bookId);
                    for (CustomerEntity customerEntity : customerEntities) {
                        mFirestore.collection(DATA_TABLE)
                                .document(customerEntity.customerId).set(customerEntity);

                    }
                }
                FeaturePrefManager.getInstance().setBackupTimestamp(System.currentTimeMillis());
            }
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }

    public boolean restoreAll(){
        try {
            mFirestore.collection(AppCustomerSyncManager.DATA_TABLE).whereEqualTo("createdByUser",User.getUserId()).get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                boolean flag = true;
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    try {
                                        CustomerEntity customerEntity = doc.toObject(CustomerEntity.class);
                                        CustomerRepository.getInstance(Application.getAppContext()).insertCustomerSync(customerEntity);

                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            }
                        }
                    }
                });
        }catch (Exception e){
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }
}
