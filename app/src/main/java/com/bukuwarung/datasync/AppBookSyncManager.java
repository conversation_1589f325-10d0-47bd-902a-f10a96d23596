package com.bukuwarung.datasync;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import androidx.annotation.NonNull;

import com.bukuwarung.Application;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.AccountingDataSyncHelper;
import com.bukuwarung.session.AccountingMigrationHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.ArrayList;
import java.util.List;

public class AppBookSyncManager {
    private static AppBookSyncManager appSyncManager;
    public static final String DATA_TABLE = "book_store";
    int PRIVATE_MODE = 0;
    private Context context;
    Editor editor;
    SharedPreferences pref;

    private FirebaseFirestore mFirestore;

    private AppBookSyncManager(Context context2) {
        try {

            this.context = context2;
            this.pref = context2.getSharedPreferences("BookSyncPref", this.PRIVATE_MODE);
            this.editor = this.pref.edit();
            mFirestore = FirebaseFirestore.getInstance();
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public static AppBookSyncManager getInstance() {
        if (appSyncManager == null) {
            appSyncManager = new AppBookSyncManager(Application.getAppContext());
        }
        return appSyncManager;
    }

    public void setBookSyncTime(long timestamp) {
        this.editor.putLong("book_sync_time", timestamp);
        this.editor.apply();
    }

    public long getBookSyncTime(){
        return this.pref.getLong("book_sync_time", System.currentTimeMillis()) - Constants.SYNC_WINDOW;
    }

    public void setBookRestoreTime(long timeStamp) {
        this.editor.putLong("book_restore_time", timeStamp);
        this.editor.apply();
    }

    public String getMigrationStatus(){
        return this.pref.getString("migration_status", Constants.MIGRATION_NOT_REQUESTED);
    }

    public void setMigrationStatus(String status) {
        this.editor.putString("migration_status", status);
        this.editor.apply();
    }

    public long getMigrationTimestamp(){
        return this.pref.getLong("migration_timestamp", 0);
    }

    public void setMigrationTimestamp(long timestamp) {
        this.editor.putLong("migration_timestamp", timestamp);
        this.editor.apply();
    }

    public long getBookRestoreTime() {
        return this.pref.getLong("book_restore_time",0l);
    }

    public void updateBook(BookEntity book) {
        FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
        try {
            book.dirty=1;
            mFirestore.collection("book_store")
                    .document(book.bookId).set(book);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),book, SyncTableEnum.BUSINESS, book.bookId);

        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public List<String> backupAll(){
        List<String> bookList = new ArrayList<>();
        try {
            if (Utility.hasInternet()) {
                List<BookEntity> bookEntities = BusinessRepository.getInstance(Application.getAppContext()).getBusinessListRaw(User.getUserId());
                for (BookEntity book : bookEntities) {
                    mFirestore.collection("book_store").document(book.bookId).set(book);
                    bookList.add(book.bookId);
                }
                FeaturePrefManager.getInstance().setBackupTimestamp(System.currentTimeMillis());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return bookList;
    }

    public boolean restoreAll(){
        try{
            mFirestore.collection("book_store").whereEqualTo("ownerId", User.getUserId())
                .get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    try {
                                        BookEntity bookEntity = doc.toObject(BookEntity.class);
                                        BusinessRepository.getInstance(Application.getAppContext()).insertBookSync(bookEntity);

                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            }
                        } else {
                            FirebaseCrashlytics.getInstance().setUserId(User.getUserId());
                            FirebaseCrashlytics.getInstance().log("restore error");
                        }
                    }
                });
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
