package com.bukuwarung.datasync;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import androidx.annotation.NonNull;

import com.bukuwarung.Application;
import com.bukuwarung.database.FirestoreDataProvider;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.AccountingDataSyncHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.List;

public class AppCustomerCategorySyncManager {
    private static AppCustomerCategorySyncManager appSyncManager;
    public static final String DATA_TABLE = "app_expense_category_store";
    int PRIVATE_MODE = 0;
    private Context context;
    Editor editor;
    SharedPreferences pref;

    private AppCustomerCategorySyncManager(Context context2) {
        try{
        this.context = context2;
        this.pref = context2.getSharedPreferences("BookSyncPref", this.PRIVATE_MODE);
        this.editor = this.pref.edit();

        }catch(Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }

    }

    public static AppCustomerCategorySyncManager getLocaleAppSyncManager(Context context2) {
        if (appSyncManager == null) {
            appSyncManager = new AppCustomerCategorySyncManager(context2);
        }
        return appSyncManager;
    }

    public static AppCustomerCategorySyncManager getInstance() {
        if (appSyncManager == null) {
            appSyncManager = new AppCustomerCategorySyncManager(Application.getAppContext());
        }
        return appSyncManager;
    }

    public void setSyncTime(long timestamp) {
        this.editor.putLong("cat_sync_time", timestamp);
        this.editor.apply();
    }

    public long getSyncTime(){
        return this.pref.getLong("cat_sync_time", System.currentTimeMillis())- Constants.SYNC_WINDOW;
    }

    public void setCustomerRestoreTime(long timestamp) {
        this.editor.putLong("cat_restore_time", timestamp);
        this.editor.apply();
    }

    public long getCustomerRestoreTime() {
        return this.pref.getLong("cat_restore_time", 0L);
    }

    public long getSyncSeq() {
        return this.pref.getLong("cashSyncSeq", 0);
    }

    public void setSyncSeq(long i) {
        this.editor.putLong("cashSyncSeq", i);
        this.editor.apply();
    }

    public void updateCashCategory(CashCategoryEntity categoryEntity){
        FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
        categoryEntity.serverSeq = getSyncSeq();
        setSyncSeq(categoryEntity.serverSeq+1);
        try {
            categoryEntity.dirty=1;
//            if(categoryEntity.cashCategoryId.contains("/")){
//                categoryEntity.cashCategoryId = categoryEntity.cashCategoryId.replace("/","_");
//            }

            String documentId = categoryEntity.cashCategoryId.replace("/","_");

            Task<Void> task = mFirestore.collection(DATA_TABLE)
                    .document(documentId).set(categoryEntity);
            task.addOnSuccessListener(aVoid -> {
               // inserting cash category into firestore success
               FirebaseCrashlytics.getInstance().log("[FireStore][CashCategoryEntity]Success.ID:"+categoryEntity.cashCategoryId);
            });
            task.addOnFailureListener(e -> {
               // inserting cash category into firestore fails
               FirebaseCrashlytics.getInstance().log("[FireStore][CashCategoryEntity]Failed.ID:"+categoryEntity.cashCategoryId+",Trace:"+e.getLocalizedMessage());
            });
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),categoryEntity, SyncTableEnum.CASH_TRANSACTION_CATEGORY,categoryEntity.cashCategoryId);
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            // inserting cash category into firestore fails
            FirebaseCrashlytics.getInstance().log("[FireStore][CashCategoryEntity]Failed.ID:"+categoryEntity.cashCategoryId+",Trace:"+e.getLocalizedMessage());
        }
    }

    public boolean backupAll(List<String> bookList){

        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            if (Utility.hasInternet()) {
                for(String bookId:bookList) {
                    List<CashCategoryEntity> cashCategoryEntities = CashRepository.getInstance(Application.getAppContext()).getAllCategoryList(bookId);
                    for (CashCategoryEntity cashCategoryEntity : cashCategoryEntities) {
                        mFirestore.collection(DATA_TABLE)
                                .document(cashCategoryEntity.cashCategoryId).set(cashCategoryEntity);

                    }
                }
                FeaturePrefManager.getInstance().setBackupTimestamp(System.currentTimeMillis());
            }
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }

    public void restoreAll() {
        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(DATA_TABLE).whereEqualTo("createdByUser",User.getUserId()).get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                boolean flag = true;
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    try {
                                        CashCategoryEntity cashCategoryEntity = doc.toObject(CashCategoryEntity.class);
                                        if (cashCategoryEntity.deleted == 0) {
                                            CashRepository.getInstance(Application.getAppContext()).insertCashCategorySync(cashCategoryEntity);
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            }
                        }
                    }
                });
        }catch (Exception e){
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
