package com.bukuwarung.datasync;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import androidx.annotation.NonNull;

import com.bukuwarung.Application;
import com.bukuwarung.database.FirestoreDataProvider;
import com.bukuwarung.database.dto.TransactionItemListDto;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.ProductEntity;
import com.bukuwarung.database.entity.TransactionItemsEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.AccountingDataSyncHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.LoginUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.List;

public class AppExpenseTransSyncManager {
    private static AppExpenseTransSyncManager appSyncManager;
    public static final String DATA_TABLE = "app_expense_transaction_store";
    public static final String TXN_ITEM_TABLE = "app_transaction_item_store";
    public static final String PRODUCT_DATA_TABLE = "app_user_product_store";
    public static final String INVENTORY_HISTORY_DATA_TABLE = "app_inventory_history_store";

    private final String SYNC_TABLE = "trans_sync_seq";
    int PRIVATE_MODE = 0;
    private Context context;
    Editor editor;
    SharedPreferences pref;

    private FirestoreDataProvider mDataProvider;
    private FirebaseFirestore mFirestore;
    private Query mQuery;

    private AppExpenseTransSyncManager(Context context2) {
        this.context = context2;
        this.pref = context2.getSharedPreferences("BookSyncPref", this.PRIVATE_MODE);
        this.editor = this.pref.edit();
            try{
                mFirestore = FirebaseFirestore.getInstance();
            }catch (Exception e){
                e.printStackTrace();
            }

    }

    public static AppExpenseTransSyncManager getLocaleAppSyncManager(Context context2) {
        if (appSyncManager == null) {
            appSyncManager = new AppExpenseTransSyncManager(context2);
        }
        return appSyncManager;
    }

    public static AppExpenseTransSyncManager getInstance() {
        if (appSyncManager == null) {
            appSyncManager = new AppExpenseTransSyncManager(Application.getAppContext());
        }
        return appSyncManager;
    }

    public void setSyncTime(long timestamp) {
        this.editor.putLong("cash_trans_sync_time", timestamp);
        this.editor.apply();
    }

    public long getSyncTime(){
        return this.pref.getLong("cash_trans_sync_time", System.currentTimeMillis())- Constants.SYNC_WINDOW;
    }

    public void setExpenseRestoreTime(long timestamp) {
        this.editor.putLong("cash_trans_restore_time", timestamp);
        this.editor.apply();
    }

    public long getExpenseRestoreTime() {
        return this.pref.getLong("cash_trans_restore_time", 0L);
    }

    public long getSyncSeq() {
        return this.pref.getLong("cashTransSyncSeq", 0);
    }

    public void setSyncSeq(long i) {
        this.editor.putLong("cashTransSyncSeq", i);
        this.editor.apply();
    }

    public void updateExpenseTransaction(CashTransactionEntity transactionEntity) {
        FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
        try {
            transactionEntity.dirty = 1;
            if (Utility.hasInternet()) {
                transactionEntity.isOffline = 0;
            } else {
                transactionEntity.isOffline = 1;
            }
            transactionEntity.updatedByDevice = LoginUtils.getDevice();
            if(!Utility.isBlank(transactionEntity.orderId) && !transactionEntity.orderId.equals(transactionEntity.cashTransactionId)){
                CashTransactionEntity txn = CashRepository.getInstance(Application.getAppContext()).getSingleRawCashTransaction(transactionEntity.cashTransactionId);
                transactionEntity.cashTransactionId=transactionEntity.orderId;
                CashRepository.getInstance(Application.getAppContext()).insertCashTransaction(transactionEntity);
            }
            Task<Void> task = mFirestore.collection(DATA_TABLE).document(transactionEntity.cashTransactionId).set(transactionEntity);
            task.addOnSuccessListener(aVoid -> {
                // inserting cash transaction into SQLite success
                FirebaseCrashlytics.getInstance().log("[FireStore][CashTransactionEntity]Success.ID:" + transactionEntity.cashTransactionId);
            });
            task.addOnFailureListener(e -> {
                // inserting cash transaction into FireStore fails
                FirebaseCrashlytics.getInstance().log("[FireStore][CashTransactionEntity]Failed.ID:" + transactionEntity.cashTransactionId + ",Trace:" + e.getLocalizedMessage());
            });

            CashCategoryEntity cashCategoryEntity = null;
            cashCategoryEntity = CashRepository.getInstance(Application.getAppContext()).getCashCategoryById(transactionEntity.cashCategoryId);
            AppCustomerCategorySyncManager.getInstance().updateCashCategory(cashCategoryEntity);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),transactionEntity, SyncTableEnum.CASH_TRANSACTION, transactionEntity.cashTransactionId);
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            // inserting cash transaction into FireStore fails
            FirebaseCrashlytics.getInstance().log("[FireStore][CashTransactionEntity]Failed.ID:" + transactionEntity.cashTransactionId + ",Trace:" + e.getLocalizedMessage());
        }
    }

    public boolean backupAll(List<String> bookList){

        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            if (Utility.hasInternet()) {
                for(String bookId:bookList) {
                    List<CashTransactionEntity> cashTransactionEntities = CashRepository.getInstance(Application.getAppContext()).getAllCashTransactionsSync(bookId);
                    for (CashTransactionEntity cashTransactionEntity : cashTransactionEntities) {
                        mFirestore.collection(DATA_TABLE)
                                .document(cashTransactionEntity.cashTransactionId).set(cashTransactionEntity);

                    }
                }
                FeaturePrefManager.getInstance().setBackupTimestamp(System.currentTimeMillis());
            }
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }

    public void restoreAll() {
        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(DATA_TABLE).whereEqualTo("createdByUser", User.getUserId()).get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                boolean flag = true;
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    try {
                                        CashTransactionEntity cashTransactionEntity = doc.toObject(CashTransactionEntity.class);
                                        TransactionRepository.getInstance(Application.getAppContext()).insertCashTransactionSync(cashTransactionEntity);
//                                        restoreTransactionItemData(cashTransactionEntity.cashTransactionId);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            }
                        }
                    }
                });
        }catch (Exception e){
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void updateTransactionItems(List<TransactionItemsEntity> transactionItemsEntityList) {
        try {
            if(ListUtils.isEmpty(transactionItemsEntityList))
                return;
            TransactionItemListDto transactionItemListDto = new TransactionItemListDto();
            transactionItemListDto.itemList = transactionItemsEntityList;
            transactionItemListDto.createdByUser = User.getUserId();
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(TXN_ITEM_TABLE)
                    .document(transactionItemsEntityList.get(0).transactionId).set(transactionItemListDto);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),transactionItemListDto, SyncTableEnum.TRANSACTION_ITEMS, transactionItemsEntityList.get(0).transactionId);
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public void updateUserProducts(ProductEntity productEntity) {
        if (productEntity == null)
            return;
        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(PRODUCT_DATA_TABLE)
                    .document(productEntity.productId).set(productEntity);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),productEntity,SyncTableEnum.PRODUCT,productEntity.productId);
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public void updateInventoryHistory(InventoryHistoryEntity historyEntity) {
        try {
            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(INVENTORY_HISTORY_DATA_TABLE)
                    .document(historyEntity.historyId).set(historyEntity);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),historyEntity,SyncTableEnum.INVENTORY_HISTORY,historyEntity.historyId);
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public void deleteTransactionItems(String transactionId) {
        try {
            if(Utility.isBlank(transactionId))
                return;

            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(TXN_ITEM_TABLE)
                    .document(transactionId).delete();
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public void deleteInventoryHistoryItems(String historyId) {
        try {
            if(Utility.isBlank(historyId))
                return;

            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection(INVENTORY_HISTORY_DATA_TABLE)
                    .document(historyId).delete();
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
