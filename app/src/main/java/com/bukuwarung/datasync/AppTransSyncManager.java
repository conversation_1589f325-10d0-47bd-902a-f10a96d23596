package com.bukuwarung.datasync;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import androidx.annotation.NonNull;

import com.bukuwarung.Application;
import com.bukuwarung.database.FirestoreDataProvider;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.AccountingDataSyncHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.List;

public class AppTransSyncManager {
    private static AppTransSyncManager appSyncManager;
    public static final String DATA_TABLE = "transaction_store";
    private final String SYNC_TABLE = "trans_sync_seq";
    int PRIVATE_MODE = 0;
    private Context context;
    Editor editor;
    SharedPreferences pref;

    private FirestoreDataProvider mDataProvider;
    private FirebaseFirestore mFirestore;
    private Query mQuery;

    private AppTransSyncManager(Context context2) {
        try{
        this.context = context2;
        this.pref = context2.getSharedPreferences("BookSyncPref", this.PRIVATE_MODE);
        this.editor = this.pref.edit();
        try{
            mFirestore = FirebaseFirestore.getInstance();
        }catch (Exception e){
            e.printStackTrace();
        }

        }catch(Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }

    }

    public void setTransSyncTime(long timestamp) {
        this.editor.putLong("trans_sync_time", timestamp);
        this.editor.apply();
    }

    public long getTransSyncTime(){
        return this.pref.getLong("trans_sync_time", System.currentTimeMillis())- Constants.SYNC_WINDOW;
    }

    public void setTransactionRestoreTime(long timestamp) {
        this.editor.putLong("trans_restore_time", timestamp);
        this.editor.apply();
    }

    public long getTransactionRestoreTime() {
        return this.pref.getLong("trans_restore_time", 0L);
    }

    public static AppTransSyncManager getInstance() {
        if (appSyncManager == null) {
            appSyncManager = new AppTransSyncManager(Application.getAppContext());
        }
        return appSyncManager;
    }

    public void updateTransaction(TransactionEntity transactionEntity){
        FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
        try {
            transactionEntity.dirty=1;
            if (Utility.hasInternet()) {
                transactionEntity.isOffline = 0;
            } else {
                transactionEntity.isOffline = 1;
            }
            Task<Void> task = mFirestore.collection(DATA_TABLE)
                    .document(transactionEntity.transactionId).set(transactionEntity);
            task.addOnSuccessListener(aVoid -> {
                // inserting utang piutang entity data to FireStore success
                FirebaseCrashlytics.getInstance().log("[FireStore][UtangPiutang]Success.ID:"+transactionEntity.transactionId);
            });
            task.addOnFailureListener(e -> {
                // inserting utang piutang entity data to FireStore fails
                FirebaseCrashlytics.getInstance().log("[FireStore][UtangPiutang]Failed.ID:"+transactionEntity.transactionId+",Trace:"+e.toString());
            });

            CustomerEntity customerEntity = CustomerRepository.getInstance(Application.getAppContext()).getCustomerById(transactionEntity.customerId);
            AppCustomerSyncManager.getInstance().updateCustomer(customerEntity);
            AccountingDataSyncHelper.Companion.updateEntity(Application.getAppContext(),transactionEntity, SyncTableEnum.CUSTOMER_TRANSACTION, transactionEntity.transactionId);
        }catch (Exception e){
            // inserting utang piutang entity data to FireStore fails

            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            String transactionId = "-";
            if (transactionEntity != null) transactionId = transactionEntity.customerId;
            FirebaseCrashlytics.getInstance().log("[FireStore][UtangPiutang]Failed.ID:"+transactionId+",Trace:"+e.toString());
        }
    }

    public boolean backupAll(List<String> bookList){

        try {
            if (Utility.hasInternet()) {
                for(String bookId:bookList) {
                    List<TransactionEntity> transactionEntities = TransactionRepository.getInstance(Application.getAppContext()).getAllCustomerTransactionsForBookSync(bookId);
                    for (TransactionEntity transactionEntity : transactionEntities) {
                        mFirestore.collection(DATA_TABLE)
                                .document(transactionEntity.transactionId).set(transactionEntity);

                    }
                }
                FeaturePrefManager.getInstance().setBackupTimestamp(System.currentTimeMillis());
            }
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }

    public boolean restoreAll() {
        try {
            mFirestore.collection(AppTransSyncManager.DATA_TABLE).whereEqualTo("createdByUser",User.getUserId()).get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                for (QueryDocumentSnapshot doc : task.getResult()) {

                                    try {
                                        TransactionEntity transactionEntity = doc.toObject(TransactionEntity.class);
                                        if (transactionEntity.deleted == 0) {
                                            TransactionRepository.getInstance(Application.getAppContext()).insertTransactionSync(transactionEntity);
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            }
                        }
                    }
                });
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }
}
