package com.bukuwarung.domain.cash

import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.constants.AppConst.DEBIT
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.CashCategoryEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.product.ProductUseCase
import com.bukuwarung.domain.transaction.TransactionUseCase
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.TransactionConfigUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.extensions.toCustomerEntity
import com.bukuwarung.utils.getFormattedId
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isNotNullOrEmpty


class CashUseCase(
        private val repository: CashRepository,
        private val transactionUseCase: TransactionUseCase,
        private val customerUseCase: CustomerUseCase,
        private val productUseCase: ProductUseCase,
        private val transactionConfigUseCase: TransactionConfigUseCase,
        private val sessionManager: SessionManager
) {

    fun getSingleRawCashTransaction(cashTransactionId: String?): CashTransactionEntity? {
        return repository.getSingleRawCashTransaction(cashTransactionId)
    }

    fun getCashCategoryById(categoryId: String?): CashCategoryEntity? {
        return repository.getCashCategoryById(categoryId)
    }


    fun getUniqueCategoryEntries(categoryType: Int): List<CashCategoryEntity>? {
        return repository.getUniqueCategoryEntries(categoryType)
    }
    fun getCategoryByFrequency(categoryType: Int): List<CashCategoryEntity>? {
        return repository.getCategoryByFrequency(categoryType)
    }

    fun mergeGuestRecords() {
        return repository.mergeGuestRecords()
    }

    fun saveNewCashCategory(categoryId: String, name: String, amount: Double, type: Int,freqency:Int): String {
        return repository.saveNewCashCategory(sessionManager.userId, sessionManager.deviceId, sessionManager.businessId, categoryId, name, amount, type,freqency)
    }

    fun updateNewCashCategory(categoryId: String, name: String, amount: Double, type: Int,freqency:Int): String {
        return repository.updateCashCategoryEntity(sessionManager.userId, sessionManager.deviceId, sessionManager.businessId, categoryId, name, amount, type,freqency)
    }

    /**
     * refactored methods
     * */
    fun createCashTransaction(dto: CashDto, categorySelectedStatus: Int, attachment: String?,
                              trxType: Int = DEBIT):
            String {
        // will check and use selected category if exist, otherwise will create a new one and use its id
        val categoryId = ensureCategoryId(dto.category, dto.amount, trxType)

        var customerId: String? = null
        val customerTransactionId = if (dto.contact?.name.isNotNullOrEmpty() &&
            (dto.status == BELUM_LUNAS || categorySelectedStatus > 0)) {
                customerId = dto.contact?.customerId.takeIf { it.isNotNullOrBlank() }
                    ?: customerUseCase.saveContactAsCustomer(dto.contact)

            // create customerTransaction for selected contact/customer
            if (categorySelectedStatus == 1 || categorySelectedStatus == 2) {
                transactionUseCase.saveCustomerTransactionForCash(
                    customerId,
                    dto.amount,
                    dto.transactionDate,
                    dto.note,
                    attachment
                )
            } else {
                transactionUseCase.saveCustomerTransactionForCash(
                    customerId,
                    -dto.amount,
                    dto.transactionDate,
                    dto.note,
                    attachment
                )
            }
        } else {
            // if status is LUNAS then there will be no CustomerTransaction created
            null
        }

        // create new cashTransaction with selected customer (if any)
        val customerEntity = dto.contact.toCustomerEntity(customerId)
        val newCashTrx = saveNewCashTransaction(
            categoryId,
            dto.amount,
            dto.buyingPrice,
            dto.transactionDate,
            dto.note,
            customerTransactionId,
            customerEntity,
            dto.status,
            dto.customAmount,
            attachment,
            dto.paymentMethod,
            dto.transactingProducts
            )

        saveCashProduct(newCashTrx, dto.amount, dto.transactingProducts, trxType)

        return newCashTrx
    }

    fun updateCashTransaction(dto: CashDto, categorySelectedStatus: Int, attachment: String?, trxType: Int) {
        val cashEntity = dto.currentCashTransaction ?: return

        var customerId: String? = null

        if (categorySelectedStatus > 0) {
            customerId = dto.contact?.customerId.takeIf { it.isNotNullOrBlank() }
                    ?: customerUseCase.saveContactAsCustomer(dto.contact)

            transactionUseCase.saveCustomerTransactionForCash(customerId, dto.amount, dto.transactionDate, dto.note, attachment)
        }

        val customerTransactionId = when (dto.status) {
            BELUM_LUNAS -> {
                // previous BELUM LUNAS transaction must have TransactionEntity
                if (cashEntity.status == BELUM_LUNAS && categorySelectedStatus <= 0) { // previous trx has customerTransaction connected to it
                    val customerTransaction = transactionUseCase.getCustomerTransactionById(cashEntity.customerTransactionId
                            ?: "")
                    when (cashEntity.customerId) {
                        dto.contact?.customerId -> { // contact did not change
                            customerTransaction?.let {
                                transactionUseCase.deleteCustomerTransactionById(it)
                            }
                            transactionUseCase.saveCustomerTransactionForCash(dto.contact?.customerId, -dto.amount, dto.transactionDate, dto.note, attachment)
                            customerId = cashEntity.customerId
                            cashEntity.customerTransactionId // use customerTransactionId from previous transaction
                        }
                        else -> {
                            // contact changed, check if it is existing or non-existing customer
                            customerId = dto.contact?.customerId.takeIf { it.isNotNullOrBlank() }
                                    ?: customerUseCase.saveContactAsCustomer(dto.contact)

                            customerTransaction?.let {
                                transactionUseCase.deleteCustomerTransactionById(it)
                            }
                            transactionUseCase.saveCustomerTransactionForCash(customerId, -dto.amount, dto.transactionDate, dto.note, attachment)
                        }
                    }
                } else {
                    // contact changed, check if it is existing or non-existing customer
                    customerId = dto.contact?.customerId.takeIf { it.isNotNullOrBlank() }
                            ?: customerUseCase.saveContactAsCustomer(dto.contact)

                    transactionUseCase.saveCustomerTransactionForCash(customerId, -dto.amount, dto.transactionDate, dto.note, attachment)
                }
            }
            else -> {
                // switching BELUM LUNAS to LUNAS trx
                if (cashEntity.status == BELUM_LUNAS) {
                    transactionUseCase.getCustomerTransactionById(cashEntity.customerTransactionId?:"")?.also {
                        transactionUseCase.deleteCustomerTransactionById(it)
                    }
                    customerId = dto.contact?.customerId
                }
                null
            }
        }

        val newCategoryId = cashEntity.cashCategoryId.takeIf { dto.category.getIdString()?.let { id -> it.contains(id) } == true || PaymentUtils.isPpob(it) }
                ?: ensureCategoryId(dto.category, dto.amount, trxType)

        saveCashProduct(cashEntity.cashTransactionId, dto.amount, dto.transactingProducts, trxType)
        val customerEntity = dto.contact.toCustomerEntity(customerId)
        updateCashTransaction(cashEntity.cashTransactionId, newCategoryId, dto.amount, dto.buyingPrice, dto.transactionDate, dto.note, customerTransactionId, customerEntity, dto.status, attachment)
    }


    private fun saveNewCashTransaction(categoryId: String,
                                       amount: Double,
                                       buyingPrice: Double,
                                       date: String?, note: String,
                                       customerTransactionId: String?,
                                       customerEntity: CustomerEntity,
                                       status: Int,
                                       customAmount: Double = 0.0,
                                       attachment: String?,
                                       paymentMethod: String,
                                       transactingProducts : List<TransactionItemDto>
                                       ): String {
        // For creating a new POS transaction
        return if ( customAmount > 0 || categoryId.contains("pos::")) {
            repository.saveNewCashTransactionForPos(
                categoryId,
                amount,
                customAmount,
                buyingPrice,
                date,
                note,
                customerTransactionId,
                customerEntity,
                status,
                paymentMethod)
        } else {
            repository.saveNewCashTransaction(categoryId, amount, buyingPrice, date, note, customerTransactionId, customerEntity, status, attachment, paymentMethod)
        }
    }

    private fun updateCashTransaction(cashTrxId: String, categoryId: String, amount: Double, buyingPrice: Double, date: String?, note: String, customerTransactionId: String?, customerEntity: CustomerEntity, status: Int, attachment: String?): Int {
        return repository.updateCashTransaction(cashTrxId, categoryId, amount, date, note, 0, buyingPrice, customerTransactionId, status, customerEntity, attachment)
    }

    fun getCashTrxCountForCurrentBusinessId(): Int {
        return repository.getCountCashTransaction(sessionManager.businessId)
    }

    fun getCashCategoryTrxCountForCurrentBusinessId(categoryId: String): Int {
        return repository.getCountCashCategoryTransaction(sessionManager.businessId, categoryId)
    }

    /**
     * Category gets stored in db after a transaction, categoryId consists of defaultCategoryId(cashIn,cashOut ..) + delimiter (::) + bookId
     * check if category exists in database by generating categoryId = defaultCategoryId+ delimiter (::) + bookId
     * if category doesn't exist in database, create new category with given balance
     * @param category plain category object with default category id // non uuid category id
     * @param amount initial balance for the category
     * @return categoryId newly generated or existing category
     */
    public fun ensureCategoryId(category: Category, amount: Double, trxType: Int): String {
        val formattedCategoryId = category.getFormattedId()
        val currentCategory :CashCategoryEntity? = getCashCategoryById(formattedCategoryId)
        var frequency = if (currentCategory?.frequency != null) {
            currentCategory.frequency + 1
        } else 1

        updateNewCashCategory(formattedCategoryId, category.name.orEmpty(), amount, trxType ,frequency)
        return currentCategory?.cashCategoryId
                ?: saveNewCashCategory(formattedCategoryId, category.name.orEmpty(), amount, trxType, frequency)
    }

    /**
     * this method can be used to create or update existing product list of a CashTransaction
     * */
    private fun saveCashProduct(cashTrxId: String, trxAmount: Double, products:
    List<TransactionItemDto>, trxType: Int = DEBIT) {
        //delete old history
        transactionUseCase.deleteAllHistoryItemsForTransaction(cashTrxId)
        //delete old transaction items if any
        transactionUseCase.deleteAllItemsForTransaction(cashTrxId)

        if (products.isNotEmpty()) {
            val transactionItemsEntityList = EntityHelper.completeTransactionItemsList(cashTrxId, products)
            //insert new transaction items
            transactionConfigUseCase.increaseProductTransactionCount()
            productUseCase.updateProductInventoryAfterTransaction(transactionItemsEntityList, trxType, products)
            transactionUseCase.insertTransactionItems(transactionItemsEntityList)
        }
    }

    companion object {
        private const val BELUM_LUNAS = 0
        private const val LUNAS = 1
    }
}
