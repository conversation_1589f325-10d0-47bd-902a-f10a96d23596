package com.bukuwarung.utils

import android.app.Activity
import android.app.ActivityManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.content.res.Resources
import android.gesture.GestureOverlayView
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.view.ViewCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.referral.share.ReferralUploadReceiver
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.PRODUCTION_FLAVOR
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CashCategoryEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionItemsEntity
import com.bukuwarung.databinding.LayoutActivityTitleBinding
import com.bukuwarung.datasync.model.AppExpenseCategoryResponseListItem
import com.bukuwarung.datasync.model.AppExpenseTransactionResponseListItem
import com.bukuwarung.datasync.model.AppTransactionItemResponseListItem
import com.bukuwarung.datasync.model.BookResponseListItem
import com.bukuwarung.datasync.model.CustomerResponseListItem
import com.bukuwarung.datasync.model.TransactionResponseListItem
import com.bukuwarung.enums.Language
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.tutor.utils.Utils.dpToPx
import com.bukuwarung.wrapper.EventWrapper
import com.bumptech.glide.Glide
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputLayout
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import io.reactivex.Single
import io.reactivex.SingleEmitter
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.reflect.Type
import java.math.BigDecimal
import java.math.RoundingMode
import java.net.URL
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.math.abs

fun TextView.setTextOrDefault(newText: String?) {
    try {
        text = if (newText.isNullOrBlank()) {
            context.resources.getString(R.string.default_placeholder)
        } else {
            newText
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun TextView.setDrawable(left: Int = 0, right: Int = 0, top: Int = 0, bottom: Int = 0) {
    this.setCompoundDrawablesWithIntrinsicBounds(
            left,
            top,
            right,
            bottom
    )
}

fun TextView.clearDrawables() {
    this.setDrawable()
}

fun TextView.clearDrawableTint() {
    compoundDrawables.forEach { it?.setTintList(null) }
    compoundDrawablesRelative.forEach { it?.setTintList(null) }
}

fun TextView.setOvalColor(initial: String?) {
    val color = when (initial?.get(0)?.toInt()?.rem(7)) {
        0 -> Color.parseColor("#66bdff")
        1 -> Color.parseColor("#33a7ff")
        2 -> Color.parseColor("#03A9F4")
        3 -> Color.parseColor("#0084ff")
        4 -> Color.parseColor("#0077ff")
        5 -> Color.parseColor("#006bff")
        else -> Color.parseColor("#005eff")
    }

    DrawableCompat.setTint(background, color)
}
fun TextView.textHTML(html: String?) {
    html ?: return
    text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT)
    } else {
        Html.fromHtml(html)
    }
}
fun MaterialButton.setStyleButtonFill(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.new_yellow))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, R.color.black_80))
}

fun MaterialButton.setStyleButtonFillBlue(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.blue_80))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, R.color.white))
}

fun MaterialButton.setStyleButtonFillRed(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.red_60))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, R.color.white))
}

fun MaterialButton.setStyleButtonColorFill(
    context: Context?,
    backgroundColor: Int,
    textColor: Int
) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, backgroundColor))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, textColor))
}

fun MaterialButton.setStyleButtonOutline(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, android.R.color.transparent))
    strokeColor = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.new_yellow))
    strokeWidth = dpToPx(1)
    setTextColor(ContextCompat.getColor(context, R.color.new_yellow))
}

fun MaterialButton.setStyleButtonOutlinePrimary(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.blue_5))
    strokeColor = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.blue_60))
    strokeWidth = dpToPx(1)
    setTextColor(ContextCompat.getColor(context, R.color.blue_60))
}


fun MaterialButton.setStyleButtonOutlineBlue(context: Context?) {
    context?.let {
        backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(it, R.color.white))
        strokeColor = ColorStateList.valueOf(it.getColorCompat(R.color.blue_80))
        strokeWidth = dpToPx(2)
        setTextColor(ContextCompat.getColor(it, R.color.blue_80))
    }
}

fun MaterialButton.setStyleButtonOutlineRed(context: Context?) {
    context?.let {
        backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(it, R.color.transparent))
        strokeColor = ColorStateList.valueOf(it.getColorCompat(R.color.red_80))
        strokeWidth = dpToPx(2)
        setTextColor(ContextCompat.getColor(it, R.color.red_80))
    }
}

fun MaterialButton.makeButtonInactive(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.black_20))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, R.color.white))
}

fun View.setHorizontalMargin(start: Int, end: Int) {
    if (this.layoutParams is ViewGroup.MarginLayoutParams) {
        val p = (this.layoutParams as ViewGroup.MarginLayoutParams).apply {
            marginStart = start.dp
            marginEnd = end.dp
        }
        this.layoutParams = p
        this.requestLayout()
    }
}

fun View.reverseVisibility() {
    this.apply {
        visibility = if (visibility == View.VISIBLE) View.GONE else View.VISIBLE
    }
}

val Int.dp: Int
    get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

val Int.sp: Int
    get() = (this * Resources.getSystem().displayMetrics.scaledDensity).toInt()

fun String?.getFirstLetter(): String? {
    this ?: return this
    return this[0].toString()
}

fun String?.getFirstTwoLetter(): String? {
    this ?: return this

    try {
        val splitted = this.split(" ")
        var ret = ""
        splitted.forEach {
            ret += it[0]
            if (ret.length >= 2) return@forEach // we got enough
        }
        return ret
    } catch (e: Exception) {
        e.printStackTrace()
        return this
    }
}

fun Boolean?.asVisibility(): Int {
    val converted = this ?: false
    return if (converted) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

fun ImageView.loadExternalBitmap(imageUrl: String?) {
    if (imageUrl.isNullOrEmpty()) return
    val disposable = Single.create<Bitmap> { emitter: SingleEmitter<Bitmap> ->
        val url = URL(imageUrl)
        val bitmap = BitmapFactory.decodeStream(url.openConnection().getInputStream())
        emitter.onSuccessWithCheck(bitmap)
    }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .subscribe(
                    {
                        it?.let {
                            try {
                                this.setImageBitmap(it)
                                this.visibility = View.VISIBLE
                                this.scaleType = ImageView.ScaleType.CENTER_CROP
                            } catch (ex: java.lang.Exception) {
                                ex.printStackTrace();
                            }
                        }
                    },
                    {
                        Log.e("ImageViewLoader", "Image View Fails to load", it)
                    }
            )
}

fun String.parseToZeroHourDate(): Calendar? {
    return try {
        val date = DateTimeUtils.convertToDateYYYYMMDD(this)
        val actualDate = Calendar.getInstance()
        actualDate.time = date
        actualDate.set(Calendar.HOUR_OF_DAY, 0)
        actualDate.set(Calendar.MINUTE, 0)
        actualDate.set(Calendar.SECOND, 0)
        actualDate.set(Calendar.MILLISECOND, 0)
        actualDate
    } catch (ex: Exception) {
        Log.e("Exception", "Exception when parsing date", ex)
        null
    }
}

fun Calendar?.beforeToday(): Boolean {
    return try {
        val todayDate = Calendar.getInstance()
        todayDate.set(Calendar.HOUR_OF_DAY, 0)
        todayDate.set(Calendar.MINUTE, 0)
        todayDate.set(Calendar.SECOND, 0)
        todayDate.set(Calendar.MILLISECOND, 0)
        this?.before(todayDate) ?: false
    } catch (ex: Exception) {
        false
    }
}


fun Calendar?.isToday(): Boolean {
    return try {
        val todayDate = Calendar.getInstance()
        todayDate.set(Calendar.HOUR_OF_DAY, 0)
        todayDate.set(Calendar.MINUTE, 0)
        todayDate.set(Calendar.SECOND, 0)
        todayDate.set(Calendar.MILLISECOND, 0)
        this == todayDate
    } catch (ex: Exception) {
        false
    }
}

fun Calendar?.afterToday(): Boolean {
    return try {
        val todayDate = Calendar.getInstance()
        todayDate.set(Calendar.HOUR_OF_DAY, 0)
        todayDate.set(Calendar.MINUTE, 0)
        todayDate.set(Calendar.SECOND, 0)
        todayDate.set(Calendar.MILLISECOND, 0)
        this?.after(todayDate) ?: false
    } catch (ex: Exception) {
        false
    }
}

fun Calendar.getDateZeroHour(): Date {
    this.set(Calendar.HOUR_OF_DAY, 0)
    this.set(Calendar.MINUTE, 0)
    this.set(Calendar.SECOND, 0)
    this.set(Calendar.MILLISECOND, 0)
    return this.time
}

fun Long?.secondsToMilliSeconds(): Long = this.orNil*1000

fun Long.milliSecondsToSeconds(): Long = this/1000

fun Double.milliSecondsToSeconds(): Double = this/1000

fun Boolean.asVisibility(): Int {
    return if (this) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

fun Boolean.visibleIfTrue() = if (this) View.VISIBLE else View.INVISIBLE

fun List<View>.hideViews() {
    this.forEach {
        it.visibility = View.GONE
    }
}

fun List<View>.showViews() {
    this.forEach {
        it.visibility = View.GONE
    }
}

fun RecyclerView.smoothScrollToPosition(context: Context, position: Int) {
    try {
        val layoutManager = this.layoutManager as LinearLayoutManager
        val smoothScroller: RecyclerView.SmoothScroller = object : LinearSmoothScroller(context) {
            override fun getVerticalSnapPreference(): Int {
                return SNAP_TO_START
            }
        }
        smoothScroller.targetPosition = position
        layoutManager.startSmoothScroll(smoothScroller)
    } catch (ex: Exception) {
        Log.e("RecyclerView", "Exception", ex)
    }
}

fun <T : RecyclerView.ViewHolder> T.listen(event: (position: Int, type: Int) -> Unit): T {
    itemView.setSingleClickListener {
        event.invoke(adapterPosition, itemViewType)
    }
    return this
}

fun <T> SingleEmitter<T>.onSuccessWithCheck(result: T) {
    try {
        if (!isDisposed) onSuccess(result)
    } catch (ex: Exception) {
        Log.e("RxException", "Exception", ex)
    }
}

fun <T> SingleEmitter<T>.onErrorWithCheck(exception: Exception) {
    try {
        if (!isDisposed) onError(exception)
    } catch (ex: Exception) {
        Log.e("RxException", "Exception", ex)
    }
}

val Boolean?.isTrue
    get() = (this == true)

val Boolean?.isFalse
    get() = this == false

val Boolean?.isFalseOrNull
    get() = this == false || this == null

val Double?.orNil
    get() = (this ?: 0.0)

val Int?.orNil
    get() = (this ?: 0)

val Long?.orNil
    get() = (this ?: 0L)

val BigDecimal?.orNil: BigDecimal
    get() = (this ?: BigDecimal.ZERO)

fun String?.getPaymentSharingText(customerEntity: CustomerEntity?): String? {
    val dueDate = customerEntity?.formattedDueDate

    return if (dueDate.isNullOrBlank()) {
        this?.replace("Jatuh tempo: {due_date}\n", "")
    } else {
        this?.replace("{due_date}", dueDate)
    }
}

fun Any.getClassTag(): String = this.javaClass.simpleName

// be careful using this as in recyclerviewadapter as it will always add new listener
fun EditText.afterTextChanged(action: (String) -> Unit) {
    val textWatcher = object : TextWatcher {
        override fun afterTextChanged(editable: Editable?) {
            action(editable.toString())
        }

        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        }

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        }

    }

    addTextChangedListener(textWatcher)
}

inline fun <T> LifecycleOwner.subscribeSingleLiveEvent(liveData: LiveData<EventWrapper<T>>, crossinline onEventUnhandled: (T) -> Unit) {
    liveData.observe(this, Observer { it?.getEventIfNotHandled()?.let(onEventUnhandled) })
}

fun Context.getColorCompat(colorRes: Int): Int {
    return ContextCompat.getColor(this, colorRes)
}

fun Context.getDrawableCompat(drawableRes: Int): Drawable? {
    return ContextCompat.getDrawable(this, drawableRes)
}

fun Context.isValidGlideContext() = this !is Activity || (!this.isDestroyed && !this.isFinishing)

fun Exception.recordException() {
    if (BuildConfig.DEBUG) printStackTrace()
    if (BuildConfig.FLAVOR == PRODUCTION_FLAVOR) {
        FirebaseCrashlytics.getInstance().recordException(this)
    }
}

fun Exception.recordException(msg: String) {
    if (BuildConfig.DEBUG) printStackTrace()
    if (BuildConfig.FLAVOR == PRODUCTION_FLAVOR) {
        FirebaseCrashlytics.getInstance().log(msg)
    }
}

suspend fun <T> Task<T>.await(): T? {
    if (isComplete) {
        val e = exception
        return if (e == null) {
            if (isCanceled) {
                throw CancellationException("Task $this was cancelled normally.")
            } else {
                result
            }
        } else {
            throw e
        }
    }

    return suspendCancellableCoroutine { cont ->
        addOnCompleteListener {
            val e = exception
            if (e == null) {
                if (isCanceled) cont.cancel() else cont.resume(result)
            } else { // !isSuccessful also has exception
                cont.resumeWithException(e)
            }
        }
    }
}


fun Context.debugToast(text: String) {
    if (BuildConfig.DEBUG) {
        Toast.makeText(this, text, Toast.LENGTH_SHORT).apply {
            setGravity(Gravity.CENTER, 0, 0)
        }.show()
    }
}

fun Context.getFragmentManager(): FragmentManager? {
    return tryToGetValueOrDefault(
        { (this as? AppCompatActivity)?.supportFragmentManager }, null
    )
}

fun Activity.showToast(text: String){
    Toast.makeText(this, text, Toast.LENGTH_SHORT).show()
}

fun Activity.showToast(@StringRes stringRes: Int){
    Toast.makeText(this, getString(stringRes), Toast.LENGTH_SHORT).show()
}

fun Fragment.showToast(text: String){
    Toast.makeText(requireContext(), text, Toast.LENGTH_SHORT).show()
}

fun Fragment.showToast(@StringRes stringRes: Int){
    Toast.makeText(requireContext(), getString(stringRes), Toast.LENGTH_SHORT).show()
}

fun View.setSingleClickListener(debounceTime: Long = 500L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0
        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) return
            else action()
            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

fun BookEntity.getFormattedPhoneNumber() = Utility.beautifyPhoneNumber(this.businessPhone).takeIf { it.isNotBlank() }

fun BookEntity.getFormattedOwnerIdAsPhoneNumber() = Utility.beautifyPhoneNumber(this.ownerId).takeIf { it.isNotBlank() }

fun SpannableStringBuilder.boldText(strToBold: String, ignoreCase: Boolean = false): SpannableStringBuilder {
    if (indexOf(strToBold, ignoreCase = ignoreCase) < 0) return this
    val start = indexOf(strToBold, ignoreCase = ignoreCase)
    val end = start + (strToBold.length)
    setSpan(
            StyleSpan(Typeface.BOLD),
            start,
            end,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    return this
}

fun SpannableStringBuilder.colorText(strToColor: String, colorInt : Int, ignoreCase: Boolean = false): SpannableStringBuilder {
    if (indexOf(strToColor, ignoreCase = ignoreCase) < 0) return this
    val start = indexOf(strToColor, ignoreCase = ignoreCase)
    val end = start + (strToColor.length)
    setSpan(
        ForegroundColorSpan(colorInt),
        start,
        end,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    return this
}

inline fun <reified T : Enum<T>> valueOf(type: String): T? {
    return try {
        java.lang.Enum.valueOf(T::class.java, type)
    } catch (e: Exception) {
        null
    }
}

fun Double?.asFormattedCurrency() = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(this))

fun String?.toInt(defaultValue: Int): Int = try {
    this?.toInt() ?: defaultValue
} catch (e: NumberFormatException) {
    e.recordException()
    defaultValue
}

fun String?.toBoolean() = "true" == this

fun String?.isNotNullOrBlank() = !this.isNullOrBlank()

fun String?.isNotNullOrEmpty() = !this.isNullOrEmpty()


fun TextView.setDrawableRightListener(listener: () -> Unit) {
    isClickable = true
    setOnTouchListener { _, motionEvent ->
        val drawableRight = 2
        if (motionEvent.action != MotionEvent.ACTION_UP || compoundDrawables[drawableRight] == null)
            return@setOnTouchListener false
        if (motionEvent.rawX >= (right - compoundDrawables[drawableRight].bounds.width() - paddingEnd)) {
            listener()
            return@setOnTouchListener false
        }
        false
    }
}

fun TextView.setDrawableLeftListener(listener: () -> Unit) {
    isClickable = true
    setOnTouchListener { _, motionEvent ->
        val drawableLeft = 0
        if (motionEvent.action != MotionEvent.ACTION_UP || compoundDrawables[drawableLeft] == null)
            return@setOnTouchListener false
        if (motionEvent.rawX <= (paddingStart + compoundDrawables[drawableLeft].bounds.width())) {
            listener()
            return@setOnTouchListener false
        }
        false
    }
}

fun Category.getFormattedId() = formatCategoryId(this.getIdString().orEmpty())

fun formatCategoryId(id: String) = id.takeIf { it.contains("::") }
        ?: "${id}::${SessionManager.getInstance().businessId}"

fun <T> MutableList<T>.addIf(item: T, condition: Boolean){
    if(condition) this.add(item)
}

fun TextInputLayout.setErrorIf(isError: Boolean, errorMsg: String) {
    if (isError){
        this.error = errorMsg
    }else{
        this.error = ""
    }
}

fun String?.removeLeadingZeroes() = this?.replaceFirst("^[-0÷x+]+(?!$)".toRegex(), "")

fun CharSequence.isValidPhoneNumber() : Boolean = "^[+]?[0-9]{10,13}\$".toRegex().containsMatchIn(this)

fun Double?.greaterThan(d: Double): Boolean {
    return if (this == null) {
        false
    } else {
        this > d
    }
}

fun EditText.on(actionId: Int, func: () -> Unit) {
    setOnEditorActionListener { _, receivedActionId, _ ->

        if (actionId == receivedActionId) {
            func()
        }

        true
    }
}

fun Double.isFraction() = this % 1 != 0.0

fun TransactionItemDto.getFormattedFractionalQuantity(): String {
    return if (quantity.isFraction()) abs(quantity).toString() else abs(quantity).toInt().toString()
}

fun ImageView.loadImage(url: String?, @DrawableRes placeholder: Int) {
    url.takeIf { it != null && context != null }
            .let {
                Glide.with(context.applicationContext)
                        .load(url)
                        .placeholder(placeholder)
                        .fitCenter()
                        .dontAnimate()
                        .error(placeholder)
                        .into(this)
            }
}

fun ImageView.loadImage(@DrawableRes drawableRes: Int){
    Glide.with(this)
        .load(drawableRes)
        .into(this)
}

fun <T> ImageView.loadImageCircleCropped(
    imageRes: T,
    placeholderRes: Int = R.color.white,
    errorRes: Int = R.mipmap.default_icon
) {
    Glide.with(this.context)
        .load(imageRes)
        .circleCrop()
        .placeholder(placeholderRes)
        .error(errorRes)
        .into(this)
}

fun getScreenWidth(activity: Activity): Int {
    val displayMetrics = DisplayMetrics()
    activity.windowManager.defaultDisplay.getMetrics(displayMetrics)
    return displayMetrics.widthPixels
}

fun View.showView() {
    this.visibility = View.VISIBLE
}

fun View.hideView() {
    this.visibility = View.GONE
}

fun View.invisibleView() {
    this.visibility = View.INVISIBLE
}

fun ScrollView.moveToTop()
{
    this.isFocusableInTouchMode = true
    this.smoothScrollTo(0,0)
}

fun ScrollView.moveToTopWithoutScrollEffect()
{
    this.isFocusableInTouchMode = true
    this.fullScroll(View.FOCUS_UP)
    this.smoothScrollTo(0,0)
}

fun <T> Type.returnObject(json: String): T? {
    try {
        val gson: Gson = GsonBuilder().create()
        return gson.fromJson(json, this)
    } catch (e: Exception) {
        return null
    }
}

fun ViewPager2.setPreview(offsetPx: Int, pageMarginPx: Int) {
    this.setPageTransformer { page, position ->
        val viewPager = page.parent.parent as ViewPager2
        val offset = position * -(2 * offsetPx + pageMarginPx)
        if (viewPager.orientation == GestureOverlayView.ORIENTATION_HORIZONTAL) {
            if (ViewCompat.getLayoutDirection(viewPager) == ViewCompat.LAYOUT_DIRECTION_RTL) {
                page.translationX = -offset
            } else {
                page.translationX = offset
            }
        } else {
            page.translationY = offset
        }
    }
}

fun EditText.setupForSearch(coroutineScope: CoroutineScope, delay: Long = 1000, enableImeAction : Boolean = true,onQuerySubmitted: (newQuery: String) -> Unit){
    // add UX to submit query after user stop typing for n second
    afterTextChanged {
        coroutineScope.launch {
            delay(delay)
            onQuerySubmitted(text.toString())
        }
    }

    if (enableImeAction){
        // add action when Ime button clicked
        setOnEditorActionListener { _, actionId, event ->
            if (
                actionId == EditorInfo.IME_ACTION_SEARCH
                || event.action == KeyEvent.ACTION_DOWN
                && event.keyCode == KeyEvent.KEYCODE_ENTER
            ) {
                onQuerySubmitted(text.toString())
            }

            true
        }
    }
}

val emptyLambda = {}

/**
 * will check all editTexts field whether all is filled
 */

fun validateEditTextsNotEmpty(vararg editTexts: EditText): Boolean {
    val fieldsEmptiness = editTexts.map {
        it.text.toString().trim().isNotEmpty()
    }

    return !fieldsEmptiness.contains(false)
}

fun BookEntity.isEditingAddressForFirstTime(): Boolean {
        return  this.businessAddress.isNullOrEmpty() &&
                this.province.isNullOrEmpty() &&
                this.city.isNullOrEmpty() &&
                this.district.isNullOrEmpty() &&
                this.subdistrict.isNullOrEmpty() &&
                this.postalCode.isNullOrEmpty()
}

fun append(arr: Array<String>, element: String): Array<String> {
    val list: MutableList<String> = arr.toMutableList()
    list.add(element)
    return list.toTypedArray()
}

fun Double.roundTo(): Double {
    val df = DecimalFormat("#.#", DecimalFormatSymbols(Locale.ENGLISH)).apply {
        roundingMode = RoundingMode.HALF_UP
    }
    return df.format(this).toDouble()
}

fun SessionManager.getLanguageString(): String {
    return when(this.appLanguage){
        Language.DEFAULT.langCd, Language.INDONESIAN.langCd -> "id"
        Language.ENGLISH.langCd -> "en"
        else -> "id"
    }
}

val String?.orDash
    get() = (this ?: "-")

fun String?.orDefault(defaultValue: String)= this ?: defaultValue

fun Int?.orDefault(defaultValue: Int)= this ?: defaultValue

fun Long?.orDefault(defaultValue: Long) = this ?: defaultValue

fun Double?.ifNull(defaultValue: () -> Double?) = this ?: defaultValue()

fun Double.isZero() = this == 0.0

fun Double.isNotZero() = this != 0.0

fun Double?.isNotZero() = (this ?: 0.0) != 0.0

fun Int.isZero() = this == 0

fun Double?.getValueFromPercent(): Double {
    return if (this.orNil.isZero())
        0.0
    else
        this.orNil / 100
}

fun PackageManager.isAppInstalled(packageName: String): Boolean = try {
    getApplicationInfo(packageName, PackageManager.GET_META_DATA)
    true
} catch (e: Exception) {
    false
}

fun generateAndShareViewImage(context: Context?,
                              layout: View?,
                              text: String?,
                              mobile: String? = null,
                              isPhonebook: Boolean? = false,
                            from: String?) {
    try {
        val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> =
            ImageUtils.saveLayoutConvertedImage(layout, false)
        val receiverIntent = Intent(context, ReferralUploadReceiver::class.java)

        if (from.isNotNullOrEmpty()) {
            receiverIntent.putExtra("from", from)
        }

        if (isPhonebook == true) {
            receiverIntent.putExtra(AnalyticsConst.ENTRY_POINT, "referral_phonebook")
        } else {
            receiverIntent.putExtra(
                AnalyticsConst.ENTRY_POINT,
                AnalyticsConst.REFERRAL_HOME_PAGE
            )
        }
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(context, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getBroadcast(context, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        val shareLayoutImage = ShareLayoutImage(
            text,
            context,
            if (mobile.isNotNullOrEmpty()) "com.whatsapp" else "",
            mobile ?: "",
            mobile.isNotNullOrEmpty(),
            "Bagikan Dengan",
            true,
            pendingIntent,
            false
        )
        saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
    } catch (e: Exception) {
        e.printStackTrace()
    }


}
fun BookResponseListItem.toBookEntity(): BookEntity {
    val enablePayment = if (enabledPayment == true) 1 else 0
    return BookEntity(
        ownerId,
        bookId,
        language,
        bookName,
        bookType,
        bookTypeName,
        businessAddress,
        email,
        businessImage,
        businessName,
        businessOwnerName,
        businessPhone,
        tagLine,
        deleted,
        enableSmsAlerts,
        enablePayment
    )
}
fun CustomerResponseListItem.toCategoryEntity():CustomerEntity{
    return CustomerEntity(
        bookId,
        address,
        altCustomerId,
        customerId,
        countryCd,
        dueDate,
        imageUrl,
        name,
        phoneNumber,
        balance,
        deleted,
        langCd,
        lastModifiedAt
    )
}

fun TransactionResponseListItem.toTransactionEntity(): TransactionEntity {
    return TransactionEntity(
        transactionId,
        attachments,
        bookId,
        customerId,
        transactionDate,
        description,
        amount,
        deleted,
        isOffline,
        paymentDisbursableId
    )
}

fun AppExpenseCategoryResponseListItem.toCashCategoryEntity(): CashCategoryEntity {
    return CashCategoryEntity(
        cashCategoryId,
        bookId,
        name,
        balance,
        deleted,
        type,
        frequency,
        updatedAt

    )
}

fun AppExpenseTransactionResponseListItem.toCashTransactionEntity(): CashTransactionEntity {
    val calculatedPaymentStatus =
        if (paymentStatus.equals("UN_PAID", true)) 0 else if (paymentStatus.equals(
                "PAID",
                true
            )
        ) 1 else -1
    val attachmentString: StringBuilder? = null
    attachments?.let {
        it.forEach { str ->
            attachmentString?.append(str)
        }
    }
    return CashTransactionEntity(
        amount?.toDouble(),
        buyingPrice?.toDouble(),
        attachmentString.toString(),
        bookId,
        cashCategoryId,
        transactionDate,
        deleted,
        description,
        orderId,
        cashTransactionId,
        customerTransactionId,
        customerId,
        customerName,
        customerPhoneNumber,
        calculatedPaymentStatus,
        paymentMethod
    )
}

fun AppTransactionItemResponseListItem.toTransactionItemsEntity(): TransactionItemsEntity {
    return TransactionItemsEntity(
        productName ?: "",
        buyingPrice ?: 0.0,
        sellingPrice ?: 0.0,
        unitMeasure ?: "",
        transactionId ?: "",
        productId ?: "",
        quantity
    )
}

fun saveAndShare(context: Context?, view: View, shareString: String) {
    ImageUtils.saveLayoutConvertedImage(view, true)
    val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(view, false)
    val shareLayoutImage = ShareLayoutImage(shareString, context, null, null, false, false)
    saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
}
fun String.times(times: Int): String {
    val sb = StringBuilder()
    for (i in 1..times) {
        sb.append(this)
    }
    return sb.toString()
}

fun checkForLowMemory(context: Context?): ActivityManager.MemoryInfo {
    val activityManager = context?.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    return ActivityManager.MemoryInfo().also { memoryInfo ->
        activityManager.getMemoryInfo(memoryInfo)
    }
}

/**
 * Converts amount to short notation with Million/Billion suffix.
 */
fun Double.formatAmountShort(context: Context): String {
    val sb = StringBuilder()
    sb.append(Utility.getCurrency())
    val formatter = DecimalFormat("0.#")
    val amount = when {
        this >= PaymentConst.BILLION -> context.getString(
            R.string.x_billion,
            formatter.format(this / PaymentConst.BILLION)
        )
        this >= PaymentConst.MILLION -> context.getString(
            R.string.x_million,
            formatter.format(this / PaymentConst.MILLION)
        )
        else -> Utility.formatCurrency(this)
    }
    sb.append(amount)
    return sb.toString()
}

fun LayoutActivityTitleBinding.setToolbar(
    context: Context,
    toolbarLabel: String,
    helpUrl: String,
    backNavigation: () -> Unit
) {
    toolBarLabel.text = toolbarLabel
    tvReminder.hideView()
    tbPpob.navigationIcon = ContextCompat.getDrawable(
        context,
        R.drawable.ic_arrow_back
    )
    tbPpob.setNavigationOnClickListener {
        backNavigation()
    }
    ivHelp.setSingleClickListener {
        if (AppConfigManager.getInstance().useWebView()) {
            val intent =
                Intent(context, HelpCenterActivity::class.java)
            intent.putExtra(AppConst.URL, helpUrl)
            intent.putExtra(AppConst.TITLE, context.getString(R.string.help))
            context.startActivity(intent)
        } else {
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(helpUrl)))
        }
    }
}

fun View.singleClick(debounceTime: Long = 600L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0

        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) return
            else action()

            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

fun <T> Context.openActivity(it: Class<T>, extras: Bundle.() -> Unit = {}) {
    val intent = Intent(this, it)
    intent.putExtras(Bundle().apply(extras))
    startActivity(intent)
}

fun <T> Context.openActivityForResult(
    it: Class<T>,
    launcher: ActivityResultLauncher<Intent>,
    extras: Bundle.() -> Unit = {}
) {
    val intent = Intent(this, it)
    intent.putExtras(Bundle().apply(extras))
    launcher.launch(intent)
}