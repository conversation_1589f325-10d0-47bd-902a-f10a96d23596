package com.bukuwarung.managers.local_notification.domain;

import static com.bukuwarung.managers.local_notification.LocalNotificationManager.NOTIFICATION_CHANNEL_TITLE;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.collectingcalendar.main.CollectingCalendarActivity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.managers.local_notification.LocalNotificationData;
import com.bukuwarung.managers.local_notification.LocalNotificationIcon;
import com.bukuwarung.managers.local_notification.LocalNotificationManager;
import com.bukuwarung.managers.local_notification.LocalNotificationStyle;
import com.bukuwarung.session.SessionManager;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class DailyDueDateNotificationReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (hasRunToday()) return;
        List<CustomerEntity> customerListDueByToday = CustomerRepository.getInstance(context).getCustomerWithTodaysDueDate();
        if (!customerListDueByToday.isEmpty()) {
            String message = context.getString(R.string.daily_due_date_notif_message);
            LocalNotificationData localNotificationData = new LocalNotificationData(
                    context.getString(R.string.daily_due_date_notif_title, customerListDueByToday.size()),
                    message,
                    LocalNotificationIcon.DEFAULT
            );
            Intent backIntent = new Intent(context, MainActivity.class);
            backIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            Intent i = CollectingCalendarActivity.Companion.createIntent(context, true);
            final PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivities(context, 0, new Intent[]{backIntent, i}, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivities(context, 0, new Intent[]{backIntent, i}, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            LocalNotificationManager.Companion.showDefaultNotification(
                    Application.getAppContext(),
                    localNotificationData,
                    LocalNotificationStyle.BIG_TEXT,
                    pendingIntent, NOTIFICATION_CHANNEL_TITLE
            );
            SessionManager.getInstance().setDailyDueDateLastRun();
            Log.i("Daily Due Date", "Notifications sent.");
        }
    }

    private boolean hasRunToday() {
        long alarmLastRun = SessionManager.getInstance().getDailyDueDateLastRun();
        if (alarmLastRun == -1L) return false;
        //check by comparing day, month and year
        return new Date().getTime() - alarmLastRun < TimeUnit.DAYS.toMillis(1);
    }
}
