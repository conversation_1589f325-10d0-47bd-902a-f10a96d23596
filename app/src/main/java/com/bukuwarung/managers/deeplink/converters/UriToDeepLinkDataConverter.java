package com.bukuwarung.managers.deeplink.converters;

import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.managers.deeplink.DeepLinkData;
import com.bukuwarung.managers.deeplink.DeepLinkType;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.Converter;

public class UriToDeepLinkDataConverter extends Converter<Uri, DeepLinkData> {

    @Override
    public DeepLinkData convert(Uri input) {

        if (input == null) return null;
        String typeString = input.getQueryParameter(AppConst.DEEPLINK_KEY_TYPE);
        String data = input.getQueryParameter(AppConst.DEEPLINK_KEY_DATA);
        String referralCode = input.getQueryParameter(AppConst.DEEPLINK_TYPE_REFERRAL_CODE);
        if (data == null && referralCode != null) {
            data = referralCode;
            ReferralPrefManager.getInstance().setTemporaryReferralCode(referralCode);
        }
        if (typeString == null || data == null) return null;
        // "data" also contains URLs
        data = data.replace("api-v3.bukuwarung.com","api-v4.bukuwarung.com").replace("api-v2.bukuwarung.com","api-v4.bukuwarung.com");
        Bundle bundle = new Bundle();
        for (String name : input.getQueryParameterNames()) {
            if (AppConst.DEEPLINK_TYPE_WEB.equals(typeString)) {
                String originalParamValue = input.getQueryParameter(name);
                String budParamValue = originalParamValue.replace("api-v3.bukuwarung.com","api-v4.bukuwarung.com").replace("api-v2.bukuwarung.com","api-v4.bukuwarung.com");
                bundle.putString(name, budParamValue);
            }else{
                bundle.putString(name, input.getQueryParameter(name));
            }
        }
        DeepLinkType deepLinkType;
        if (AppConst.DEEPLINK_TYPE_ACTIVITY.equals(typeString)) {
            deepLinkType = DeepLinkType.ACTIVITY;
        } else if (AppConst.DEEPLINK_TYPE_WEB.equals(typeString)) {
            deepLinkType = DeepLinkType.WEB;
        } else if (AppConst.DEEPLINK_TYPE_WEB_BNPL.equals(typeString)) {
            deepLinkType = DeepLinkType.WEB_BNPL;
        } else if (AppConst.DEEPLINK_TYPE_REFERRAL.equals(typeString) || AppConst.DEEPLINK_TYPE_REFERRAL2.equals(typeString)) {
            deepLinkType = DeepLinkType.REFERRAL;
        } else if (AppConst.DEEPLINK_TYPE_BUKU.equals(typeString)) {
            deepLinkType = DeepLinkType.SCHEMA_LINK;
        } else {
            return null; // another type that we haven't known how to handle
        }
        return new DeepLinkData(deepLinkType, data, bundle);
    }
}
