package com.bukuwarung.managers.deeplink;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.util.Log;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.managers.Manager;
import com.bukuwarung.managers.deeplink.converters.UriToDeepLinkDataConverter;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.neuro.api.SourceLink;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.Utility;
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks;
import com.google.firebase.dynamiclinks.PendingDynamicLinkData;

import javax.annotation.Nullable;

public class DeepLinkManager extends Manager {

    private Activity activity;
    private SessionManager sessionManager;
    private UriToDeepLinkDataConverter converter;
    private Neuro neuro;

    private boolean detectReferralCode = true;

    public DeepLinkManager(Activity activity) {
        this.activity = activity;
        sessionManager = SessionManager.getInstance();
        // TODO: EXTRACT WITH DI
        converter = new UriToDeepLinkDataConverter();
    }

    public void setNotDetectReferralCode() {
        this.detectReferralCode = false;
    }

    public void setNeuro(Neuro neuro) {
        this.neuro = neuro;
    }

    private FirebaseDynamicLinks getDynamicLinksInstance() {
        return FirebaseDynamicLinks.getInstance();
    }

    private void doOnError(Exception e) {
        Log.e(TAG, "Something wrong when parsing deeplink", e);
    }

    private void processDynamicLinkData(PendingDynamicLinkData dynamicLinkData) {
        if (dynamicLinkData != null) {
            final Uri uri = dynamicLinkData.getLink();
            if (uri == null) return;
            DeepLinkData deepLinkData = converter.convert(uri);
            // LegacySignalHandler uses uri string for redirection instead of deepLinkData
            String uriString = uri.toString()
                    .replace("api-v3.bukuwarung.com","api-v4.bukuwarung.com")
                    .replace("api-v2.bukuwarung.com","api-v4.bukuwarung.com");
            actBasedOnDeepLinkData(uriString, deepLinkData);
        }
    }

    public void actBasedOnDeepLinkData(String link, DeepLinkData deepLinkData) {
        // we'll only redirect if the user has logged in for some cases.
        final AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put("is_logged_in", sessionManager.isLoggedIn());
        propBuilder.put("token_refresh_gap", (System.currentTimeMillis() - SessionManager.getInstance().getSessionStart()));
        propBuilder.put("url", link);
        propBuilder.put("has_data", deepLinkData != null);
        if (deepLinkData != null) {
            propBuilder.put("deeplink", deepLinkData.data);
            propBuilder.put("deeplink_type", deepLinkData.type);
            if (sessionManager.isLoggedIn()) {
                switch (deepLinkData.type) {
                    case WEB:
                        AuthHelper.refreshUserSession();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    AppAnalytics.trackEvent("deeplink_redirection_web");
                                    redirect(link, "web", deepLinkData.data);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }, 2000);
                        break;
                    case WEB_BNPL:
                        AuthHelper.refreshUserSession();
                        if (!Utility.isBlank(SessionManager.getInstance().getSelectedBookName()) && SessionManager.getInstance().getSelectedBookName().contains("alangin")) {

                        } else {
                            BookEntity bnplBook = BusinessRepository.getInstance(Application.getAppContext()).getBnplBookId();
                            SessionManager.getInstance().setSelectedBookName(bnplBook.bookName);
                            SessionManager.getInstance().setBusinessId(bnplBook.bookId);
                        }
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    AppAnalytics.trackEvent("deeplink_redirection_web");
                                    redirect(link, "web", deepLinkData.data);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }, 2000);
                        break;
                    case ACTIVITY:
                        AppAnalytics.trackEvent("deeplink_redirection_activity");
                        redirect(link + "&launch=2", "activity", deepLinkData.data);
                        break;
                    case REFERRAL:
                        AppAnalytics.trackEvent("deeplink_redirection_referral");
                        processReferralData(link, deepLinkData.data);
                        break;
                    case SCHEMA_LINK:
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_DEEPLINK_REDIRECTION_ACTIVITY);
                        redirect(deepLinkData.data, AppConst.DEEPLINK_TYPE_BUKU, deepLinkData.data);
                        break;
                }
            } else if (deepLinkData.type == DeepLinkType.REFERRAL) {
                processReferralData(link, deepLinkData.data);
            }
        } else {
            if (sessionManager.isLoggedIn()) {
                redirect(link, "activity", link);
            }
        }
        AppAnalytics.trackEvent("deeplink_redirection_start", propBuilder);
    }

    private void processReferralData(String link, String referralCode) {
        ReferralPrefManager.getInstance().setTemporaryReferralCode(referralCode);
        AppAnalytics.PropBuilder prop =  new AppAnalytics.PropBuilder();
        prop.put("code",referralCode);
        prop.put("link",link);
        AppAnalytics.trackEvent("process_referral_code",prop);
    }

    private void redirect(String link, String propStatus, String propDetail) {
        final SourceLink sourceLink = new SourceLink(activity, link);
        neuro.route(
                sourceLink,
                activity::startActivity,
                () -> {
                    AppAnalytics.trackEvent("open_deep_link", propStatus, propDetail);
                    return null;
                },
                (throwable) -> {
                    Log.e(TAG, "Something wrong when redirecting to activity", throwable);
                    return null;
                }
        );
    }

    @Override
    public void start(@Nullable Intent intent) {
        // start manager
        Log.d(TAG, "DeeplinkManager Started...");
        assert intent != null;
        getDynamicLinksInstance()
                .getDynamicLink(intent)
                .addOnSuccessListener(this::processDynamicLinkData)
                .addOnFailureListener(this::doOnError);
    }

    @Override
    public void stop() {
        // do nothing
    }

    public static String TAG = "DeepLinkManager";
}