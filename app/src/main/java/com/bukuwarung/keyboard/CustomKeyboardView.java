package com.bukuwarung.keyboard;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.utils.ExtensionsKt;
import com.bukuwarung.utils.Utility;
import com.bukuwarung.utils.UtilsKt;

import java.text.DecimalFormat;

import androidx.annotation.IdRes;
import androidx.core.content.ContextCompat;

public class CustomKeyboardView extends RelativeLayout implements View.OnClickListener {

    final static String OP_DIV = "÷";
    final static String OP_SUM = "+";
    final static String OP_SUB = "-";
    final static String OP_MULTIPLY = "x";
    final static String ops = OP_DIV + OP_MULTIPLY + OP_SUB + OP_SUM;
    private TextView exprTv;
    private TextView resultTv;
    private LinearLayout resultLayout;
    private View cursor;
    private TextView currency;
    private boolean invalid;
    private boolean submitted;
    private double resultDouble;
    private OnSubmitListener onSubmitListener;
    public TextView submitBtn;
    private boolean fromBulk = false;
    private boolean fromPos = false;

    public CustomKeyboardView(Context context) {
        super(context);
        init();
    }

    public CustomKeyboardView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CustomKeyboardView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setFromPos() {
        this.fromPos = true;
    }

    public void setExprTv(TextView display) {
        this.exprTv = display;
    }

    public void setFromBulk() {
        this.fromBulk = true;
    }

    public void setResultTv(TextView resultTv) {
        this.resultTv = resultTv;
    }

    private void init() {
        inflate(getContext(), R.layout.calculator_keyboard_layout, this);
        initViews();
    }

    private void initViews() {
        $(R.id.zero_button).setOnClickListener(this);
        $(R.id.one_button).setOnClickListener(this);
        $(R.id.two_zero_button).setOnClickListener(this);
        $(R.id.two_button).setOnClickListener(this);
        $(R.id.three_button).setOnClickListener(this);
        $(R.id.four_button).setOnClickListener(this);
        $(R.id.five_button).setOnClickListener(this);
        $(R.id.six_button).setOnClickListener(this);
        $(R.id.seven_button).setOnClickListener(this);
        $(R.id.eight_button).setOnClickListener(this);
        $(R.id.nine_button).setOnClickListener(this);
        $(R.id.clear_button).setOnClickListener(this);
        $(R.id.delete_button).setOnClickListener(this);

        $(R.id.sum_button).setOnClickListener(this);
        $(R.id.subtraction_button).setOnClickListener(this);
        $(R.id.multiplication_button).setOnClickListener(this);
        $(R.id.divider_button).setOnClickListener(this);
        submitBtn = $(R.id.submit_button);
        if(Utility.getCurrency().equals("Rp")){
            ((Button)$(R.id.point_button)).setText(",");
        }
        $(R.id.point_button).setOnClickListener(this);
        final View v = this;
        $(R.id.submit_button).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (invalid) {
                    return;
                }
                if (exprTv.getText().length() == 0) {
                    exprTv.setText("");
                    resultTv.setText("0");
                } else {
                    DecimalFormat format = new DecimalFormat("0.#");
                    exprTv.setText(String.valueOf(format.format(resultDouble)));
                }
                v.setVisibility(GONE);
                resultLayout.setVisibility(GONE);
                v.setVisibility(GONE);
                hideCursor();
                if (onSubmitListener != null) onSubmitListener.onSubmit();
                submitted = true;
            }
        });
    }

    public void showCursor() {
        Animation animBlink = AnimationUtils.loadAnimation(getContext(),
                R.anim.blink);
        if (UtilsKt.isAnimEnabled(getContext())) cursor.startAnimation(animBlink);
        cursor.setVisibility(View.VISIBLE);
        if (exprTv.getText().length() > 0) {
            resultLayout.setVisibility(VISIBLE);
        }
    }

    public void hideCursor() {
        if (UtilsKt.isAnimEnabled(getContext())) cursor.clearAnimation();
        cursor.setVisibility(View.INVISIBLE);
        if (exprTv.getText().length() > 0) {
            resultLayout.setVisibility(GONE);
        }
    }

    @Override
    public void onClick(View v) {
//

        switch (v.getId()) {
            case R.id.clear_button: { // handle clear button
                exprTv.setText(null);
                updateResult("0");
            }
            break;
            case R.id.delete_button: { // handle backspace button
                // delete one character
                String text = exprTv.getText().toString();
                int charCount = text.length();
                if (charCount <= 0) {
                    return;
                }
                if (charCount > 0) {
                    text = text.substring(0, charCount - 1);
                    exprTv.setText(text);
                }
                charCount = text.length();
                if (charCount > 1 && ops.contains(text.charAt(charCount - 1) + "")) {
                    updateResult(text.substring(0, charCount - 1));
                } else if (charCount == 0) {
                    updateResult("0");
                } else {
                    updateResult(text);
                }
                if (charCount <= 0) {
                    resultTv.setText("");
                }

            }
            break;
            case R.id.sum_button:
                if (exprTv.getText().length() == 0) {
                    return;
                }
                handleOp(v);
                exprTv.setText(exprTv.getText() + OP_SUM);
                break;
            case R.id.subtraction_button:
                if (exprTv.getText().length() == 0) {
                    return;
                }
                handleOp(v);
                exprTv.setText(exprTv.getText() + OP_SUB);
                break;
            case R.id.multiplication_button:
                if (exprTv.getText().length() == 0) {
                    return;
                }
                handleOp(v);
                exprTv.setText(exprTv.getText() + OP_MULTIPLY);
                break;
            case R.id.divider_button:
                if (exprTv.getText().length() == 0) {
                    return;
                }
                handleOp(v);
                exprTv.setText(exprTv.getText() + OP_DIV);
                break;
            case R.id.point_button:
                handleDecimal(v);
                break;
            default:
                Button btn = (Button) v;
                String text = exprTv.getText().toString();
                if (updateResult(text + btn.getText())) {
                    exprTv.setText(ExtensionsKt.removeLeadingZeroes(text + btn.getText()));
                } else {
                    exprTv.setText(text);
                }
                break;
        }


        if (exprTv.getText().length() > 0) {
            this.resultLayout.setVisibility(VISIBLE);
            if (!fromPos) {
                submitBtn.setBackgroundColor(getContext().getResources().getColor(R.color.buku_CTA));
            }
        }else {
            this.resultLayout.setVisibility(GONE);
            if (!fromPos) {
                submitBtn.setBackgroundColor(getContext().getResources().getColor(R.color.buku_CTA));
            }
        }
    }

    private void handleOp(View v) {

        Button btn = (Button) v;
        String text = exprTv.getText().toString();
        int charCount = text.length();
        if (charCount > 0 && (ops.contains(text.charAt(charCount - 1) + "") || OP_DIV.equals(text.charAt(charCount - 1) + "") || OP_MULTIPLY.equals(text.charAt(charCount - 1) + ""))) {
            exprTv.setText(text.substring(0, charCount - 1));
        } else {
            updateResult(exprTv.getText().toString());
            exprTv.setText(text);
        }
    }

    private void handleDecimal(View v) {
        String text = exprTv.getText().toString();
        int charCount = text.length();
        if (charCount < 1) {
            return;
        }
        for (int i = charCount - 1; i > 0; i--) {
            if (ops.contains(text.charAt(i) + "")) {
                break;
            } else if (text.charAt(i) == '.' || text.charAt(i) == ',') {
                return;
            }
        }
        if (Utility.getCurrency().equals("Rp")) {
            exprTv.setText(text + ",");
        } else {
            exprTv.setText(text + ".");
        }
    }

    public boolean updateResult(String expressionStr) {
        if (expressionStr.length() < 1 || expressionStr.charAt(expressionStr.length() - 1) == '.') {
            return true;
        }
        expressionStr = expressionStr.replace(OP_MULTIPLY, "*");
        expressionStr = expressionStr.replace(OP_DIV, "/");
        expressionStr = expressionStr.replace(",", ".");
        Double result = ExpressionEvaluator.eval(expressionStr, fromBulk);
        if (result < 0 || Double.isInfinite(result) || Double.isNaN(result)) {
            return false;
        } else {
            this.resultDouble = result.doubleValue();
            String resultStr = Utility.priceToString(result, fromBulk);
//            if(Utility.getCurrency().equals("Rp")){
//                resultStr = resultStr.replace(".",",");
//            }
            resultTv.setText(resultStr);
            currency.setVisibility(VISIBLE);
            invalid = false;
            submitted = false;
            return true;
        }

    }

    public boolean hasError() {
        return this.invalid;
    }

    public String getInputText() {
        return exprTv.getText().toString();
    }

    protected <T extends View> T $(@IdRes int id) {
        return (T) super.findViewById(id);
    }

    public void setResultLayout(LinearLayout resultBox) {
        this.resultLayout = resultBox;
    }

    public void setCursor(View cursor) {
        this.cursor = cursor;
    }

    public View getCursor() {
        return cursor;
    }

    public void setCurrency(TextView currencySymbol) {
        this.currency = currencySymbol;
    }

    public void setOnSubmitListener(OnSubmitListener onSubmitListener) {
        this.onSubmitListener = onSubmitListener;
    }

    public void setResult(String text) {
        this.resultTv.setText(text);
    }

    public void submit() {
        try {
            if (exprTv.getText().length() == 0) {
                exprTv.setText("0");
                return;
            }
            if (!submitted) {
                exprTv.setText(String.valueOf(resultDouble));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getInputAmount() {
        return String.valueOf(resultDouble);
    }

    public double getInputAmountInDouble() {
        return resultDouble;
    }

    public void setResultAmount(String text) {
        try {
            this.resultTv.setText(text);
            this.resultDouble = Double.parseDouble(text);
        } catch (Exception ex) {
            Log.e("CustomKeyboardView", "Error Parsing Amount", ex);
        }
    }

    public void setAmount(Double amount) {
        this.resultDouble = amount;
    }

    public interface OnSubmitListener {
        void onSubmit();
    }
}
