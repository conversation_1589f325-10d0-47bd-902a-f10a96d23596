package com.bukuwarung.database.helper;

import com.bukuwarung.Application;
import com.bukuwarung.database.dto.TransactionItemDto;
import com.bukuwarung.database.entity.AppEntity;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.ProductEntity;
import com.bukuwarung.database.entity.SelfReminderEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionItemsEntity;
import com.bukuwarung.database.entity.UserProfileEntity;
import com.bukuwarung.database.entity.referral.PaymentUserReferral;
import com.bukuwarung.database.entity.referral.ReferralCode;
import com.bukuwarung.database.entity.referral.ReferralLink;
import com.bukuwarung.database.entity.referral.UserReferral;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.ReferralUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

public class EntityHelper {

    public static void fillBusinessMetadata(BookEntity bookEntity){
        if(bookEntity == null){
            bookEntity = new BookEntity();
            bookEntity.bookId = AppIdGenerator.resourceUUID();
            bookEntity.ownerId = User.getUserId();
        }
        bookEntity.createdAt = Utility.getCurrentTime();
        bookEntity.createdByUser =User.getUserId();
        bookEntity.createdByDevice = User.getDeviceId();
        bookEntity.updatedAt = Utility.getCurrentTime();
        bookEntity.updatedByUser = User.getUserId();;
        bookEntity.updatedByDevice = User.getDeviceId();;
        bookEntity.serverSeq = Long.valueOf(0);
        bookEntity.dirty = Integer.valueOf(1);
    }

    public static void updateBusinessMetadata(BookEntity bookEntity){
        if(bookEntity == null){
            bookEntity = new BookEntity();
            bookEntity.bookId = AppIdGenerator.resourceUUID();
            bookEntity.ownerId = User.getUserId();
        }
        bookEntity.updatedAt = Utility.getCurrentTime();
        bookEntity.updatedByUser = User.getUserId();
        bookEntity.updatedByDevice = User.getDeviceId();;
        bookEntity.dirty = Integer.valueOf(1);
    }

    public static void fillCstMetadata(CustomerEntity customerEntity){
        if(customerEntity == null) return;
        customerEntity.createdAt = Utility.getCurrentTime();
        customerEntity.createdByUser =User.getUserId();
        customerEntity.createdByDevice = User.getDeviceId();
        customerEntity.updatedAt = Utility.getCurrentTime();
        customerEntity.updatedByUser = User.getUserId();;
        customerEntity.updatedByDevice = User.getDeviceId();;
        customerEntity.serverSeq = Long.valueOf(0);
        customerEntity.dirty = Integer.valueOf(1);
    }

    public static void fillReminderMetadata(SelfReminderEntity reminderEntity){
        reminderEntity.createdAt = Utility.getCurrentTime();
        reminderEntity.createdByUser =User.getUserId();
        reminderEntity.createdByDevice = User.getDeviceId();
        reminderEntity.updatedAt = Utility.getCurrentTime();
        reminderEntity.updatedByUser = User.getUserId();;
        reminderEntity.updatedByDevice = User.getDeviceId();;
        reminderEntity.serverSeq = Long.valueOf(0);
        reminderEntity.dirty = Integer.valueOf(1);
    }

    public static void fillMetadata(UserProfileEntity userProfileEntity) {
        userProfileEntity.setCreatedAt(String.valueOf(Utility.getCurrentTime()));
        userProfileEntity.setCreatedByUser(User.getUserId());
        userProfileEntity.setCreatedByDevice(User.getDeviceId());
        userProfileEntity.setUpdatedAt(String.valueOf(Utility.getCurrentTime()));
        userProfileEntity.setUpdatedByUser(User.getUserId());
        userProfileEntity.setUpdatedByDevice(User.getDeviceId());
        userProfileEntity.setServerSeq(Long.valueOf(0));
    }

    public static void fillCashCategoryMetadata(CashCategoryEntity cashCategoryEntity){
        cashCategoryEntity.createdAt = Utility.getCurrentTime();
        cashCategoryEntity.createdByUser =User.getUserId();
        cashCategoryEntity.createdByDevice = User.getDeviceId();
        cashCategoryEntity.updatedAt = Utility.getCurrentTime();
        cashCategoryEntity.updatedByUser = User.getUserId();;
        cashCategoryEntity.updatedByDevice = User.getDeviceId();;
        cashCategoryEntity.serverSeq = Long.valueOf(0);
        cashCategoryEntity.dirty = Integer.valueOf(1);
    }

    public static void updateCstMetadata(CustomerEntity customerEntity){
        customerEntity.updatedAt = Utility.getCurrentTime();
        customerEntity.updatedByUser = User.getUserId();
        customerEntity.updatedByDevice = User.getDeviceId();;
        customerEntity.dirty = Integer.valueOf(1);
    }

    public static void fillTxnMetadata(TransactionEntity txnEntity){
        txnEntity.createdAt = Utility.getCurrentTime();
        txnEntity.createdByUser =User.getUserId();
        txnEntity.createdByDevice = User.getDeviceId();
        txnEntity.updatedAt = Utility.getCurrentTime();
        txnEntity.updatedByUser = User.getUserId();;
        txnEntity.updatedByDevice = User.getDeviceId();;
        txnEntity.serverSeq = Long.valueOf(0);
        txnEntity.dirty = Integer.valueOf(1);
    }

    public static void updateTxnMetadata(TransactionEntity txnEntity){
        txnEntity.updatedAt = Utility.getCurrentTime();
        txnEntity.updatedByUser = User.getUserId();
        txnEntity.updatedByDevice = User.getDeviceId();;
        txnEntity.dirty = Integer.valueOf(1);
    }

    /**
     * having AppEntity as the param type makes this method
     * usable for all entities inherited from AppEntity
     * */
    public static void fillMetaData(AppEntity entity){
        entity.createdAt = Utility.getCurrentTime();
        entity.createdByUser =User.getUserId();
        entity.createdByDevice = User.getDeviceId();
        entity.updatedAt = Utility.getCurrentTime();
        entity.updatedByUser = User.getUserId();;
        entity.updatedByDevice = User.getDeviceId();;
        entity.serverSeq = Long.valueOf(0);
        entity.dirty = Integer.valueOf(1);
    }

    /**
     * having AppEntity as the param type makes this method
     * usable for all entities inherited from AppEntity
     * */
    public static void updateMetadata(AppEntity entity){
        entity.updatedAt = Utility.getCurrentTime();
        entity.updatedByUser = User.getUserId();
        entity.updatedByDevice = User.getDeviceId();;
        entity.dirty = Integer.valueOf(1);
    }
    
    //String code, boolean active, String link, String userId, long earnedPoint, long activatedAt, long disabledAt, long createdAt, long updatedAt
    public static ReferralCode prepareNewReferralCode(ReferralLink referralLink,String userId){
        ReferralCode referralCode = new ReferralCode(referralLink.code,false,referralLink.deeplink,userId,0,-1,-1,referralLink.createdAt,referralLink.createdAt);
        return referralCode;
    }

    //String code, boolean active, String link, String userId, long earnedPoint, long activatedAt, long disabledAt, long createdAt, long updatedAt
    public static UserReferral getUserReferralForRegistration(
            ReferralLink referralLink, String userId,String myLeaderboardName, String referralCode,
            boolean profileCompleted, boolean cardCompleted){
        UserReferral userReferral = new UserReferral(
                myLeaderboardName,referralLink.code,referralLink.sender,
                ReferralRepository.REGISTRATION_POINTS,-1,true,
                AppIdGenerator.getCurrentTime(), referralCode, profileCompleted, cardCompleted);
        return userReferral;
    }

    //String code, boolean active, String link, String userId, long earnedPoint, long activatedAt, long disabledAt, long createdAt, long updatedAt
    public static UserReferral getEmptyUserReferralForRegistration(String referralCode, String userId,String myLeaderboardName,
                                                                   boolean profileCompleted, boolean cardCompleted){
        long initialPoint = (profileCompleted?ReferralRepository.POINTS_PROFILE_COMPLETED:0)+(cardCompleted?ReferralRepository.POINTS_CARD_COMPLETED:0);
        UserReferral userReferral = new UserReferral(
                myLeaderboardName,"","",
                initialPoint,-1,true,
                AppIdGenerator.getCurrentTime(), referralCode, profileCompleted, cardCompleted);
        return userReferral;
    }

    public static UserReferral getUserReferralForSharing(ReferralLink referralLink,
                                                         String userId,String myLeaderboardName, String referralCode,
                                                         boolean profileCompleted, boolean cardCompleted){
        UserReferral userReferral = new UserReferral(
                myLeaderboardName,referralLink.code,
                referralLink.sender,0,-1,true,
                AppIdGenerator.getCurrentTime(), referralCode, profileCompleted, cardCompleted);
        return userReferral;
    }

    public static PaymentUserReferral emptyReferralObject(String userId){
        //String leaderboardName, String referralCodeInUse, long referralCodeRegisteredAt, String sender, long points, int rank, boolean active, long createdAt, long updatedAt, String myReferralCode, String shareableLink, int level
        PaymentUserReferral paymentUserReferral = new PaymentUserReferral(
                userId,"",-1,"",0,-1,true,AppIdGenerator.getCurrentTime(),AppIdGenerator.getCurrentTime(), ReferralUtils.getCodePrefix()+""+ReferralUtils.getRandonIdx(1000,90000),"",-1);
        paymentUserReferral.altRefCode = AppIdGenerator.getReportId();
        paymentUserReferral.userId = User.getUserId();
        return paymentUserReferral;
    }

    public static List<TransactionItemsEntity> completeTransactionItemsList(String newTrxId, List<TransactionItemDto> transactionItems) {
        List<TransactionItemsEntity> transactionItemsEntityList = new ArrayList<>();
        for(TransactionItemDto transactionItemDto:transactionItems){
            List<ProductEntity> productList = ProductRepository.getInstance(Application.getAppContext()).getProducstById(transactionItemDto.productId);
            ProductEntity targetProduct = null;
            if(!ListUtils.isEmpty(productList)){
                targetProduct = productList.get(0);
            }else{
                targetProduct = new ProductEntity(User.getBusinessId(),AppIdGenerator.resourceUUID(),transactionItemDto.productName);
                fillProductMetadata(targetProduct);
                ProductRepository.getInstance(Application.getAppContext()).insertProduct(targetProduct);
            }
            TransactionItemsEntity transactionItemsEntity = new TransactionItemsEntity(transactionItemDto.productName,transactionItemDto.buyingPrice,transactionItemDto.sellingPrice,transactionItemDto.measurementUnit,newTrxId,targetProduct.productId,transactionItemDto.quantity);
            fillTxnItemMetadata(transactionItemsEntity);
            transactionItemsEntityList.add(transactionItemsEntity);
        }
        return transactionItemsEntityList;
    }

    public static TransactionItemsEntity fillTxnItemMetadata(TransactionItemsEntity txnEntity){
        txnEntity.createdAt = Utility.getCurrentTime();
        txnEntity.createdByUser =User.getUserId();
        txnEntity.createdByDevice = User.getDeviceId();
        txnEntity.updatedAt = Utility.getCurrentTime();
        txnEntity.updatedByUser = User.getUserId();;
        txnEntity.updatedByDevice = User.getDeviceId();;
        txnEntity.serverSeq = Long.valueOf(0);
        txnEntity.dirty = Integer.valueOf(1);
        return txnEntity;
    }

    public static ProductEntity fillProductMetadata(ProductEntity productEntity){
        productEntity.createdAt = Utility.getCurrentTime();
        productEntity.createdByUser =User.getUserId();
        productEntity.createdByDevice = User.getDeviceId();
        productEntity.updatedAt = Utility.getCurrentTime();
        productEntity.updatedByUser = User.getUserId();;
        productEntity.updatedByDevice = User.getDeviceId();;
        productEntity.serverSeq = Long.valueOf(0);
        productEntity.dirty = Integer.valueOf(1);
        if(productEntity.bookId == null){
            productEntity.bookId = User.getBusinessId();
        }
        return productEntity;
    }

    public static TransactionItemsEntity updateTxnItemMetadata(TransactionItemsEntity txnEntity){
        txnEntity.updatedAt = Utility.getCurrentTime();
        txnEntity.updatedByUser = User.getUserId();;
        txnEntity.updatedByDevice = User.getDeviceId();;
        txnEntity.serverSeq = Long.valueOf(0);
        txnEntity.dirty = Integer.valueOf(1);
        return txnEntity;
    }

    public static AppEntity fillEntityMetadata(AppEntity entity){
        entity.createdAt = Utility.getCurrentTime();
        entity.createdByUser =User.getUserId();
        entity.createdByDevice = User.getDeviceId();
        entity.updatedAt = Utility.getCurrentTime();
        entity.updatedByUser = User.getUserId();;
        entity.updatedByDevice = User.getDeviceId();;
        entity.serverSeq = Long.valueOf(0);
        entity.dirty = Integer.valueOf(1);
        return entity;
    }

    public static AppEntity fillUpdateEntityMetadata(AppEntity entity){
        entity.updatedAt = Utility.getCurrentTime();
        entity.updatedByUser = User.getUserId();;
        entity.updatedByDevice = User.getDeviceId();;
        entity.serverSeq = Long.valueOf(0);
        entity.dirty = Integer.valueOf(1);
        return entity;
    }
}
