package com.bukuwarung.database.helper;

import androidx.room.TypeConverter;

import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.utils.Utility;

public class TransactionEntityTypeConverter {

    @TypeConverter
    public static String fromTransactionEntityTypeToString(TransactionEntityType transactionEntityType) {
        if (transactionEntityType == null)
            return null;
        return transactionEntityType.toString();
    }

    @TypeConverter
    public static TransactionEntityType fromStringToTransactionEntityType(String transactionEntityType) {
        if (Utility.isBlank(transactionEntityType))
            return TransactionEntityType.DEFAULT;

        if (transactionEntityType.equalsIgnoreCase("PAYMENT")) {
            return TransactionEntityType.PAYMENT;
        } else if(transactionEntityType.equalsIgnoreCase("CASH_TRANSACTION")){
            return TransactionEntityType.CASH_TRANSACTION;
        } else if(transactionEntityType.equalsIgnoreCase("BRICK_TRANSACTION")){
            return TransactionEntityType.BRICK_TRANSACTION;
        }
        else {
            return TransactionEntityType.DEFAULT;
        }
    }

}
