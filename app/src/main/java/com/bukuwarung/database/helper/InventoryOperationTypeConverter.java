package com.bukuwarung.database.helper;

import androidx.room.TypeConverter;

import com.bukuwarung.database.dao.InventoryDao;
import com.bukuwarung.database.entity.InventoryOperationType;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.utils.Utility;

public class InventoryOperationTypeConverter {

    @TypeConverter
    public static String fromInventoryOperationTypeToString(InventoryOperationType inventoryOperationType) {
        if (inventoryOperationType == null)
            return null;
        return inventoryOperationType.toString();
    }

    @TypeConverter
    public static InventoryOperationType fromStringToInventoryOperationType(String inventoryOperationType) {
        if (Utility.isBlank(inventoryOperationType))
            return InventoryOperationType.DEFAULT;

        if (inventoryOperationType.equalsIgnoreCase("PERUBAHAN_HARGA_JUAL")) {
            return InventoryOperationType.PERUBAHAN_HARGA_JUAL;
        } else if (inventoryOperationType.equalsIgnoreCase("PERUBAHAN_HARGA_BELI")) {
          return InventoryOperationType.PERUBAHAN_HARGA_BELI;
        } else if (inventoryOperationType.equalsIgnoreCase("ADD_STOCK")) {
            return InventoryOperationType.ADD_STOCK;
        } else if (inventoryOperationType.equalsIgnoreCase("REMOVE_STOCK")) {
            return InventoryOperationType.REMOVE_STOCK;
        } else if (inventoryOperationType.equalsIgnoreCase("SALE_TRANSACTION")) {
            return InventoryOperationType.SALE_TRANSACTION;
        } else if (inventoryOperationType.equalsIgnoreCase("EXPENSE_TRANSACTION")) {
            return InventoryOperationType.EXPENSE_TRANSACTION;
        }else{
            return InventoryOperationType.DEFAULT;
        }

    }

}
