package com.bukuwarung.database.helper;

import com.bukuwarung.utils.ListUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.util.List;

public class AttechmentSerializer {

    public static class AttachmentsSerDes implements JsonDeserializer<String>, JsonSerializer<String> {
        public JsonElement serialize(String str, Type type, JsonSerializationContext jsonSerializationContext) {
            JsonArray jsonArray = new JsonArray();
            List<String> stringToList = ListUtils.convertToList(str);
            if (stringToList == null) {
                return JsonNull.INSTANCE;
            }
            for (String add : stringToList) {
                jsonArray.add(add);
            }
            return jsonArray;
        }

        public String deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            if (jsonElement == null) {
                return null;
            }
            return jsonElement.toString();
        }
    }
}
