package com.bukuwarung.database;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.bukuwarung.activities.productcategory.data.model.ProductCategoryCrossRef;
import com.bukuwarung.activities.productcategory.data.model.ProductCategoryDao;
import com.bukuwarung.bulk.data.local.CashCategoryDao;
import com.bukuwarung.bulk.data.local.UserTransactionsDao;
import com.bukuwarung.database.dao.BankAccountDao;
import com.bukuwarung.database.dao.BusinessDao;
import com.bukuwarung.database.dao.CashDao;
import com.bukuwarung.database.dao.CommonDao;
import com.bukuwarung.database.dao.CustomerDao;
import com.bukuwarung.database.dao.InventoryDao;
import com.bukuwarung.database.dao.ProductDao;
import com.bukuwarung.database.dao.SelfReminderDao;
import com.bukuwarung.database.dao.TransactionDao;
import com.bukuwarung.database.entity.*;
import com.bukuwarung.database.helper.InventoryOperationTypeConverter;
import com.bukuwarung.database.helper.TransactionEntityTypeConverter;
import org.jetbrains.annotations.NotNull;


@Database(entities = {
        UserProfileEntity.class,
        BookEntity.class,
        CustomerEntity.class,
        TransactionEntity.class,
        CashCategoryEntity.class,
        CashTransactionEntity.class,
        ProductEntity.class,
        TransactionItemsEntity.class,
        SelfReminderEntity.class,
        InventoryHistoryEntity.class,
        MeasurementEntity.class,
        BankAccount.class,
        ProductCategoryEntity.class,
        ProductCategoryCrossRef.class },
        version = 33, exportSchema = false)
@TypeConverters({TransactionEntityTypeConverter.class, InventoryOperationTypeConverter.class})
public abstract class AppDatabase extends RoomDatabase {

    private static volatile AppDatabase INSTANCE;

    public abstract BusinessDao businessDao();

    public abstract CustomerDao customerDao();

    public abstract CommonDao commonDao();

    public abstract ProductDao productDao();

    public abstract CashDao cashDao();

    public abstract TransactionDao transactionDao();

    public abstract SelfReminderDao selfReminderDao();

    public abstract InventoryDao inventoryDao();

    public abstract BankAccountDao bankAccountDao();

    public abstract CashCategoryDao cashCategoryDao();

    public abstract UserTransactionsDao userTransactionsDao();

    public abstract ProductCategoryDao productCategoryDao();

    public static Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("DROP TABLE IF EXISTS kb_user_numbers");
            db.execSQL("DROP TABLE IF EXISTS collection_links");
            db.execSQL("DROP TABLE IF EXISTS collection_accounts");
            db.execSQL("DROP TABLE IF EXISTS received_customers");
            db.execSQL("DROP TABLE IF EXISTS received_transactions");
            db.execSQL("CREATE TABLE IF NOT EXISTS `cash_category` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `cash_category_id` TEXT NOT NULL, `book_id` TEXT, `name` TEXT, `deleted` INTEGER,`type` INTEGER, `last_modified_at` INTEGER, `balance` REAL, `due_date` TEXT, `language` INTEGER, PRIMARY KEY(`cash_category_id`))");
            db.execSQL("CREATE TABLE IF NOT EXISTS `cash_transactions` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `cash_transaction_id` TEXT NOT NULL, `book_id` TEXT, `cash_category_id` TEXT, `amount` REAL, `date` TEXT, `description` TEXT, `deleted` INTEGER, `attachments` TEXT, `attachments_upload_pending` INTEGER, PRIMARY KEY(`cash_transaction_id`))");
        }
    };

    public static Migration MIGRATION_2_3 = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE cash_transactions ADD COLUMN `buying_price` REAL DEFAULT 0");
            db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_items` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `quantity` INTEGER,`type` INTEGER,`product_id` TEXT NOT NULL,`transaction_id` TEXT NOT NULL, PRIMARY KEY(`transaction_id`,`product_id`))");
            db.execSQL("CREATE TABLE IF NOT EXISTS `products` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER,`deleted` INTEGER,`category` INTEGER, `stock` INTEGER, `unit_price` REAL DEFAULT 0,`name` TEXT NOT NULL,`product_id` TEXT NOT NULL, `book_id` TEXT NOT NULL,PRIMARY KEY(`product_id`))");
        }
    };

    public static Migration MIGRATION_3_4 = new Migration(3, 4) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE books ADD COLUMN `enabled_payment` INTEGER DEFAULT 0");
        }
    };

    public static Migration MIGRATION_4_5 = new Migration(4, 5) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE transactions ADD COLUMN `transaction_type` TEXT DEFAULT 'DEFAULT'");
            db.execSQL("ALTER TABLE transactions ADD COLUMN `payment_disbursable_id` TEXT");
            db.execSQL("ALTER TABLE books ADD COLUMN `book_type_name` TEXT");
        }
    };

    public static Migration MIGRATION_5_6 = new Migration(5, 6) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("CREATE TABLE IF NOT EXISTS `self_reminder` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `hour` INTEGER,`minute` INTEGER,`is_active` INTEGER,`reminder_id` TEXT NOT NULL,`reminder_category` INTEGER NOT NULL,`notes` TEXT, PRIMARY KEY(`reminder_id`))");
        }
    };

    public static Migration MIGRATION_6_7 = new Migration(6, 7) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE books ADD COLUMN `is_guest` INTEGER DEFAULT 0");
        }
    };

    public static Migration MIGRATION_7_8 = new Migration(7, 8) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE transactions ADD COLUMN `is_offline` INTEGER DEFAULT 0");
            db.execSQL("ALTER TABLE cash_transactions ADD COLUMN `is_offline` INTEGER DEFAULT 0");
        }
    };

    public static Migration MIGRATION_8_9 = new Migration(8, 9) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("CREATE TABLE IF NOT EXISTS `inventory_history` (`history_id` TEXT NOT NULL,`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `current_stock` INTEGER DEFAULT 0,`minimum_stock` INTEGER DEFAULT 0,`quantity_change` INTEGER DEFAULT 0,`product_id` TEXT NOT NULL,`operation_type` TEXT DEFAULT 'DEFAULT',`book_id` TEXT NOT NULL, PRIMARY KEY(`history_id`))");
            db.execSQL("CREATE TABLE IF NOT EXISTS `measurements` (`measurement_id` TEXT NOT NULL,`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `measurement_name` TEXT NOT NULL, `is_default` INTEGER DEFAULT 0, `book_id` TEXT NOT NULL, PRIMARY KEY(`measurement_id`))");
            db.execSQL("ALTER TABLE products ADD COLUMN `minimum_stock` INTEGER DEFAULT 0");
            db.execSQL("ALTER TABLE products ADD COLUMN `track_inventory` INTEGER DEFAULT 0");
            db.execSQL("ALTER TABLE products ADD COLUMN `measurement_name` TEXT DEFAULT 'Pcs'");
            db.execSQL("ALTER TABLE products ADD COLUMN `measurement_id` TEXT DEFAULT '5'");
            db.execSQL("ALTER TABLE products ADD COLUMN `code` TEXT");
        }
    };

    public static Migration MIGRATION_9_10 = new Migration(9, 10) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `customer_id` TEXT");
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `customer_transaction_id` TEXT");
            database.execSQL("ALTER TABLE transaction_items ADD COLUMN `inventory_history_id` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `business_logo` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `business_logo_upload_pending` INTEGER DEFAULT 0");
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `status` INTEGER NOT NULL DEFAULT -1");
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `customer_name` TEXT");
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `mobile_number` TEXT");
        }
    };

    public static Migration MIGRATION_10_11 = new Migration(10, 11) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `order_id` TEXT");
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `transaction_type` TEXT DEFAULT 'DEFAULT'");
        }
    };

    public static Migration MIGRATION_11_12 = new Migration(11, 12) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("CREATE TABLE IF NOT EXISTS `products_new` (" +
                    "`created_at` INTEGER," +
                    "`created_by_user` TEXT," +
                    "`created_by_device` TEXT," +
                    "`updated_at` INTEGER," +
                    "`updated_by_user` TEXT," +
                    "`updated_by_device` TEXT," +
                    "`dirty` INTEGER," +
                    "`server_seq` INTEGER," +
                    "`deleted` INTEGER," +
                    "`category` INTEGER," +
                    "`stock` REAL DEFAULT 0," +
                    "`initial_stock` REAL DEFAULT 0," +
                    "`unit_price` REAL DEFAULT 0," +
                    "`name` TEXT NOT NULL," +
                    "`product_id` TEXT NOT NULL," +
                    "`book_id` TEXT NOT NULL," +
                    "`minimum_stock` INTEGER DEFAULT 0," +
                    "`track_inventory` INTEGER DEFAULT 0," +
                    "`measurement_name` TEXT DEFAULT 'Pcs'," +
                    "`measurement_id` TEXT DEFAULT '5'," +
                    "`code` TEXT," +
                    "PRIMARY KEY(`product_id`))");

            database.execSQL("INSERT INTO `products_new` (" +
                    "`created_at`," +
                    "`created_by_user`," +
                    "`created_by_device`," +
                    "`updated_at`," +
                    "`updated_by_user`," +
                    "`updated_by_device`," +
                    "`dirty`," +
                    "`server_seq`," +
                    "`deleted`,`category`," +
                    "`stock`, `unit_price`," +
                    "`name`,`product_id`," +
                    "`book_id`," +
                    "`product_id`, " +
                    "`minimum_stock`, " +
                    "`track_inventory`," +
                    "`measurement_name`," +
                    "`measurement_id`," +
                    "`code`) SELECT `created_at`, `created_by_user`, `created_by_device`, `updated_at`, `updated_by_user`, `updated_by_device`, `dirty`, `server_seq`,`deleted`,`category`, `stock`, `unit_price`,`name`,`product_id`, `book_id`,`product_id`, `minimum_stock`, `track_inventory`, `measurement_name`, `measurement_id`, `code` FROM products");
            database.execSQL("DROP TABLE  products");
            database.execSQL("ALTER TABLE products_new RENAME TO products");

            database.execSQL("CREATE TABLE IF NOT EXISTS `inventory_history_new` (" +
                    "`history_id` TEXT NOT NULL," +
                    "`created_at` INTEGER," +
                    "`created_by_user` TEXT," +
                    "`created_by_device` TEXT," +
                    "`updated_at` INTEGER," +
                    "`updated_by_user` TEXT," +
                    "`updated_by_device` TEXT," +
                    "`dirty` INTEGER," +
                    "`server_seq` INTEGER," +
                    "`current_stock` REAL DEFAULT 0," +
                    "`minimum_stock` INTEGER DEFAULT 0," +
                    "`quantity_change` REAL DEFAULT 0," +
                    "`product_id` TEXT NOT NULL," +
                    "`operation_type` TEXT DEFAULT 'DEFAULT'," +
                    "`book_id` TEXT NOT NULL, PRIMARY KEY(`history_id`))");

            database.execSQL("INSERT INTO `inventory_history_new` (" +
                    "`history_id`," +
                    "`created_at`," +
                    "`created_by_user`," +
                    "`created_by_device`," +
                    "`updated_at`," +
                    "`updated_by_user`," +
                    "`updated_by_device`," +
                    "`dirty`,`server_seq`," +
                    "`current_stock`," +
                    "`minimum_stock`," +
                    "`quantity_change`," +
                    "`product_id`," +
                    "`operation_type`," +
                    "`book_id`) SELECT `history_id`, `created_at`, `created_by_user`, `created_by_device`, `updated_at`, `updated_by_user`, `updated_by_device`, `dirty`,`server_seq`,`current_stock`, `minimum_stock`, `quantity_change`,`product_id`,`operation_type`, `book_id` FROM inventory_history");
            database.execSQL("DROP TABLE  inventory_history");
            database.execSQL("ALTER TABLE inventory_history_new RENAME TO inventory_history");

            database.execSQL("CREATE TABLE IF NOT EXISTS `transaction_items_new` (" +
                    "`created_at` INTEGER," +
                    "`created_by_user` TEXT," +
                    "`created_by_device` TEXT," +
                    "`updated_at` INTEGER," +
                    "`updated_by_user` TEXT," +
                    "`updated_by_device` TEXT," +
                    "`dirty` INTEGER," +
                    "`server_seq` INTEGER," +
                    "`quantity` REAL," +
                    "`type` INTEGER," +
                    "`product_id` TEXT NOT NULL," +
                    "`transaction_id` TEXT NOT NULL," +
                    "`inventory_history_id` TEXT," +
                    "PRIMARY KEY(`transaction_id`,`product_id`))");

            database.execSQL("INSERT INTO `transaction_items_new` (" +
                    "`created_at`," +
                    "`created_by_user`," +
                    "`created_by_device`," +
                    "`updated_at`," +
                    "`updated_by_user`," +
                    "`updated_by_device`," +
                    "`dirty`," +
                    "`server_seq`," +
                    "`quantity`," +
                    "`type`," +
                    "`product_id`," +
                    "`transaction_id`," +
                    "`inventory_history_id`) " +
                    "SELECT `created_at`," +
                    "`created_by_user`," +
                    "`created_by_device`," +
                    "`updated_at`," +
                    "`updated_by_user`," +
                    "`updated_by_device`," +
                    "`dirty`," +
                    "`server_seq`," +
                    "`quantity`," +
                    "`type`," +
                    "`product_id`," +
                    "`transaction_id`," +
                    "`inventory_history_id`" +
                    "FROM transaction_items");
            database.execSQL("DROP TABLE  transaction_items");
            database.execSQL("ALTER TABLE transaction_items_new RENAME TO transaction_items");
        }
    };

    public static Migration MIGRATION_12_13 = new Migration(12, 13) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `custom_amount` REAL DEFAULT 0");
            database.execSQL("CREATE TABLE IF NOT EXISTS `bank_account` (`created_at` INTEGER, `created_by_user` TEXT, `created_by_device` TEXT, `updated_at` INTEGER, `updated_by_user` TEXT, `updated_by_device` TEXT, `dirty` INTEGER, `server_seq` INTEGER, `bank_account_id` TEXT NOT NULL,`account_id` TEXT, `bank_code` TEXT, `bank_name` TEXT, `account_number` TEXT, `customer_id` TEXT, `account_holder_name` TEXT, `is_selected` INTEGER NOT NULL, PRIMARY KEY(`bank_account_id`))");
        }
    };

    public static Migration MIGRATION_13_14 = new Migration(13, 14) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE bank_account ADD COLUMN `flag` TEXT DEFAULT 'ACTIVE'");
            database.execSQL("ALTER TABLE bank_account ADD COLUMN `message` TEXT");
        }
    };

    /**
     * Added buying price in products table
     */
    public static Migration MIGRATION_14_15 = new Migration(14, 15) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE products ADD COLUMN `buying_price` REAL DEFAULT 0");
        }
    };

    /**
     * Added payment_method in cash_transaction table to tag POS payment
     */
    public static Migration MIGRATION_15_16 = new Migration(15, 16) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `payment_method` TEXT " +
                    "DEFAULT 'CASH'");
        }
    };


    public static Migration MIGRATION_16_17 = new Migration(16, 17) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("CREATE TABLE IF NOT EXISTS `user_profile` " +
                    "(`created_at` TEXT DEFAULT 0 NOT NULL, " +
                    "`created_by_user` TEXT, " +
                    "`created_by_device` TEXT, " +
                    "`updated_at` TEXT DEFAULT 0 NOT NULL, " +
                    "`updated_by_user` TEXT, " +
                    "`updated_by_device` TEXT, " +
                    "`dirty` INTEGER DEFAULT 0 NOT NULL, " +
                    "`server_seq` INTEGER DEFAULT 0 NOT NULL, " +
                    "`user_id` TEXT NOT NULL," +
                    "`user_name` TEXT," +
                    "`user_phone` TEXT NOT NULL," +
                    "`user_email` TEXT," +
                    "`dob` TEXT, " +
                    "`user_profile_image` TEXT, " +
                    "PRIMARY KEY(`user_id`))");
        }
    };

    public static Migration MIGRATION_17_18 = new Migration(17, 18) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE books ADD COLUMN `is_daily_business_update_seen` INTEGER NOT NULL DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_18_19 = new Migration(18, 19) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE books ADD COLUMN `operating_hours_start` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `operating_hours_end` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `operating_days` TEXT");

            database.execSQL("ALTER TABLE books ADD COLUMN `establishment_year` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `outlet_count` INTEGER DEFAULT(0)");
            database.execSQL("ALTER TABLE books ADD COLUMN `emp_count` INTEGER  DEFAULT(0)");
            database.execSQL("ALTER TABLE books ADD COLUMN `profile_completion_progress` INTEGER  DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_19_20 = new Migration(19, 20) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE products ADD COLUMN `has_updated_price` INTEGER NOT NULL DEFAULT(0)");
            database.execSQL("ALTER TABLE products ADD COLUMN `is_imported_from_catalog` INTEGER NOT NULL DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_20_21 = new Migration(20, 21) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE books ADD COLUMN `province` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `city` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `district` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `subdistrict` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `postal_code` TEXT");
        }
    };

    public static Migration MIGRATION_21_22 = new Migration(21, 22) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE books ADD COLUMN `production` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `product_buyer` TEXT");
            database.execSQL("ALTER TABLE books ADD COLUMN `monthly_turnover` INTEGER  DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_22_23 = new Migration(22, 23) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE bank_account ADD COLUMN `is_qris_bank` INTEGER DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_23_24 = new Migration(23, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_transactions ADD COLUMN `brick_institution_id` INTEGER NOT NULL DEFAULT(-1)");
        }
    };

    public static Migration MIGRATION_24_25 = new Migration(24, 25) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE products ADD COLUMN `favourite` INTEGER NOT NULL DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_25_26 = new Migration(25, 26) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE user_profile ADD COLUMN `gender` TEXT");
        }
    };


    // added for ProductCategory task
    public static Migration MIGRATION_26_27 = new Migration(26, 27) {
        @Override
        public void migrate(@NonNull @NotNull SupportSQLiteDatabase database) {
            database.execSQL("CREATE TABLE IF NOT EXISTS `product_category` (" +
                    "`category_id` TEXT NOT NULL," +
                    "`name` TEXT NOT NULL," +
                    "`book_id` TEXT NOT NULL," +
                    "`logo_url` TEXT," +
                    "`created_at` INTEGER," +
                    "`created_by_device` TEXT," +
                    "`created_by_user` TEXT," +
                    "`dirty` INTEGER," +
                    "`server_seq` INTEGER," +
                    "`updated_at` INTEGER," +
                    "`updated_by_device` TEXT," +
                    "`updated_by_user` TEXT," +
                    "`deleted` INTEGER NOT NULL," +
                    "PRIMARY KEY(`category_id`)" +
                    ")"
            );

            database.execSQL("CREATE TABLE IF NOT EXISTS `product_category_cross_ref`(" +
                    "`product_id` TEXT NOT NULL," +
                    "`category_id` TEXT NOT NULL," +
                    "`created_at` INTEGER," +
                    "`created_by_device` TEXT," +
                    "`created_by_user` TEXT," +
                    "`dirty` INTEGER," +
                    "`server_seq` INTEGER," +
                    "`updated_at` INTEGER," +
                    "`updated_by_device` TEXT," +
                    "`updated_by_user` TEXT," +
                    "`book_id` TEXT NOT NULL," +
                    "`deleted` INTEGER NOT NULL," +
                    "PRIMARY KEY(`product_id`, `category_id`)" +
                    ")"
            );
        }
    };
    public static Migration MIGRATION_27_28 = new Migration(27, 28) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE cash_category ADD COLUMN `frequency` INTEGER");
        }
    };

    public static Migration MIGRATION_28_29 = new Migration(28, 29) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE transaction_items ADD COLUMN `name` TEXT NOT NULL DEFAULT ''");
            db.execSQL("ALTER TABLE transaction_items ADD COLUMN `buying_price` REAL NOT NULL DEFAULT 0");
            db.execSQL("ALTER TABLE transaction_items ADD COLUMN `selling_price` REAL NOT NULL DEFAULT 0");
            db.execSQL("ALTER TABLE transaction_items ADD COLUMN `measurement_name` TEXT NOT NULL DEFAULT ''");
        }
    };

    public static Migration MIGRATION_29_30 = new Migration(29, 30) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE inventory_history ADD COLUMN `buying_price` REAL NOT NULL DEFAULT 0");
            db.execSQL("ALTER TABLE inventory_history ADD COLUMN `selling_price` REAL NOT NULL DEFAULT 0");
            db.execSQL("ALTER TABLE inventory_history ADD COLUMN `measurement_name` TEXT NOT NULL DEFAULT ''");
        }
    };

    public static Migration MIGRATION_30_31 = new Migration(30, 31) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE bank_account ADD COLUMN `is_disabled` INTEGER DEFAULT(0)");
        }
    };

    public static Migration MIGRATION_31_32 = new Migration(31, 32) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE bank_account ADD COLUMN `matching_status` INTEGER DEFAULT(0)");
            db.execSQL("ALTER TABLE bank_account ADD COLUMN `account_relation` TEXT");
        }
    };

    public static Migration MIGRATION_32_33 = new Migration(32, 33) {
        @Override
        public void migrate(SupportSQLiteDatabase db) {
            db.execSQL("ALTER TABLE cash_transactions ADD COLUMN `restore_transaction_items` INTEGER NOT NULL DEFAULT(0)");
        }
    };

    public static AppDatabase getDatabase(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {

                    INSTANCE = Room.databaseBuilder(context.getApplicationContext(), AppDatabase.class, "khatabook-main")
                            .allowMainThreadQueries()
                            .addMigrations(MIGRATION_1_2)
                            .addMigrations(MIGRATION_2_3)
                            .addMigrations(MIGRATION_3_4)
                            .addMigrations(MIGRATION_4_5)
                            .addMigrations(MIGRATION_5_6)
                            .addMigrations(MIGRATION_6_7)
                            .addMigrations(MIGRATION_7_8)
                            .addMigrations(MIGRATION_8_9)
                            .addMigrations(MIGRATION_9_10)
                            .addMigrations(MIGRATION_10_11)
                            .addMigrations(MIGRATION_11_12)
                            .addMigrations(MIGRATION_12_13)
                            .addMigrations(MIGRATION_13_14)
                            .addMigrations(MIGRATION_14_15)
                            .addMigrations(MIGRATION_15_16)
                            .addMigrations(MIGRATION_16_17)
                            .addMigrations(MIGRATION_17_18)
                            .addMigrations(MIGRATION_18_19)
                            .addMigrations(MIGRATION_19_20)
                            .addMigrations(MIGRATION_20_21)
                            .addMigrations(MIGRATION_21_22)
                            .addMigrations(MIGRATION_22_23)
                            .addMigrations(MIGRATION_23_24)
                            .addMigrations(MIGRATION_24_25)
                            .addMigrations(MIGRATION_25_26)
                            .addMigrations(MIGRATION_26_27)
                            .addMigrations(MIGRATION_27_28)
                            .addMigrations(MIGRATION_28_29)
                            .addMigrations(MIGRATION_29_30)
                            .addMigrations(MIGRATION_30_31)
                            .addMigrations(MIGRATION_31_32)
                            .addMigrations(MIGRATION_32_33)
                            .build();
                }
            }
        }
        return INSTANCE;
    }
}