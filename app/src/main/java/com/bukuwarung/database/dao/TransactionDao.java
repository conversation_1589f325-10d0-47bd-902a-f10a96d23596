package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.paging.DataSource;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.bukuwarung.activities.expense.category.Category;
import com.bukuwarung.database.dto.CashTransactionDto;
import com.bukuwarung.database.dto.FrequentProductDto;
import com.bukuwarung.database.dto.ReportTranasctionModel;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.ProductCategoryEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.entity.TransactionItemsEntity;

import java.util.List;

import javax.annotation.Nullable;

@Dao
public abstract class TransactionDao {

    @Insert
    public abstract Long insert(TransactionEntity transactionEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insertCashTransaction(CashTransactionEntity cashTransactionEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract List<Long> insertCashTransaction(List<CashTransactionEntity> cashTransactionEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertCashTransactions(List<CashTransactionEntity> cashTransactionEntities);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract Long retryCashTransaction(CashTransactionEntity cashTransactionEntity);

    @Query("SELECT * FROM (SELECT transaction_id, customer_id, amount, 0 as buying_price, date, description FROM transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :businessId AND tt.date >= :startDate AND tt.date <= :endDate ORDER BY tt.date DESC, tt.created_at DESC) AS t JOIN (SELECT name, customer_id FROM customers AS cc WHERE cc.deleted=0) AS c ON c.customer_id=t.customer_id")
    public abstract List<ReportTranasctionModel> getTransactionByBusiness(String businessId, String startDate, String endDate);

    @Query("SELECT * FROM transactions WHERE customer_id=:customerId AND deleted=0 ORDER BY date DESC, created_at DESC")
    public abstract LiveData<List<TransactionEntity>> getTransactionListByCustomerId(String customerId);

    @Query("SELECT * FROM cash_transactions WHERE cash_category_id=:categoryId AND deleted=0 ORDER BY date DESC, created_at DESC")
    public abstract LiveData<List<CashTransactionEntity>> getTransactionListByCategoryId(String categoryId);

    @Query("SELECT * FROM cash_transactions WHERE cash_category_id=:categoryId AND date >= :startDate AND date <= :endDate AND deleted=0 ORDER BY date DESC, created_at DESC")
    public abstract LiveData<List<CashTransactionEntity>> getTransactionListByCategoryIdWithDate(String categoryId, String startDate, String endDate);

    @Query("SELECT * FROM cash_transactions WHERE brick_institution_id=:brickInstitutionId AND date >= :startDate AND date <= :endDate AND cash_category_id LIKE '%' || :str || '%'")
    public abstract CashCategoryEntity getCashCategoryByIdForBrick(String brickInstitutionId, String str,String startDate,String endDate);

     @Query("SELECT * FROM cash_transactions WHERE brick_institution_id=:brickInstitutionId AND date >= :startDate AND date <= :endDate AND cash_category_id LIKE '%' || :categoryId || '%'")
    public abstract LiveData<List<CashTransactionEntity>> getTransactionListByBrickInstitutionIdWithDate(String categoryId,String brickInstitutionId,String startDate,String endDate);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, transaction_type FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract DataSource.Factory<Integer, CashTransactionDto> getCashTransactionsForViewPaging(String book_id);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, transaction_type FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.date >= :startDate AND tt.date <= :endDate ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract DataSource.Factory<Integer, CashTransactionDto> getCashTransactionsForViewWithRangePaging(String book_id, String startDate, String endDate);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getCashTransactionsForView(String book_id);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type, brick_institution_id FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getAutoRecordCreditTransactionsForView(String book_id);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type, brick_institution_id FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.transaction_type = :transactionEntityType ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getAllAutoRecordTransactionsForView(String book_id,TransactionEntityType transactionEntityType);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract LiveData<List<CashTransactionDto>> getCashTransactionsLiveDataForView(String book_id);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.date >= :startDate AND tt.date <= :endDate ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getCashTransactionsForViewWithRange(String book_id, String startDate, String endDate);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.date >= :startDate AND tt.date <= :endDate ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getAutoRecordCreditTransactionsForViewWithRange(String book_id, String startDate, String endDate);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price,date, created_at, updated_at, description, status, customer_id, customer_name, mobile_number, transaction_type, brick_institution_id FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.date >= :startDate AND tt.date <= :endDate AND tt.transaction_type = :transactionEntityType ORDER BY tt.date ASC, tt.created_at ASC) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<CashTransactionDto> getAllAutoRecordTransactionsForViewWithRange(String book_id, String startDate, String endDate,TransactionEntityType transactionEntityType);

    @Query("SELECT * FROM (SELECT cash_transaction_id, cash_category_id, amount, buying_price, date, description FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :book_id AND tt.date >= :startDate AND tt.date <= :endDate ORDER BY tt.date DESC, tt.created_at DESC) AS t JOIN (SELECT name, cash_category_id FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract List<ReportTranasctionModel> getCashTransactionByBusiness(String book_id, String startDate, String endDate);

    @Query("SELECT * FROM cash_transactions WHERE cash_transaction_id=:cashTransactionId")
    public abstract CashTransactionEntity getCashTransactionById(String cashTransactionId);

    @Query("SELECT * FROM cash_transactions WHERE cash_transaction_id=:cashTransactionId AND deleted=0")
    public abstract LiveData<CashTransactionEntity> getObservableCashTransactionById(String cashTransactionId);

    @Query("SELECT * FROM cash_transactions WHERE cash_transaction_id=:cashTransactionId AND date >= :startDate AND date <= :endDate AND deleted=0")
    public abstract LiveData<CashTransactionEntity> getObservableCashTransactionByIdDate(String cashTransactionId, String startDate, String endDate);

    @Query("UPDATE cash_transactions SET amount=:amount,date=:date,description=:description,deleted=:deleted,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,dirty=1 WHERE cash_transaction_id=:transactionId")
    public abstract int updateCashTransaction(String transactionId, Double amount, String date, String description, Integer deleted, Long updateTime, String updatedBy, String device);

    @Query("UPDATE cash_transactions SET cash_category_id=:categoryId, deleted=:deleted,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,dirty=1 WHERE cash_transaction_id IN (:transactionIds)")
    public abstract int updateCashTransactions(Integer deleted, Long updateTime, String updatedBy, String device, List<String> transactionIds, String categoryId);

    @Query("DELETE FROM cash_transactions WHERE cash_transaction_id=:transactionId")
    public abstract int hardDeleteCashTransaction(String transactionId);

    @Query("UPDATE cash_transactions SET amount=:amount,buying_price=:buyingPrice,date=:date,description=:description,deleted=:deleted,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,dirty=1 WHERE cash_transaction_id=:transactionId")
    public abstract int updateCashTransaction(String transactionId, Double amount, String date, String description, Integer deleted, Long updateTime, String updatedBy, String device, Double buyingPrice);

    @Query("UPDATE cash_transactions SET amount=:amount,buying_price=:buyingPrice,date=:date,description=:description,customer_transaction_id=:customerTransactionId,status=:status,deleted=:deleted,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,customer_id=:customerId,customer_name=:customerName, mobile_number=:customerPhone, dirty=1 WHERE cash_transaction_id=:transactionId")
    public abstract int updateCashTransaction(String transactionId, Double amount, String date, String description, Integer deleted, Long updateTime, String updatedBy, String device, Double buyingPrice, @Nullable String customerTransactionId, int status, String customerId, String customerName, String customerPhone);

    @Query("UPDATE cash_transactions SET amount=:amount,buying_price=:buyingPrice,date=:date,description=:description,customer_transaction_id=:customerTransactionId,status=:status,deleted=:deleted,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,customer_id=:customerId,customer_name=:customerName, mobile_number=:customerPhone, dirty=1, cash_category_id=:categoryId, attachments=:attachment WHERE cash_transaction_id=:transactionId")
    public abstract int updateCashTransactionWithCategory(String transactionId, Double amount, String date, String description, Integer deleted, Long updateTime, String updatedBy, String device, Double buyingPrice, @Nullable String customerTransactionId, int status, String customerId, String customerName, String customerPhone, String categoryId, String attachment);


    @Query("SELECT * FROM transactions WHERE transaction_id=:transactionId")
    public abstract TransactionEntity getTransactionById(String transactionId);

    @Query("SELECT * FROM cash_transactions WHERE customer_transaction_id=:customerTransactionId")
    public abstract CashTransactionEntity getCashTransactionByCusutomerTransactionId(String customerTransactionId);

    @Query("SELECT * FROM transactions WHERE transaction_id=:transactionId")
    public abstract LiveData<TransactionEntity> getObservableTransactionById(String transactionId);

    @Query("UPDATE transactions SET amount=:amount,date=:date,description=:description,deleted=:deleted,sms_status=:smsStatus,updated_at=:updateTime,updated_by_user=:updatedBy,updated_by_device=:device,dirty=1 WHERE transaction_id=:transactionId")
    public abstract int updateById(String transactionId, Double amount, String date, String description, Integer deleted, Integer smsStatus, Long updateTime, String updatedBy, String device);

    @Query("SELECT count(*) FROM cash_transactions WHERE deleted=0")
    public abstract int countCashTransactions();

    @Query("SELECT count(*) FROM cash_transactions WHERE deleted=0 and book_id=:bookId")
    public abstract int countCashTransactionsByBook(String bookId);

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0 and book_id=:bookId")
    public abstract int countUtangTransactionsByBook(String bookId);

    @Query("SELECT count(*) FROM cash_transactions WHERE deleted=0 and date=:date")
    public abstract int countCashTransactionsByDate(String date);

    @Query("SELECT count(*) FROM transactions WHERE deleted=0 and date=:date")
    public abstract int countUtangTransactionsByDate(String date);

    @Query("SELECT COUNT(*) FROM (SELECT * FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :bookId) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract int countBusinessCashTransactions(String bookId);

    @Query("SELECT COUNT(*) FROM (SELECT * FROM cash_transactions AS tt WHERE tt.deleted=0 AND tt.book_id = :bookId AND tt.transaction_type = :transactionEntityType) AS t JOIN (SELECT name, cash_category_id, type FROM cash_category AS cc WHERE cc.deleted=0) AS c ON c.cash_category_id=t.cash_category_id")
    public abstract int countAutoRecordTransactions(String bookId,TransactionEntityType transactionEntityType);

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0")
    public abstract int countUtangTransactions();

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0 AND book_id=:bookId and date=:date")
    public abstract int countUtangTransactionsWithBookIdAndDate(String bookId, String date);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE deleted=0 AND book_id=:bookId and date=:date")
    public abstract int countCashTransactionsWithBookIdAndDate(String bookId, String date);

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0 AND book_id=:bookId and date=:date and amount > 0")
    public abstract int countUtangSalesTransactionsWithBookIdAndDate(String bookId, String date);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE deleted=0 AND book_id=:bookId and date=:date and amount > 0")
    public abstract int countCashSalesTransactionsWithBookIdAndDate(String bookId, String date);

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract int countUtangTransactionsWithBookId(String bookId);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract int countCashTransactionsWithBookId(String bookId);

    @Query("SELECT COUNT(*) FROM transactions WHERE amount < 0 AND created_by_user=:userId")
    public abstract int countNegativeAmountUtangTransactionsWithDeleted(String userId);

    @Query("SELECT COUNT(*) FROM transactions where created_by_user=:userId")
    public abstract int countUtangTransactionsWithDeletedForUserId(String userId);

    @Query("SELECT COUNT(DISTINCT customer_id) FROM transactions where created_by_user=:userId")
    public abstract int countUniqueUtangTransactionsWithDeletedForUserId(String userId);

    @Query("SELECT COUNT(*) FROM cash_transactions where created_by_user=:userId")
    public abstract int countCashTransactionsWithDeletedForUserId(String userId);

    @Query("SELECT COUNT(*) FROM cash_transactions where created_by_user=:userId AND transaction_type=:transactionType")
    public abstract int countPosTransactionsWithDeletedForUserId(String userId, TransactionEntityType transactionType);

    @Query("SELECT COUNT(*) FROM cash_transactions where created_by_user=:userId AND cash_category_id=:categoryId")
    public abstract int countCashTransactionsWithDeletedAndCategoryIdForUserId(String userId,String categoryId);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE created_by_user=:userId AND (description !='' OR cash_category_id != (SELECT cash_category_id FROM cash_category where created_by_user=:userId AND (name = 'Pengeluaran' OR name = 'Pemasukan'))) ")
    public abstract int countCashTransactionsWithDeletedHavingNotesOrCategoryForUserId(String userId);

    @Query("SELECT COUNT(*) FROM cash_transactions where created_by_user=:userId AND status=:status AND amount>=0")
    public abstract int countCashTransactionsWithDeletedAndProfitForUserIdByStatus(String userId, int status);

    @Query("SELECT COUNT(*) FROM cash_transactions where created_by_user=:userId AND status=:status")
    public abstract int countCashTransactionsWithDeletedForUserIdByStatus(String userId, int status);

    @Query("SELECT COUNT(*) FROM transactions WHERE amount < 0 AND book_id in (select book_id from books where is_guest=1 and book_id=:bookId)")
    public abstract int countGuestNegativeAmountUtangTransactionsWithDeleted(String bookId);

    @Query("SELECT COUNT(*) FROM transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId)")
    public abstract int countUtangTransactionsWithDeletedForGuest(String bookId);

    @Query("SELECT COUNT(DISTINCT customer_id) FROM transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId)")
    public abstract int countUniqueUtangTransactionsWithDeletedForGuest(String bookId);

    @Query("SELECT count(*) FROM cash_transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId)")
    public abstract int countCashTransactionsWithDeletedForGuest(String bookId);

    @Query("SELECT count(*) FROM cash_transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId) AND cash_category_id=:categoryId")
    public abstract int countCashTransactionsWithDeletedAndCategoryIdForGuest(String bookId,String categoryId);

    @Query("SELECT count(*) FROM cash_transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId) " +
            "AND (description !='' or cash_category_id NOT IN (SELECT cash_category_id FROM cash_category where name = 'Pengeluaran' OR name = 'Pemasukan'))")
    public abstract int countCashTransactionsWithDeletedHavingNotesOrCategoryForGuest(String bookId);

    @Query("SELECT count(*) FROM cash_transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId) AND status=:status AND amount>=0")
    public abstract int countCashTransactionsWithDeletedAndProfitForGuestByStatus(String bookId, int status);

    @Query("SELECT count(*) FROM cash_transactions where book_id in (select book_id from books where is_guest=1 and book_id=:bookId) AND status=:status")
    public abstract int countCashTransactionsWithDeletedForGuestByStatus(String bookId, int status);

    @Query("SELECT COUNT(*) FROM transactions WHERE deleted=0 AND created_by_user=:userId")
    public abstract LiveData<Integer> countUtangTransactionsLiveData(String userId);

    @Query("SELECT (SELECT COUNT(*) FROM transactions WHERE deleted=0 AND created_by_user=:userId) + (SELECT count(*) FROM cash_transactions WHERE deleted=0 AND created_by_user=:userId)")
    public abstract LiveData<Integer> countAllTrxLiveData(String userId);

    @Query("SELECT (SELECT COUNT(*) FROM transactions WHERE deleted=0 AND created_by_user=:userId) + (SELECT count(*) FROM cash_transactions WHERE deleted=0 AND created_by_user=:userId)")
    public abstract int countAllTrx(String userId);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE book_id=:str AND deleted=0 AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract int countAllCashTransaction(String str);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE book_id=:bookId AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract int countAllCashTransactionWithDeleted(String bookId);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertCreditTransaction(TransactionEntity transactionEntity);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void retryCreditTransaction(TransactionEntity transactionEntity);

    @Query("SELECT MIN(date) FROM transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract String getMinCreditTransacionDate(String bookId);

    @Query("SELECT MAX(date) FROM transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract String getMaxCreditTransacionDate(String bookId);

    @Query("SELECT MAX(date) FROM cash_transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract String getMaxExpenseTransacionDate(String bookId);

    @Query("SELECT MIN(date) FROM cash_transactions WHERE deleted=0 AND book_id=:bookId")
    public abstract String getMinExpenseTransacionDate(String bookId);

    @Query("SELECT * FROM cash_transactions WHERE cash_category_id=:cashCategoryId AND book_id=:businessId ORDER BY date DESC, created_at DESC")
    public abstract List<CashTransactionEntity> getAllCategoryTransactions(String cashCategoryId, String businessId);

    @Query("SELECT * FROM cash_transactions WHERE cash_category_id=:cashCategoryId AND book_id=:businessId AND date >= :sdate AND date <= :edate ORDER BY date DESC, created_at DESC")
    public abstract List<CashTransactionEntity> getCategoryTransactionsByRange(String cashCategoryId, String businessId, String sdate, String edate);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE cash_category_id=:cashCategoryId AND book_id=:businessId AND deleted=0")
    public abstract int countAllCategoryTransactions(String cashCategoryId, String businessId);


    @Query("SELECT COUNT(*) FROM cash_transactions WHERE cash_category_id in (SELECT cash_category_id from cash_category WHERE type = 1 and deleted=0) AND book_id=:str AND amount>0 AND deleted=0")
    public abstract int countAllIncomeTransaction(String str);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE book_id=:str AND cash_category_id=:cashCatId AND deleted=0 AND date >= :startDate AND date <= :endDate")
    public abstract int countIncomeTransactionWithId(String str,String cashCatId,String startDate,String endDate);

    @Query("SELECT SUM(amount) FROM cash_transactions WHERE book_id=:str AND cash_category_id=:cashCatId AND deleted=0 AND date >= :startDate AND date <= :endDate")
    public abstract int getCategoryAmountByDate(String str,String cashCatId,String startDate,String endDate);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE  cash_category_id in (SELECT cash_category_id from cash_category WHERE type = -1 and deleted=0) AND book_id=:str AND amount<0 AND deleted=0")
    public abstract int countAllExpenseTransaction(String str);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE book_id=:str AND cash_category_id=:cashCatId AND deleted=0 AND date >= :startDate AND date <= :endDate")
    public abstract int countExpenseTransactionWithId(String str,String cashCatId,String startDate,String endDate);

    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND type=1 AND deleted=0 ORDER BY frequency DESC")
    public abstract List<CashCategoryEntity> getAllIncomeCategories(String bookId);

    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND type=1 AND deleted=0 AND cash_category_id IN (select cash_category_id from cash_transactions where date>=:startDate and date<=:endDate and deleted =0 GROUP BY cash_category_id)")
    public abstract List<CashCategoryEntity> getAllIncomeCategoriesByDate(String bookId,String startDate,String endDate);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE cash_category_id in (SELECT cash_category_id from cash_category WHERE type = 1 and deleted=0) AND book_id=:bookId AND deleted=0 AND date>=:startDate and date<=:endDate")
    public abstract int getAllIncomeTrxCountWithProductByDate(String bookId,String startDate,String endDate);

    @Query("SELECT COUNT(*) FROM cash_transactions WHERE cash_category_id in (SELECT cash_category_id from cash_category WHERE type = -1 and deleted=0) AND book_id=:bookId AND deleted=0 AND date>=:startDate and date<=:endDate")
    public abstract int getAllExpenseTrxCountWithProductByDate(String bookId,String startDate,String endDate);

    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND type=-1 AND deleted=0 AND cash_category_id IN (select cash_category_id from cash_transactions where date>=:startDate  and date<=:endDate and deleted=0 GROUP BY cash_category_id)")
    public abstract List<CashCategoryEntity> getAllExpenseCategoriesByDate(String bookId,String startDate,String endDate);

    @Query("SELECT COUNT(DISTINCT date) FROM cash_transactions WHERE book_id=:businessId")
    public abstract int countDistinctDateTransaction(String businessId);

    @Query("UPDATE cash_transactions SET deleted=1 WHERE cash_category_id=:cashCategoryId AND book_id=:businessId")
    public abstract void deleteAllCategoryTransactions(String cashCategoryId, String businessId);

    @Query("UPDATE cash_transactions SET cash_category_id=:newCashCategoryId, updated_at=:updatedAt, updated_by_user=:updatedBy,updated_by_device=:device WHERE cash_category_id=:oldCashCategoryId AND book_id=:businessId")
    public abstract void changeCategoryNameToTransactions(String oldCashCategoryId,
                                                          String businessId,
                                                          String newCashCategoryId,
                                                          long updatedAt,
                                                          String updatedBy,
                                                          String device);

    @Query("DELETE FROM cash_transactions WHERE cash_category_id=:cashId")
    public abstract void deleteByCashId(String cashId);

    @Query("SELECT * FROM transactions WHERE book_id=:bookId ORDER BY updated_at ASC")
    public abstract List<TransactionEntity> getAllTransactionByBookId(String bookId);

    @Query("SELECT * FROM cash_transactions WHERE book_id=:bookId AND deleted=0")
    public abstract List<CashTransactionEntity> getAllCashTransactionByBookId(String bookId);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insertTransactionItems(TransactionItemsEntity transactionItemsEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertTransactionItems(List<TransactionItemsEntity> transactionItemsEntityList);

    @Query("SELECT * FROM transaction_items WHERE transaction_id=:transactionId ORDER BY created_at DESC")
    public abstract List<TransactionItemsEntity> getTransactionItems(String transactionId);

    @Query("DELETE FROM transaction_items WHERE transaction_id=:transactionId")
    public abstract void deleteTransactionItems(String transactionId);

    @Query("DELETE FROM cash_transactions WHERE brick_institution_id=:institutionId")
    public abstract void deleteCashTransactionsWithInstitutionId(int institutionId);

    @Query("DELETE FROM cash_transactions WHERE transaction_type=:transactionEntityType")
    public abstract void deleteCashTransactionsWithEntityType(TransactionEntityType transactionEntityType);

    @Query("DELETE FROM inventory_history WHERE history_id in (select inventory_history_id from transaction_items WHERE transaction_id=:transactionId)")
    public abstract void deleteTransactionHistoryItems(String transactionId);

    @Query("SELECT * FROM inventory_history WHERE history_id in (select inventory_history_id from transaction_items WHERE transaction_id=:transactionId)")
    public abstract List<InventoryHistoryEntity> selectTransactionHistoryItems(String transactionId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE amount >= 0 AND NOT deleted AND cash_category_id=:categoryId")
    public abstract Double getTotalSellingPrice(String categoryId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE amount >= 0 AND book_id=:bookId AND date >= :startDate AND date <= :endDate AND NOT deleted")
    public abstract Double getTotalSellingPriceForAllCategories(String bookId,String startDate, String endDate);

    @Query("select COUNT(*) from cash_transactions WHERE book_id=:bookId AND date >= :startDate AND date <= :endDate AND deleted=0 AND amount >= 0 AND cash_category_id in(select cash_category_id from cash_category where type=1 and deleted=0 and book_id=:bookId) AND cash_transaction_id in (select transaction_id from transaction_items)")
    public abstract Integer getTotalTransactionsWithProducts(String bookId,String startDate, String endDate);

    @Query("select COALESCE(ROUND(SUM(buying_price), 2),0) from cash_transactions AS c WHERE amount >= 0 AND NOT deleted AND cash_category_id=:categoryId")
    public abstract Double getTotalBuyingPrice(String categoryId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE cash_category_id in(select cash_category_id from cash_category where type=1 and deleted=0 and book_id=:bookId) AND cash_transaction_id in (select transaction_id from transaction_items) AND amount >= 0 AND book_id=:bookId AND date >= :startDate AND date <= :endDate AND NOT deleted")
    public abstract Double getTotalSellingPriceForAllCategoriesWithProduct(String bookId,String startDate, String endDate);

    @Query("select COALESCE(ROUND(SUM(buying_price), 2),0) from cash_transactions AS c WHERE cash_category_id in(select cash_category_id from cash_category where type=1 and deleted=0 and book_id=:bookId) AND cash_transaction_id in (select transaction_id from transaction_items) AND amount >= 0 AND book_id=:bookId AND date >= :startDate AND date <= :endDate AND NOT deleted")
    public abstract Double getTotalBuyingPriceForAllCategoriesWithProduct(String bookId,String startDate, String endDate);

    @Query("UPDATE cash_transactions SET created_by_user=:userId, updated_by_user=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestCashTxnData(String userId, String bookId);

    @Query("UPDATE transactions SET created_by_user=:userId, updated_by_user=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestCustomerTxnData(String userId, String bookId);

    // to check whether a product already used for transaction
    @Query("SELECT COUNT(*) FROM transaction_items WHERE product_id= :productId AND transaction_id in (select cash_transaction_id from cash_transactions where deleted=0) ")
    public abstract int checkProductUsedForTransaction(String productId);

    @Query("SELECT COUNT(*) FROM transaction_items")
    public abstract int countCashTransactionsWithProduct();

    @Query("UPDATE cash_transactions SET updated_at=:updated_at, status=:status WHERE cash_transaction_id=:cashTransactionId")
    public abstract void updateCashTransactionStatus(String cashTransactionId, int status, long updated_at);

    @Query("UPDATE cash_transactions SET updated_at=:updated_at, order_id=:orderId WHERE cash_transaction_id=:cashTransactionId")
    public abstract void updateCashTransactionOrderId(String cashTransactionId, String orderId, long updated_at);

    @Query("UPDATE cash_transactions SET status=:status, updated_at=:updated_at WHERE customer_transaction_id=:customerTransactionId AND status=0")
    public abstract void updateCashTransactionStatusByCustomerTransactionId(String customerTransactionId, int status, long updated_at);

    @Query("SELECT COUNT(*) FROM cash_transactions AS ct WHERE ct.book_id  = :bookId and ct.cash_category_id = :categoryId")
    public abstract int countBusinessCashCategoryTransactions(String bookId, String categoryId);

    @Query("UPDATE cash_category SET name= :categoryId WHERE cash_category_id= :cashCategoryId")
    public abstract void updateCashTransactionCategory(String categoryId, String cashCategoryId);

    @Query("SELECT * FROM customers c INNER JOIN (SELECT customer_id, COUNT(*) as frequency FROM transactions WHERE book_id = :bookId AND deleted = 0 GROUP BY customer_id ORDER BY frequency DESC) as trx on c.customer_id = trx .customer_id LIMIT :remoteLimit")
    public abstract List<CustomerEntity> getMostTransactingCustomers(String bookId, int remoteLimit);

    @Query("SELECT SUM(quantity) as product_count, product_id FROM transaction_items as ti JOIN cash_transactions as ct WHERE product_id IN (SELECT product_id FROM products WHERE deleted=0 and book_id=:bookId and ti.transaction_id = ct.cash_transaction_id and ct.date = :yesterdayDate and ct.deleted = 0) GROUP BY product_id ORDER BY quantity DESC LIMIT 5")
    public abstract List<FrequentProductDto> getFrequentProductId(String bookId, String yesterdayDate);

    @Query("SELECT SUM(quantity) as product_count, product_id FROM transaction_items as ti JOIN cash_transactions as ct WHERE product_id IN (SELECT product_id FROM products WHERE deleted=0 and book_id=:bookId and ti.transaction_id = ct.cash_transaction_id and ct.amount>0 and ct.deleted = 0 and ct.date >= :sdate and ct.date <= :edate) GROUP BY product_id ORDER BY SUM(quantity) DESC LIMIT 10")
    public abstract List<FrequentProductDto> getFrequentProductByDateRange(String bookId,String sdate, String edate);

    @Query("SELECT * FROM cash_transactions WHERE order_id=:orderId AND deleted=0")
    public abstract CashTransactionEntity getCashTransactionByOrderId(String orderId);

    @Query("SELECT SUM(amount) from cash_transactions where book_id=:bookId and cash_category_id LIKE '%' || :categoryId || '%'")
    public abstract Double getAmountForCategory(String bookId, String categoryId);

    @Query("SELECT date from cash_transactions where book_id=:bookId and cash_category_id LIKE '%' || :categoryId || '%' LIMIT 1")
    public abstract String getCategoryTransactionCreatedDate(String bookId,String categoryId);

    @Query("SELECT SUM(quantity) as product_count, product_id FROM transaction_items as ti JOIN cash_transactions as ct WHERE product_id IN (SELECT product_id FROM products WHERE deleted=0 and book_id=:bookId and ti.transaction_id = ct.cash_transaction_id and ct.amount>0 and ct.deleted = 0 and ct.date >= :sdate and ct.date <= :edate) GROUP BY product_id ORDER BY SUM(quantity) DESC")
    public abstract List<FrequentProductDto> getFrequentProductIdInDateRange(String bookId,String sdate,String edate);

    @Query("select * from product_category where category_id in (select category_id from product_category_cross_ref where product_id in (SELECT product_id from transaction_items where transaction_id in (select cash_transaction_id from cash_transactions where date >=:startDate AND date <=:endDate AND deleted=0 AND book_id=:businessId)) and category_id  in (select category_id from product_category where book_id=:businessId and deleted=0))")
    public abstract ProductCategoryEntity getBestSellingCategory(String businessId, String startDate, String endDate /*,TransactionEntityType type*/);

    @Query("UPDATE cash_transactions SET restore_transaction_items=0 WHERE cash_transaction_id=:transactionId")
    public abstract int updateItemRestoreStatus(String transactionId);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void insertList(List<TransactionEntity> transactionEntityList);
}
