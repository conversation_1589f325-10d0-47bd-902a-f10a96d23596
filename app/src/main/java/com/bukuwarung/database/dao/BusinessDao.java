package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.UserProfileEntity;

import java.util.List;

@Dao
public abstract class BusinessDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insert(BookEntity bookEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insert(List<BookEntity> bookEntities);

    @Update
    public abstract int update(BookEntity bookEntity);

    @Query("SELECT COUNT(*) FROM books WHERE owner_id=:str AND (deleted is NULL or deleted=0)")
    public abstract int businessCount(String str);

    @Query("SELECT business_owner_name FROM books WHERE owner_id=:str ORDER by COALESCE(deleted,0) ASC, created_at ASC")
    public abstract List<String> getRepresentativeList(String str);

    @Query("SELECT * FROM books WHERE owner_id=:str AND (deleted is NULL or deleted=0) ORDER by created_at")
    public abstract LiveData<List<BookEntity>> getBusinessList(String str);

    @Query("SELECT * FROM books WHERE owner_id=:str AND (deleted is NULL or deleted=0)")
    public abstract List<BookEntity> getBusinessListRaw(String str);

    @Query("SELECT * FROM books WHERE book_id=:str")
    public abstract LiveData<BookEntity> getBusinessById(String str);

    @Query("SELECT * FROM books WHERE book_id=:str")
    public abstract BookEntity getBusinessSync(String str);

    @Query("UPDATE books SET deleted=:delFlg, updated_at=:timestamp,updated_by_user=:userId,updated_by_device=:deviceId,dirty=1 WHERE book_id=:businessId")
    public abstract int updateDeleteFlag(String userId, String deviceId, String businessId, Integer delFlg, Long timestamp);

    @Query("UPDATE books SET business_name=:businessName, book_name=:businessName,book_type=:businessType, book_type_name=:businessTypeName, business_owner_name=:businessOwnerName, updated_at=:updatedAt,updated_by_user=:userId,updated_by_device=:deviceId,dirty=1 WHERE book_id=:businessId")
    public abstract void updateBusinessProfile(String userId, String deviceId, String businessId, String businessName, Integer businessType, String businessTypeName, String businessOwnerName, Long updatedAt);

    @Query("UPDATE books SET business_image=:imageUrl,business_image_upload_pending=:isPending WHERE book_id=:businessId")
    public abstract void updateBusinessProfileImage(String businessId, String imageUrl, int isPending);

    @Query("UPDATE books SET business_logo=:imageUrl,business_logo_upload_pending=:isPending,business_phone=:newPhone,business_address=:newAddress WHERE book_id=:businessId")
    public abstract void updateInvoicePref(String businessId, String imageUrl, String newPhone, String newAddress, int isPending);

    @Query("UPDATE books SET business_logo_upload_pending=:isPending,business_phone=:newPhone,business_address=:newAddress WHERE book_id=:businessId")
    public abstract void updateInvoicePrefWithoutLogo(String businessId, String newPhone, String newAddress, int isPending);

    @Query("UPDATE books SET business_name=:businessName, book_name=:businessName WHERE book_id=:businessId")
    public abstract void updateBusinessProfileName(String businessId, String businessName);

    @Query("UPDATE books SET business_name=:businessName,book_name=:businessName, business_tag_line=:businessTagLine, business_owner_name=:businessOwnerName, business_address=:businessAddress,business_phone=:businessPhone,business_email=:businessEmail,updated_at=:updatedAt,updated_by_user=:userId,updated_by_device=:deviceId,dirty=1 WHERE book_id=:businessId")
    public abstract void updateBusinessProfileComplete(String userId, String deviceId, String businessId, String businessName, String businessOwnerName, String businessTagLine, String businessPhone, String businessAddress, String businessEmail, long updatedAt);

    @Query("UPDATE books SET enabled_payment=1 WHERE book_id=:businessId")
    public abstract void enablePayments(String businessId);

    @Query("DELETE FROM books WHERE is_guest=1")
    public abstract void deleteGuestBook();

    @Query("UPDATE books SET is_guest=0, created_by_user=:userId, updated_by_user=:userId, owner_id=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestBook(String userId, String bookId);

    @Query("select count(*) from books where book_type_name in (:oldBusinessCategory) and book_type_name <> :exclude")
    public abstract int getOldBusinessCategoryCount(String[] oldBusinessCategory,String exclude);

    @Query("select count(*) from books where book_type_name in (:oldBusinessCategory)")
    public abstract int getOldBusinessCategoryCount(String[] oldBusinessCategory);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insertUserProfile(UserProfileEntity userProfileEntity);

    @Query("SELECT * FROM user_profile WHERE user_id=:userId")
    public abstract LiveData<UserProfileEntity> getUserProfile(String userId);

    @Query("SELECT * FROM user_profile WHERE user_id=:userId")
    public abstract UserProfileEntity getUserProfileEntity(String userId);

    @Query("UPDATE user_profile SET user_profile_image=:imageUrl WHERE user_id=:userId")
    public abstract void updateUserProfileImage(String userId, String imageUrl);

    @Query("DELETE FROM user_profile")
    public abstract int deleteUserProfile();

    @Query("UPDATE books SET province=:province, city=:city, district=:district, subdistrict=:subdistrict, postal_code=:postalCode, business_address=:fullAddress WHERE book_id=:bookId")
    public abstract int updateBusinessAdministrativeData(String bookId, String province, String city, String district, String subdistrict, String postalCode, String fullAddress);

    @Query("UPDATE books SET book_type = :bookType, book_type_name = :bookTypeName where book_id = :bookId")
    public abstract int migrateExistingBook(String bookId, int bookType, String bookTypeName);

    @Query("select created_at from books where book_id = :bookId")
    public abstract long getJoiningDate(String bookId);

    @Query("select * from books where book_name like '%Talangin Dulu%' LIMIT 1")
    public abstract BookEntity getBnplBookId();
}
