package com.bukuwarung.database.dao;

import com.bukuwarung.database.entity.CustomerEntity;

import java.util.List;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

@Dao
public abstract class CustomerDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insert(CustomerEntity customerEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract List<Long> insert(List<CustomerEntity> customerEntity);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract Long insertRetry(CustomerEntity customerEntity);

    @Query("SELECT * FROM customers WHERE customer_id=:customerId")
    public abstract CustomerEntity getCustomerById(String customerId);

    @Query("SELECT * FROM customers WHERE phone=:mobileNumber")
    public abstract CustomerEntity getCustomerByMobileNumber(String mobileNumber);

    @Query("SELECT * FROM customers WHERE customer_id=:customerId")
    public abstract LiveData<CustomerEntity> getCustomerByIdLive(String customerId);

    @Query("UPDATE customers SET updated_at=:timestamp,last_modified_at=:timestamp,balance=(SELECT COALESCE(ROUND(SUM(amount), 2),0) FROM transactions WHERE customer_id=:cstId AND deleted=0) WHERE customer_id=:cstId")
    public abstract int updateCustomerBalance(String cstId,Long timestamp);

    @Query("SELECT * FROM customers WHERE book_id=:businessId AND NOT deleted AND country_code=:countryCode AND phone=:phone")
    public abstract List<CustomerEntity> getCustomerByNumber(String businessId, String countryCode, String phone);

    @Query("SELECT * FROM customers WHERE book_id=:businessId AND NOT deleted")
    public abstract LiveData<List<CustomerEntity>> getCustomersByBusiness(String businessId);

    @Query("SELECT * FROM customers WHERE book_id=:businessId AND NOT deleted AND due_date < :currentDate")
    public abstract LiveData<List<CustomerEntity>> getCustomersByBusinessWithPastDueDate(String businessId, String currentDate);

    @Query("SELECT * FROM customers WHERE book_id=:businessId AND NOT deleted order by last_modified_at DESC")
    public abstract List<CustomerEntity> getCustomersListForContacts(String businessId);

    @Query("SELECT * FROM customers WHERE book_id=:businessId AND NOT deleted order by last_modified_at DESC LIMIT :limit")
    public abstract List<CustomerEntity> getCustomersListForContacts(String businessId, int limit);

    @Query("SELECT * FROM customers WHERE NOT deleted AND due_date == :currentDate order by last_modified_at DESC")
    public abstract List<CustomerEntity> getCustomerWithTodaysDueDate(String currentDate);

    @Query("SELECT count(*) FROM customers WHERE book_id=:businessId AND NOT deleted")
    public abstract Integer getCustomerCountForBusiness(String businessId);

    @Query("SELECT * FROM customers WHERE customer_id=:customerId")
    public abstract LiveData<CustomerEntity> getObservableCustomerById(String customerId);

    @Query("SELECT * FROM customers WHERE book_id=:bookId AND balance < 0 AND due_date IS NOT NULL AND NOT deleted order by due_date DESC")
    public abstract LiveData<List<CustomerEntity>> getObservableCustomersWithReminderDate(String bookId);

    @Query("SELECT * FROM customers WHERE book_id=:bookId AND balance < 0 AND due_date IS NULL AND NOT deleted order by balance DESC")
    public abstract LiveData<List<CustomerEntity>> getObservableCustomersWithoutReminderDate(String bookId);

    @Query("SELECT COUNT(*) FROM customers WHERE book_id=:bookId AND balance > 0 AND NOT deleted")
    public abstract int countCredit(String bookId);

    @Query("SELECT COUNT(*) FROM customers WHERE book_id=:bookId AND balance < 0 AND NOT deleted")
    public abstract int countDebit(String bookId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount < 0 AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalDebit(String bookId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount >= 0 AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalCredit(String bookId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount >= 0 AND date = :date AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalCreditByDate(String bookId, String date);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount < 0 AND date = :date AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalDebitByDate(String bookId, String date);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount >= 0 AND date >= :sdate AND date <= :edate AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalCreditByDateRange(String bookId, String sdate,String edate);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from transactions AS c WHERE book_id=:bookId AND amount < 0 AND date >= :sdate AND date <= :edate AND NOT deleted AND customer_id in (SELECT customer_id FROM customers where deleted =0)")
    public abstract Double getSummaryTotalDebitByDateRange(String bookId, String sdate,String edate);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE book_id=:bookId AND amount >= 0 AND date = :date AND NOT deleted AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract Double getSummaryTotalCashInByDate(String bookId, String date);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE book_id=:bookId AND amount < 0 AND date = :date AND NOT deleted AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract Double getSummaryTotalCashOutByDate(String bookId, String date);

    @Query("select COALESCE(ROUND(SUM(buying_price), 2),0) from cash_transactions AS c WHERE book_id=:bookId AND date = :date AND NOT deleted AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract Double getSummaryTotalBuyingPriceByDate(String bookId, String date);

    @Query("select count(*) from cash_transactions AS c WHERE book_id=:bookId AND date = :date AND NOT deleted")
    public abstract int getCashTransactionCountByDate(String bookId, String date);

    @Query("select count(*) from transactions AS c WHERE book_id=:bookId AND date = :date AND NOT deleted")
    public abstract int getUtangTransactionCountByDate(String bookId, String date);

    @Query("UPDATE customers SET phone=:phone,last_modified_at=:last_modified_at,updated_at=:updated_at,dirty=1 WHERE customer_id=:customerId")
    public abstract int updateCustomerPhone(String phone, Long last_modified_at, Long updated_at,String customerId);

    @Query("UPDATE customers SET due_date=NULL,last_modified_at=:last_modified_at,updated_at=:updated_at,dirty=1 WHERE customer_id=:customerId")
    public abstract int deleteCustomerDueDate(Long last_modified_at, Long updated_at,String customerId);

    @Query("UPDATE customers SET due_date=:reminderDate,last_modified_at=:currentTime,updated_at=:currentTime,updated_by_user=:userId,updated_by_device=:deviceId,dirty=1 WHERE customer_id=:customerId")
    public abstract int setReminderDate(String customerId, String reminderDate, long currentTime, String userId, String deviceId);

    @Query("UPDATE customers SET name=:name,address=:address,phone=:phone,country_code=:countryCode,deleted=:delete,last_modified_at=:time,updated_at=:time,updated_by_user=:userId,updated_by_device=:deviceId,dirty=1 WHERE customer_id=:customerId")
    public abstract int updateCustomerById(String customerId, String name, String address, String phone, String countryCode, int delete, long time, String userId, String deviceId);

    @Query("UPDATE customers SET name=:name,address=:address,phone=:phone,country_code=:countryCode,deleted=:delete,last_modified_at=:time,updated_at=:time,updated_by_user=:userId,updated_by_device=:deviceId, image=:imageUrl,dirty=1 WHERE customer_id=:customerId")
    public abstract int updateCustomerWithImageById(String customerId, String name, String address, String phone, String countryCode, int delete, long time, String userId, String deviceId, String imageUrl);

    @Query("UPDATE customers SET image=:imageUrl,image_upload_pending=:uploadedFlg WHERE customer_id=:customerId")
    public abstract int updateImageInfo(String customerId, String imageUrl, int uploadedFlg);

    @Query("SELECT * FROM customers WHERE book_id=:bookId AND NOT deleted AND country_code=:countryCd AND phone=:phone")
    public abstract List<CustomerEntity> getCustomerWithPhone(String bookId, String countryCd, String phone);

    @Query("UPDATE customers SET enable_sms_alerts=:smsFlg,updated_by_device=:deviceId,updated_by_user=:userId,dirty=1 WHERE customer_id=:customerId")
    public abstract int updateSMSFlag(String customerId, Integer smsFlg, String userId, String deviceId);

    @Query("UPDATE customers SET language=:newLang,updated_by_user=:updatedBy,updated_by_device=:deviceId,dirty=1 WHERE customer_id=:customerId")
    public abstract int updateLanguage(String customerId, Integer newLang, String updatedBy, String deviceId);

    @Query("SELECT COUNT(*) FROM customers WHERE book_id=:str AND deleted=0")
    public abstract Integer countAllVisibleSync(String str);

    @Query("UPDATE customers SET created_by_user=:userId, updated_by_user=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestCustomerData(String userId, String bookId);

    @Query("DELETE FROM customers WHERE book_id=:bookId")
    public abstract void deleteCustomerGuestData(String bookId);

    @Query("DELETE FROM cash_category WHERE book_id=:bookId")
    public abstract void deleteCashCategoryGuestData(String bookId);

    @Query("UPDATE transactions SET deleted=1, dirty=1 WHERE customer_id=:customerId")
    public abstract void deleteCustomerTransactions(String customerId);

    @Query("DELETE FROM customers WHERE customer_id=:customerId")
    public abstract int deleteCustomerById(String customerId);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void insertList(List<CustomerEntity> customerEntity);
}
