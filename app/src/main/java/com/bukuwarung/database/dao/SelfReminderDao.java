package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.bukuwarung.database.entity.SelfReminderEntity;

import java.util.List;

/*
dao for all self reminder resources
 */
@Dao
public abstract class SelfReminderDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insert(SelfReminderEntity selfReminderEntity);

    @Query("SELECT * FROM self_reminder WHERE created_by_user=:userId")
    public abstract LiveData<List<SelfReminderEntity>> getLiveReminderListByUser(String userId);

    @Query("SELECT * FROM self_reminder WHERE created_by_user=:userId")
    public abstract List<SelfReminderEntity> getReminderListByUser(String userId);

    @Query("SELECT * FROM self_reminder WHERE reminder_id=:reminderId")
    public abstract SelfReminderEntity getReminderById(String reminderId);

    @Query("update self_reminder set is_active=0 WHERE reminder_id=:reminderId")
    public abstract void disableReminder(String reminderId);

    @Query("update self_reminder set is_active=1 WHERE reminder_id=:reminderId")
    public abstract void enableReminder(String reminderId);

    @Query("DELETE FROM self_reminder WHERE reminder_id=:reminderId")
    public abstract void deleteReminder(String reminderId);
}
