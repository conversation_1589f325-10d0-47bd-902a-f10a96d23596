package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.bukuwarung.database.entity.CashCategoryEntity;

import java.util.List;

@Dao
public abstract class CashDao {

    @Insert
    public abstract Long insert(CashCategoryEntity cashCategoryEntity);

    @Query("SELECT * FROM cash_category WHERE cash_category_id=:str")
    public abstract CashCategoryEntity getCashCategoryById(String str);

    @Query("SELECT * FROM cash_category WHERE name=:name")
    public abstract CashCategoryEntity getCashCategoryByName(String name);

    @Query("SELECT cash_category_id FROM cash_category WHERE book_id=:bookId and cash_category_id LIKE '%' || :name || '%' LIMIT 1")
    public abstract String getCategoryIdByName(String bookId,String name);

    @Query("SELECT * FROM cash_category WHERE cash_category_id=:str")
    public abstract LiveData<CashCategoryEntity> getCashCategoryByIdLive(String str);

    @Query("UPDATE cash_category SET updated_at=:timestamp,balance=(SELECT COALESCE(ROUND(SUM(amount), 2),0) FROM transactions WHERE cash_category_id=:cstId AND deleted=0) WHERE cash_category_id=:cstId")
    public abstract int updateCashCategoryBalance(String cstId,Long timestamp);

    @Query("SELECT * FROM cash_category WHERE book_id=:businessId AND NOT deleted")
    public abstract LiveData<List<CashCategoryEntity>> getCashCategorysByBusiness(String businessId);

    @Query("SELECT count(*) FROM cash_category WHERE book_id=:businessId AND NOT deleted")
    public abstract Integer getCashCategoryCountForBusiness(String businessId);

    @Query("SELECT * FROM cash_category WHERE cash_category_id=:cashCategoryId")
    public abstract LiveData<CashCategoryEntity> getObservableCashCategoryById(String cashCategoryId);

    @Query("UPDATE cash_category SET balance=(SELECT COALESCE(ROUND(SUM(amount), 2),0) FROM cash_transactions WHERE cash_category_id=:category_id AND deleted=0) WHERE cash_category_id=:category_id")
    public abstract int refreshCashBalance(String category_id);

    @Query("UPDATE cash_category SET type = :type, deleted=0, balance=(SELECT COALESCE(ROUND(SUM(amount), 2),0) FROM cash_transactions WHERE cash_category_id=:category_id AND deleted=0) WHERE cash_category_id=:category_id")
    public abstract int enableAndRefreshBalance(String category_id, int type);

    @Query("UPDATE cash_category SET last_modified_at=:modifiedAt WHERE  cash_category_id=:category_id  AND  (last_modified_at = NULL OR   last_modified_at < :modifiedAt)")
    public abstract int setLastModifiedAt(Long modifiedAt, String category_id);

    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND deleted=0 AND type=:categoryType")
    public abstract List<CashCategoryEntity> getAllUnique(String bookId, int categoryType);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertCashCategoryEntity(CashCategoryEntity cashCategoryEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertCashCategoryEntities(List<CashCategoryEntity> cashCategoryEntity);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void retryCashCategorySync(CashCategoryEntity cashCategoryEntity);

    @Query("DELETE FROM cash_category WHERE cash_category_id=:cashId")
    public abstract void deleteById(String cashId);

    @Query("UPDATE cash_category SET name=:name,deleted=:deleted,last_modified_at=:modifiedAt,updated_at=:updatedAt,updated_by_user=:user,updated_by_device=:device,dirty=1,type=:type WHERE cash_category_id=:id")
    public abstract int updateOne(String id, String name, Integer deleted, Long modifiedAt, Long updatedAt, String user, String device,Integer type);

    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND deleted=0")
    public abstract List<CashCategoryEntity> getAllCategoryList(String bookId);

    @Query("UPDATE cash_category SET created_by_user=:userId, updated_by_user=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestCashCategoryData(String userId, String bookId);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE book_id=:bookId AND amount >= 0 AND date >= :sdate AND date <= :edate AND NOT deleted AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract Double getSummaryTotalCashInDateRange(String bookId, String sdate,String edate);

    @Query("select COALESCE(ROUND(SUM(amount), 2),0) from cash_transactions AS c WHERE book_id=:bookId AND amount <= 0 AND date >= :sdate AND date <= :edate AND NOT deleted AND cash_category_id in (SELECT cash_category_id FROM cash_category where deleted =0)")
    public abstract Double getSummaryTotalCashOutDateRange(String bookId, String sdate,String edate);


    @Query("SELECT * FROM cash_category WHERE book_id=:bookId AND type=:categoryType ORDER BY frequency DESC LIMIT 5")
    public abstract List<CashCategoryEntity> getCategoryByFrequency(String bookId,int categoryType);

}
