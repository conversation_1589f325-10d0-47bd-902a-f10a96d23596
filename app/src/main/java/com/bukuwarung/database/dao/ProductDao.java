package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.bukuwarung.database.entity.MeasurementEntity;
import com.bukuwarung.database.entity.ProductEntity;

import java.util.List;

import kotlinx.coroutines.flow.Flow;

@Dao
public abstract class ProductDao {

    @Insert
    public abstract Long insert(ProductEntity productEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertAll(List<ProductEntity> productEntity);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void insertAllIgnore(List<ProductEntity> productEntity);

    @Query("SELECT * FROM products WHERE product_id=:str AND deleted=0")
    public abstract ProductEntity getProductById(String str);

    @Query("SELECT * FROM products WHERE product_id=:str")
    public abstract ProductEntity getAllProductById(String str);

    @Query("SELECT * FROM products WHERE product_id=:str AND deleted=0")
    public abstract LiveData<ProductEntity> getProductLiveDataById(String str);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0")
    public abstract List<ProductEntity> getProductByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0 AND track_inventory<2")
    public abstract LiveData<List<ProductEntity>> getLiveProductByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0 AND track_inventory<2")
    public abstract Flow<List<ProductEntity>> getFlowProductByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0 AND stock <= minimum_stock  AND track_inventory=1")
    public abstract LiveData<List<ProductEntity>> getLiveRunningOutProductByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0 AND stock <= minimum_stock  AND track_inventory=1")
    public abstract List<ProductEntity> getRunningOutProductListByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0 AND stock > minimum_stock  AND track_inventory=1")
    public abstract List<ProductEntity> getProductListInStockByBusiness(String businessId);

    @Query("SELECT * FROM products WHERE name=:name AND deleted=0")
    public abstract List<ProductEntity> getProductByName(String name);

    @Query("SELECT * FROM products WHERE product_id=:id AND deleted=0")
    public abstract List<ProductEntity> getProductsById(String id);

    @Query("SELECT count(*) FROM products WHERE book_id=:bookId")
    public abstract int getProductCount(String bookId);

    @Query("UPDATE products set name=:name where product_id=:productId AND deleted=0")
    public abstract void updateProductName(String name, String productId);

    @Query("UPDATE products SET created_by_user=:userId, updated_by_user=:userId WHERE book_id=:bookId")
    public abstract void mergeGuestProductData(String userId, String bookId);

    @Update
    public abstract void updateProduct(ProductEntity productEntity);

    @Delete
    public abstract void deleteProduct(ProductEntity productEntity);

    @Query("Select * from measurements where measurement_name=:measurementName")
    public abstract MeasurementEntity findMeasurementByName(String measurementName);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertMeasurementUnit(MeasurementEntity newMeasurementUnit);

    @Query("UPDATE products SET track_inventory=0 WHERE product_id=:productId")
    public abstract void disableInventoryTracking(String productId);

    @Query("UPDATE products SET track_inventory=2, deleted=1 WHERE product_id=:productId")
    public abstract void suspendInventoryProduct(String productId);

    @Query("UPDATE products SET track_inventory=1 WHERE product_id=:productId")
    public abstract void enableInventoryTracking(String productId);

    @Update
    public abstract void update(ProductEntity productEntity);

    @Query("SELECT * FROM products WHERE book_id=:businessId AND deleted=0")
    public abstract LiveData<List<ProductEntity>> getLiveProductListIncludingDisabledStock(String businessId);

    @Query("SELECT * FROM products WHERE book_id=:bookId AND deleted=0")
    public abstract List<ProductEntity> getProductListIncludingDisabledStock(String bookId);

    @Query("SELECT count(*) FROM products WHERE NOT deleted")
    public abstract Integer getProductCount();

    @Query("SELECT name FROM products WHERE book_id=:bookId AND deleted=0")
    public abstract List<String> getAllProductNames(String bookId);

}
