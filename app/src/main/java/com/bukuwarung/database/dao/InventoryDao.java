package com.bukuwarung.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.MeasurementEntity;
import com.bukuwarung.database.entity.ProductEntity;

import java.util.List;

@Dao
public abstract class InventoryDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract Long insert(InventoryHistoryEntity inventoryHistoryEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertAll(List<InventoryHistoryEntity> inventoryHistoryList);

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    public abstract void insert(List<MeasurementEntity> measurementEntity);

    @Insert
    public abstract void insert(MeasurementEntity measurementEntity);

    @Query("SELECT * FROM inventory_history WHERE product_id=:productId ORDER by created_at asc")
    public abstract LiveData<List<InventoryHistoryEntity>> getInventoryHistory(String productId);

    @Query("SELECT transaction_id FROM transaction_items where inventory_history_id =:inventoryHistoryId ")
    public abstract String getInventoryHistoryTransactionId(String inventoryHistoryId);

    @Query("SELECT * FROM measurements WHERE book_id=:bookId")
    public abstract List<MeasurementEntity> getMeasurementListByBook(String bookId);

    @Query("UPDATE measurements SET is_default=:isDefault WHERE measurement_id=:unitId")
    public abstract void setDefaultMeasurement(String unitId, int isDefault);

    @Query("SELECT * FROM measurements")
    public abstract List<MeasurementEntity> getAllMeasurements();

    @Query("SELECT * FROM measurements where measurement_id=:measurementId")
    public abstract MeasurementEntity getMeasurementById(String measurementId);

    @Query("UPDATE measurements SET measurement_name=:newName WHERE measurement_id=:id")
    public abstract void updateMeasurementName(String id, String newName);

    @Query("Select * from measurements where measurement_name=:measurementName")
    public abstract MeasurementEntity findMeasurementByName(String measurementName);

    @Query("Select count(*) from measurements where lower(measurement_name)=:measurementName")
    public abstract Integer findMeasurementCountByName(String measurementName);

    @Query("Select * from measurements where is_default=1")
    public abstract MeasurementEntity getDefaultMeasurement();

    @Query("DELETE FROM inventory_history where history_id=:inventoryHistoryId")
    public abstract void deleteByHistoryId(String inventoryHistoryId);

    @Query("DELETE FROM inventory_history where product_id=:productId")
    public abstract void deleteByProductId(String productId);

    @Query("update inventory_history set current_stock=current_stock+:adjustStock where product_id=:productId and created_at>:createdAt")
    public abstract void adjustInventoryBeforeStockDelete(Double adjustStock,String productId,long createdAt);

    @Query("update inventory_history set current_stock=quantity_change+:adjustStock where product_id=:productId and created_at>:createdAt")
    public abstract void adjustInventoryAdjutStockAfterDelete(int adjustStock,String productId,long createdAt);

    @Query("SELECT SUM(quantity_change) FROM inventory_history WHERE product_id=:product_id AND (operation_type='ADD_STOCK' OR operation_type='EXPENSE_TRANSACTION')")
    public abstract int totalStockAdd(String product_id);

    @Query("SELECT SUM(quantity_change) FROM inventory_history WHERE product_id=:product_id AND (operation_type='REMOVE_STOCK' OR operation_type='SALE_TRANSACTION')")
    public abstract int totalStockRemove(String product_id);

    @Query("Select * from inventory_history where history_id=:inventoryHistoryId")
    public abstract InventoryHistoryEntity getInventoryHistoryById(String inventoryHistoryId);
}
