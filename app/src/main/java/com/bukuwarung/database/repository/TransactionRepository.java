package com.bukuwarung.database.repository;

import android.content.Context;
import android.text.TextUtils;

import androidx.lifecycle.LiveData;
import androidx.room.Transaction;

import com.bukuwarung.Application;
import com.bukuwarung.activities.transactionreport.DateRange;
import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.CustomerDao;
import com.bukuwarung.database.dao.TransactionDao;
import com.bukuwarung.database.dto.CategorySummaryModel;
import com.bukuwarung.database.dto.ReportTranasctionModel;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.entity.TransactionItemsEntity;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.datasync.AppExpenseTransSyncManager;
import com.bukuwarung.datasync.AppTransSyncManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Nullable;

import static com.bukuwarung.constants.AppConst.LUNAS;


public class TransactionRepository {
    private static TransactionRepository sInstance;
    private TransactionDao transactionDao;
    private AppDatabase mDatabase;
    private CustomerDao customerDao;
    String transactionId;

    public TransactionRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        this.transactionDao = mDatabase.transactionDao();
        this.customerDao = mDatabase.customerDao();
    }

    public String getTransactionId() {
        return transactionId;
    }

    public static TransactionRepository getInstance(Context context) {
        try {
            if (sInstance == null) {
                synchronized (TransactionRepository.class) {
                    if (sInstance == null) {
                        sInstance = new TransactionRepository(context);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }

    public String saveCustomerTransactionFromCashTransaction(String businessId, String cstId, double amount,
                                                             String date, String description) {
        TransactionEntity transaction = insertCustomerTransactionFromCashTransaction(businessId, Utility.uuid(), cstId, amount, date,
                description);
        updateCustomerBalance(cstId);
        AppTransSyncManager.getInstance().updateTransaction(transaction);
        return transaction.transactionId;
    }

    @Transaction
    public String settleUnpaidCashTransaction(String businessId, @Nullable String cstId, double amount, String date, String description, int smsStatus, String cashTransactionId) {
        this.transactionDao.updateCashTransactionStatus(cashTransactionId, LUNAS, Utility.getCurrentTime());
        return saveNewTransaction(businessId, cstId, amount, date, description, smsStatus);
    }

    public void updateCashTransactionOrderId(String cashTransactionId, String orderId) {
        transactionDao.updateCashTransactionOrderId(cashTransactionId, orderId, Utility.getCurrentTime());
        CashTransactionEntity cashTransactionEntity = transactionDao.getCashTransactionById(cashTransactionId);
        AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(cashTransactionEntity);
    }

    public String saveNewTransaction(String businessId, @Nullable String cstId, double amount, String date, String description, int smsStatus) {
        TransactionEntity transaction = insertOnlyNewTransaction(businessId, cstId, amount, date, description, smsStatus);
        // cstId could be null if the Belum Lunas transaction comes from Pulsa or any payment
        if (!TextUtils.isEmpty(cstId)) {
            updateCustomerBalance(cstId);
        }
        AppTransSyncManager.getInstance().updateTransaction(transaction);
        transactionId = transaction.transactionId;
        return transaction.transactionId;
    }

    @Transaction
    public void insertTransactionWithBalanceRefresh(TransactionEntity transactionEntity) {
        this.transactionDao.insertCreditTransaction(transactionEntity);
        this.customerDao.updateCustomerBalance(transactionEntity.customerId, Utility.getCurrentTime());
    }

    public int updateCustomerBalance(String cstId) {
        int res = this.customerDao.updateCustomerBalance(cstId, Utility.getCurrentTime());
        CustomerEntity customerEntity = this.customerDao.getCustomerById(cstId);

        // IF CUSTOMER DIDNT HAVE UTANG ANYMORE, DELETES THE CURRENT DUEDATE
        if (customerEntity.balance >= 0 && customerEntity.dueDate != null && !customerEntity.dueDate.isEmpty()) {
            customerEntity.dueDate = null;
            long updatedAt = AppIdGenerator.getCurrentTime();
            this.customerDao.deleteCustomerDueDate(updatedAt, updatedAt, customerEntity.customerId);
        }
        return res;
    }

    public double getCustomerBalance(String customerId) {
        CustomerEntity customerEntity = this.customerDao.getCustomerById(customerId);
        return customerEntity.balance;
    }

    public TransactionEntity insertCustomerTransactionFromCashTransaction(String businessId, String trxId, String cstId,
                                                                          double amount, String date, String description) {
        TransactionEntity transactionEntity = new TransactionEntity(businessId, cstId, trxId);
        transactionEntity.amount = Double.valueOf(amount);
        transactionEntity.date = date;
        transactionEntity.description = description;
        transactionEntity.deleted = Integer.valueOf(0);
        transactionEntity.serverSeq = Long.valueOf(0);
        transactionEntity.dirty = Integer.valueOf(1);
        transactionEntity.smsStatus = 0;
        transactionEntity.transactionType = TransactionEntityType.CASH_TRANSACTION;
        EntityHelper.fillTxnMetadata(transactionEntity);
        this.transactionDao.insert(transactionEntity);
        return transactionEntity;
    }

    public TransactionEntity insertOnlyNewTransaction(String businessId, String cstId, double amount, String date, String description, int smsStatus) {
        TransactionEntity transactionEntity = new TransactionEntity(businessId, cstId, Utility.uuid());
        transactionEntity.amount = Double.valueOf(amount);
        transactionEntity.date = date;
        transactionEntity.description = description;
        transactionEntity.deleted = Integer.valueOf(0);
        transactionEntity.serverSeq = Long.valueOf(0);
        transactionEntity.dirty = Integer.valueOf(1);
        transactionEntity.smsStatus = Integer.valueOf(smsStatus);
        EntityHelper.fillTxnMetadata(transactionEntity);

        // inserting Utang Piutang to SQLite
        try {
            this.transactionDao.insert(transactionEntity);
            // inserting customer to SQLite success
            FirebaseCrashlytics.getInstance().log("[SQLite][UtangPiutang]Success.ID:" + transactionEntity.transactionId);
        } catch (Exception ex) {
            // inserting customer to SQLite fails
            FirebaseCrashlytics.getInstance().log("[SQLite][UtangPiutang]Failed.ID:" + transactionEntity.transactionId + ",Trace:" + ex.toString());
        }

        return transactionEntity;
    }

    public List<ReportTranasctionModel> getTransactionsByBusiness(String businessId, String startDate, String endDate) {
        return this.transactionDao.getTransactionByBusiness(businessId, startDate, endDate);
    }

    public List<ReportTranasctionModel> getTransactionsForDateRange(String businessId, DateRange dateRange) {
        return getTransactionsByBusiness(businessId, dateRange.getStartDate(), dateRange.getEndDate());
    }

    public List<ReportTranasctionModel> getCashTransactionsForDateRange(String businessId, DateRange dateRange) {
        return getCashTransactionsByBusiness(businessId, dateRange.getStartDate(), dateRange.getEndDate());
    }

    public List<ReportTranasctionModel> getCashTransactionsByBusiness(String businessId, String startDate, String endDate) {
        return this.transactionDao.getCashTransactionByBusiness(businessId, startDate, endDate);
    }

    public LiveData<List<TransactionEntity>> getTransactionListByCustomerId(String customerId) {
        return this.transactionDao.getTransactionListByCustomerId(customerId);
    }

    public LiveData<TransactionEntity> getObservableTransactionById(String transactionId) {
        return this.transactionDao.getObservableTransactionById(transactionId);
    }

    public void updateCashTransaction(String transactionId, String mobileNumber, Double amount, int status, String customerName, String customerId ){
        CashTransactionEntity cashTransactionEntity = this.transactionDao.getCashTransactionById(transactionId);
        if(cashTransactionEntity == null) return;// call the function to update the local data
        if(mobileNumber != null && !mobileNumber.isEmpty()) cashTransactionEntity.customerPhoneNumber = mobileNumber;
        cashTransactionEntity.status = status;
        if(customerName != null && !customerName.isEmpty()) cashTransactionEntity.customerName = customerName;
        if(amount != null) cashTransactionEntity.amount = amount;
        if(customerId != null && !customerId.isEmpty()) cashTransactionEntity.customerId = customerId;
        AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(cashTransactionEntity);
    }

    public String getCustomerId(String transactionId){
        CashTransactionEntity cashTransactionEntity = this.transactionDao.getCashTransactionById(transactionId);
        if(cashTransactionEntity != null )
            return cashTransactionEntity.customerId;
        else
            return "";
    }

    public TransactionEntity getTransactionById(String transactionId) {
        return this.transactionDao.getTransactionById(transactionId);
    }

    public CashTransactionEntity getCashTransactionByCustomerTransactionId(String transactionId) {
        return this.transactionDao.getCashTransactionByCusutomerTransactionId(transactionId);
    }

    public int updateExistingTransaction(String userId, String deviceId, String transactionId, String customerId, double amountDbl, String transactionDate, String desc, int deleteFlg) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        int result = transactionDao.updateById(transactionId, amountDbl, transactionDate, desc, deleteFlg, 0, updatedAt, userId, deviceId);
        customerDao.updateCustomerBalance(customerId, updatedAt);
        TransactionEntity transaction = this.transactionDao.getTransactionById(transactionId);
        AppTransSyncManager.getInstance().updateTransaction(transaction);
        return result;
    }

    public int countAllCashTrans(String bookId) {
        return this.transactionDao.countAllCashTransaction(bookId);
    }

    public int countAllIncomeTrans(String bookId) {
        return this.transactionDao.countAllIncomeTransaction(bookId);
    }

    public int countAllExpenseTrans(String bookId) {
        return this.transactionDao.countAllExpenseTransaction(bookId);
    }

    public int countIncomeTransactionWithIdByDate(String bookId, String catId, String sdate, String edate) {
        return this.transactionDao.countIncomeTransactionWithId(bookId,catId,sdate,edate);
    }

    public int getCategoryAmountByDate(String bookId, String catId, String sdate, String edate) {
        return this.transactionDao.getCategoryAmountByDate(bookId,catId,sdate,edate);
    }

    public int countExpenseTransactionWithIdByDate(String bookId,String catId, String sdate, String edate) {
        return this.transactionDao.countExpenseTransactionWithId(bookId,catId,sdate,edate);
    }

    public int getAllIncomeTrxCountWithProductByDate(String bookId, String sdate, String eDate) {
        return this.transactionDao.getAllIncomeTrxCountWithProductByDate(bookId,sdate,eDate);
    }

    public int getAllExpenseTrxCountWithProductByDate(String bookId, String sdate, String eDate) {
        return this.transactionDao.getAllExpenseTrxCountWithProductByDate(bookId,sdate,eDate);
    }

    public ArrayList<CashCategoryEntity> getAllIncomeCategories(String bookId, String sdate, String eDate) {
        return (ArrayList<CashCategoryEntity>) this.transactionDao.getAllIncomeCategoriesByDate(bookId,sdate,eDate);
    }

    public ArrayList<CashCategoryEntity> getAllExpenseCategories(String bookId, String sdate, String eDate) {
        return (ArrayList<CashCategoryEntity>) this.transactionDao.getAllExpenseCategoriesByDate(bookId,sdate,eDate);
    }

    public int countAllCashTransWithDeleted(String bookId) {
        return this.transactionDao.countAllCashTransactionWithDeleted(bookId);
    }

    public int countAllUtangTrans() {
        return this.transactionDao.countUtangTransactions();
    }

    public int countNegativeAmountUtangTransactionsWithDeleted() {
        if (!SessionManager.getInstance().isGuestUser()) {
            return transactionDao.countNegativeAmountUtangTransactionsWithDeleted(SessionManager.getInstance().getUserId());
        } else {
            return transactionDao.countGuestNegativeAmountUtangTransactionsWithDeleted(SessionManager.getInstance().getGuestBusinessId());
        }
    }

    public int countNotPaidCashTransactionsWithDeletedAndProfit(int status) {
        if (!SessionManager.getInstance().isGuestUser()) {
            return countCashTransactionsWithDeletedAndProfitByStatus(status);
        } else {
            return countCashTransactionsWithDeletedAndProfitForGuestByStatus(status);
        }
    }

    public int countUtangTransactionsWithDeletedForGuest() {
        return this.transactionDao.countUtangTransactionsWithDeletedForGuest(User.getBusinessId());
    }

    public int countUniqueUtangTransactionsWithDeletedForGuest() {
        return this.transactionDao.countUniqueUtangTransactionsWithDeletedForGuest(User.getBusinessId());
    }

    public int countCashTransactionsWithDeleted() {
        return this.transactionDao.countCashTransactionsWithDeletedForUserId(User.getUserId());
    }

    public int countCashTransactionsWithDeletedForGuest() {
        return this.transactionDao.countCashTransactionsWithDeletedForGuest(User.getBusinessId());
    }

    public int countCashTransactionsWithDeletedAndProfitByStatus(int status) {
        return this.transactionDao.countCashTransactionsWithDeletedAndProfitForUserIdByStatus(User.getUserId(), status);
    }

    public int countCashTransactionsWithDeletedAndProfitForGuestByStatus(int status) {
        return this.transactionDao.countCashTransactionsWithDeletedAndProfitForGuestByStatus(User.getBusinessId(), status);
    }

    public int getTransactionCount() {
        int creditCount = this.transactionDao.countUtangTransactions();
        int cashCount = this.transactionDao.countCashTransactions();
        return cashCount + creditCount;
    }

    public int getTransactionCountWithBookId(String bookId) {
        int creditCount = this.transactionDao.countUtangTransactionsWithBookId(bookId);
        int cashCount = this.transactionDao.countCashTransactionsWithBookId(bookId);
        return cashCount + creditCount;
    }

    public int getYesterdayTransactionCountWithBookIdAndDate(String bookId,String date) {
        int creditCount = this.transactionDao.countUtangTransactionsWithBookIdAndDate(bookId,date);
        int cashCount = this.transactionDao.countCashTransactionsWithBookIdAndDate(bookId,date);
        return cashCount + creditCount;
    }

    public int getYesterdaySalesTransactionCountWithBookIdAndDate(String bookId,String date) {
        return this.transactionDao.countCashSalesTransactionsWithBookIdAndDate(bookId,date);
    }

    public int getGuestTransactionCount() {
        int creditCount = this.transactionDao.countUtangTransactionsWithDeletedForGuest(User.getBusinessId());
        int cashCount = this.transactionDao.countCashTransactionsWithDeletedForGuest(User.getBusinessId());
        return cashCount + creditCount;
    }

    public int getTransactionCountWithDeletedRecords() {
        if (SessionManager.getInstance().isGuestUser()) {
            return getGuestTransactionCount();
        }
        int creditCount = this.transactionDao.countUtangTransactionsWithDeletedForUserId(User.getUserId());
        int cashCount = this.transactionDao.countCashTransactionsWithDeletedForUserId(User.getUserId());
        return cashCount + creditCount - getCashTransactionCountWithDeletedRecordsByStatus(0);
    }

    public int getCashTransactionsCountWithDeletedRecordsAndCategoryId(String categoryId) {
        if (SessionManager.getInstance().isGuestUser()) {
            return this.transactionDao.countCashTransactionsWithDeletedAndCategoryIdForGuest(User.getBusinessId(),categoryId);
        }
        return transactionDao.countCashTransactionsWithDeletedAndCategoryIdForUserId(User.getUserId(),categoryId);
    }

    public int getCashTransactionCountWithDeletedRecords() {
        if (SessionManager.getInstance().isGuestUser()) {
            return countCashTransactionsWithDeletedForGuest();
        }
        return transactionDao.countCashTransactionsWithDeletedForUserId(User.getUserId());
    }

    public int getPosTransactionCountWithDeletedRecords() {
        return transactionDao.countPosTransactionsWithDeletedForUserId(User.getUserId(), TransactionEntityType.POS_TRANSACTION);
    }

    public int getCashTransactionCountWithDeletedRecordsByStatus(int status) {
        if(SessionManager.getInstance().isGuestUser()){
            return this.transactionDao.countCashTransactionsWithDeletedForGuestByStatus(User.getBusinessId(),status);
        }
        return transactionDao.countCashTransactionsWithDeletedForUserIdByStatus(User.getUserId(),status);
    }

    public int getUtangTransactionCountWithDeletedRecords() {
        if (SessionManager.getInstance().isGuestUser()) {
            return countUtangTransactionsWithDeletedForGuest();
        }
        return transactionDao.countUtangTransactionsWithDeletedForUserId(User.getUserId());
    }

    public int getUniqueUtangTransactionCountWithDeletedRecords() {
        if (SessionManager.getInstance().isGuestUser()) {
            return countUniqueUtangTransactionsWithDeletedForGuest();
        }
        return transactionDao.countUniqueUtangTransactionsWithDeletedForUserId(User.getUserId());
    }

    public LiveData<Integer> getUtangCountLiveData(String userId) {
        return this.transactionDao.countUtangTransactionsLiveData(userId);
    }

    public LiveData<Integer> getTransactionCountLiveData(String userId) {
        return this.transactionDao.countAllTrxLiveData(userId);
    }

    public int getTransactionCount(String userId) {
        return this.transactionDao.countAllTrx(userId);
    }

    public LiveData<List<CashTransactionEntity>> getTransactionListByCategoryId(String categoryId) {
        return this.transactionDao.getTransactionListByCategoryId(categoryId);
    }

    public LiveData<List<CashTransactionEntity>> getTransactionListByCategoryIdWithDate(String categoryId, String startDate, String endDate) {
        return this.transactionDao.getTransactionListByCategoryIdWithDate(categoryId, startDate, endDate);
    }

    public CashCategoryEntity getCashCategoryByIdForBrick(String brickInstitutionId, String categoryId,String startDate,String endDate) {
        return this.transactionDao.getCashCategoryByIdForBrick(brickInstitutionId,categoryId,startDate,endDate);
    }

    public LiveData<List<CashTransactionEntity>> getTransactionListByBrickInstitutionIdWithDate(String categoryId,String brickInstitutionId,String startDate,String endDate) {
        return this.transactionDao.getTransactionListByBrickInstitutionIdWithDate(categoryId,brickInstitutionId,startDate,endDate);
    }

    public void insertCashTransactionSync(CashTransactionEntity cashTransactionEntity) {
        this.transactionDao.insertCashTransaction(cashTransactionEntity);
    }

    public void insertCashTransactionsSync(List<CashTransactionEntity> cashTransactionEntities) {
        this.transactionDao.insertCashTransactions(cashTransactionEntities);
    }

    public void insertTransactionSync(TransactionEntity transactionEntity) {
        this.transactionDao.insertCreditTransaction(transactionEntity);
    }

    public void retryTransactionSync(TransactionEntity transactionEntity) {
        this.transactionDao.retryCreditTransaction(transactionEntity);
    }

    public void retryCashTransactionSync(CashTransactionEntity cashTransactionEntity) {
        this.transactionDao.retryCashTransaction(cashTransactionEntity);
    }

    public String getTransactionMaxDate(boolean isExpense) {
        return isExpense ? this.transactionDao.getMaxExpenseTransacionDate(User.getBusinessId()) : this.transactionDao.getMaxCreditTransacionDate(User.getBusinessId());
    }

    public String getTransactionMinDate(boolean isExpense) {
        return isExpense ? this.transactionDao.getMinExpenseTransacionDate(User.getBusinessId()) : this.transactionDao.getMinCreditTransacionDate(User.getBusinessId());
    }

    public List<TransactionEntity> getAllCustomerTransactionsForBookSync(String bookId) {
        return this.transactionDao.getAllTransactionByBookId(bookId);
    }

    public void insertTransactionItems(TransactionItemsEntity transactionItemsEntity) {
        this.transactionDao.insertTransactionItems(transactionItemsEntity);
        //never add to firestore from here
    }

    public void insertTransactionItems(List<TransactionItemsEntity> transactionItemsEntity) {
        this.transactionDao.insertTransactionItems(transactionItemsEntity);
        AppExpenseTransSyncManager.getInstance().updateTransactionItems(transactionItemsEntity);
    }

    @Transaction
    public void insertTransactionItems(List<TransactionItemsEntity> transactionItemsEntity, int transactionType) {
        this.transactionDao.insertTransactionItems(transactionItemsEntity);
        AppExpenseTransSyncManager.getInstance().updateTransactionItems(transactionItemsEntity);
    }

    public List<TransactionItemsEntity> getTransactionItems(String transactionId) {
        return this.transactionDao.getTransactionItems(transactionId);
    }

    public void deleteAllItemsForTransaction(String transactionId) {
        this.transactionDao.deleteTransactionItems(transactionId);
        AppExpenseTransSyncManager.getInstance().deleteTransactionItems(transactionId);
    }

    @Transaction
    public void deleteAllHistoryItemsForTransaction(String transactionId) {
        List<InventoryHistoryEntity> historyList = this.transactionDao.selectTransactionHistoryItems(transactionId);
        for(InventoryHistoryEntity historyEntity:historyList){
            AppExpenseTransSyncManager.getInstance().deleteInventoryHistoryItems(historyEntity.historyId);
        }
        this.transactionDao.deleteTransactionHistoryItems(transactionId);
    }

    public CategorySummaryModel getCategorySummary(String categoryId) {
        CategorySummaryModel summary = new CategorySummaryModel(this.transactionDao.getTotalSellingPrice(categoryId), this.transactionDao.getTotalBuyingPrice(categoryId));
        return summary;
    }

    public CustomerEntity getCustomerById(String customerId) {
        return customerDao.getCustomerById(customerId);
    }

    public int countCashTransactionsWithDeletedHavingNotesOrCategory() {
        if (SessionManager.getInstance().isGuestUser()) {
            return transactionDao.countCashTransactionsWithDeletedHavingNotesOrCategoryForGuest(User.getBusinessId());
        }
        return transactionDao.countCashTransactionsWithDeletedHavingNotesOrCategoryForUserId(User.getUserId());
    }

    public List<CustomerEntity> getMostTransactingCustomers(String bookId, int remoteLimit){
        return transactionDao.getMostTransactingCustomers(bookId, remoteLimit);
    }

    @NotNull
    public Object getTransactionCountByBook(@NotNull String bookId) {
        int creditCount = this.transactionDao.countUtangTransactionsByBook(bookId);
        int cashCount = this.transactionDao.countCashTransactionsByBook(bookId);
        return cashCount + creditCount;
    }

    public void deleteAllTransactionsWithInstitutionId(int id) {
        transactionDao.deleteCashTransactionsWithInstitutionId(id);
    }

    public void deleteAllTransactionsWithEntityTypeBrick() {
        transactionDao.deleteCashTransactionsWithEntityType(TransactionEntityType.BRICK_TRANSACTION);
    }

    public Double getTransactionAmountForCategory(String bookId, String categoryId) {
        return transactionDao.getAmountForCategory(bookId,categoryId);
    }

    public String getFirstTransactionDateForCategory(String bookId,String categoryId) {
        return transactionDao.getCategoryTransactionCreatedDate(bookId,categoryId);
    }

    public int updateItemRestoreStatus(String transactionId) {
        return transactionDao.updateItemRestoreStatus(transactionId);
    }

    public void insertList(List<TransactionEntity> transactionEntityList) {
        transactionDao.insertList(transactionEntityList);
    }
}
