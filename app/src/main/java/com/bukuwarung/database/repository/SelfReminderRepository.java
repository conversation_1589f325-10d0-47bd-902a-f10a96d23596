package com.bukuwarung.database.repository;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;

import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.BusinessDao;
import com.bukuwarung.database.dao.CustomerDao;
import com.bukuwarung.database.dao.SelfReminderDao;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.SelfReminderEntity;
import com.bukuwarung.database.entity.enums.ReminderCategory;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.Calendar;
import java.util.List;


public class SelfReminderRepository {
    private static SelfReminderRepository sInstance;
    private SelfReminderDao selfReminderDao;
    private AppDatabase mDatabase;

    private SelfReminderRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        selfReminderDao = this.mDatabase.selfReminderDao();
    }

    public static SelfReminderRepository getInstance(Context context) {
        if (sInstance == null) {
            synchronized (SelfReminderRepository.class) {
                if (sInstance == null) {
                    sInstance = new SelfReminderRepository(context);
                }
            }
        }
        return sInstance;
    }

    public List<SelfReminderEntity> getReminderByUser(String userId) {
        return selfReminderDao.getReminderListByUser(userId);
    }

    public LiveData<List<SelfReminderEntity>> getObservableReminderByUser(String userId) {
        return selfReminderDao.getLiveReminderListByUser(userId);
    }

    public SelfReminderEntity getReminderById(String reminderId) {
        return selfReminderDao.getReminderById(reminderId);
    }

    public String saveReminder(Integer hour, Integer minute, String notes, ReminderCategory reminderCategory, Integer isActive) {
        String uuid = Utility.uuid();
        SelfReminderEntity reminderEntity = new SelfReminderEntity(hour, minute, notes, reminderCategory, isActive,uuid);
        EntityHelper.fillReminderMetadata(reminderEntity);
        long res = this.selfReminderDao.insert(reminderEntity);
        return uuid;
    }

    public void enableReminder(String reminderId){
        this.selfReminderDao.enableReminder(reminderId);
    }

    public void disableReminder(String reminderId){
        this.selfReminderDao.disableReminder(reminderId);
    }

    public void deleteReminder(String reminderId){
        this.selfReminderDao.deleteReminder(reminderId);
    }
}
