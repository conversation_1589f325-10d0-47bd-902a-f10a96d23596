package com.bukuwarung.database.repository;

import android.content.Context;
import android.database.Cursor;
import android.os.AsyncTask;
import android.provider.ContactsContract;

import androidx.lifecycle.MutableLiveData;

import com.bukuwarung.Application;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.comparator.ContactComparator;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


public final class ContactRepository {

    public static final ContactRepository INSTANCE;

    public static MutableLiveData<ArrayList<Contact>> contactsLiveData = new MutableLiveData<>();


    private static final class LoadContacts extends AsyncTask<Void, Void, ArrayList<Contact>> {

        public ArrayList<Contact> doInBackground(Void... voidArr) {

            if (!PermissonUtil.hasContactPermission()) {
                return null;
            }
            return ContactRepository.INSTANCE.startLoadingContacts();
        }


        public void onPostExecute(ArrayList<Contact> arrayList) {
            if (arrayList != null) {
                ContactRepository.contactsLiveData.setValue(arrayList);
            }
        }
    }

    static {
        ContactRepository contactRepository = new ContactRepository();
        INSTANCE = contactRepository;
        contactRepository.refreshContactList();
    }

    private ContactRepository() {
    }

    public final MutableLiveData<ArrayList<Contact>> getContactList() {
        return contactsLiveData;
    }

    public final void refreshContactList() {
        new LoadContacts().execute(new Void[0]);
    }




    public final ArrayList<Contact> startLoadingContacts() {
        ArrayList<Contact> contactModelArrayList = new ArrayList<>();
        try {
            Context context = Application.getAppContext();
            List<CustomerEntity> addedCustomer = CustomerRepository.getInstance(context).getCustomersListForContacts(User.getBusinessId());
            Set<String> phoneSet = new HashSet<String>();
            for(CustomerEntity customerEntity:addedCustomer){
                if(!Utility.isBlank(customerEntity.phone)) {
                    Contact contactModel = new Contact(customerEntity.name, customerEntity.phone, customerEntity.image, customerEntity.customerId);
                    contactModelArrayList.add(contactModel);
                    phoneSet.add(customerEntity.phone);
                }
            }
            Cursor phones = context.getContentResolver().query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null, null, null, ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC");
            while (phones != null && phones.moveToNext()) {
                String name = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME));
                String phoneNumber = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
                String photoUri = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Photo.PHOTO_URI));
                if(!phoneSet.contains(phoneNumber)) {
                    Contact contactModel = new Contact(name, Utility.cleanPhonenumber(phoneNumber), photoUri, null);
                    contactModelArrayList.add(contactModel);
                }
            }
            phones.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return contactModelArrayList;
    }

    private final void sortContactList(List<Contact> list) {
        Collections.sort(list, new ContactComparator());
    }
}
