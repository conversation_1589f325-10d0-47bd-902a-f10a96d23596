package com.bukuwarung.database.repository;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.VolleyLog;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.api.RetrofitClient;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.data.restclient.ApiResponse;
import com.bukuwarung.database.dto.UserReferralModel;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.referral.PaymentUserReferral;
import com.bukuwarung.database.entity.referral.ReferralLink;
import com.bukuwarung.database.entity.referral.ReferralReceiver;
import com.bukuwarung.database.entity.referral.UserRank;
import com.bukuwarung.database.entity.referral.UserReferral;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.referral.model.ReferralDataPostUpgradeRequestPayload;
import com.bukuwarung.referral.model.ReferralDataResponsePayload;
import com.bukuwarung.referral.model.ReferralDataResponse;
import com.bukuwarung.referral.model.ReferralUpdateResponse;
import com.bukuwarung.referral.repository.ReferralDataSource;
import com.bukuwarung.referral.usecase.ReferralUseCase;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.ReferralUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.DocumentReference;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import kotlin.coroutines.Continuation;
import kotlin.coroutines.CoroutineContext;
import kotlin.coroutines.EmptyCoroutineContext;
import retrofit2.Retrofit;

public class ReferralRepository {


    public static String TAG = "ReferralRepository";
    public static final String REFERRAL_LINK_COLLECTION = "app_referral_link";
    public static final String USER_REFERRAL_COLLECTION = "app_referral";
    public static final String PAYMENT_REFERRAL_COLLECTION = "app_payment_referral";

    //TODO: will be fetched from backend
    public static final int REGISTRATION_POINTS = 25;
    public static final int BONUS_TRANSACTION_POINTS = 3;
    public static final int SENDER_TARGET_COMPLETION_POINTS = 25;
    public static final int POINTS_PER_TRANSACTION = 1;
    public static final int POINTS_PROFILE_COMPLETED = 10;
    public static final int POINTS_CARD_COMPLETED = 10;

    private static final int REFERRAL_TARGET = 25;

    private static final int POINT_TYPE_TXN = 1;
    private static final int POINT_TYPE_PROFILE = 2;
    private static final int POINT_TYPE_CARD = 3;
    private static final int POINT_TYPE_REG = 4;
    private static final int POINT_TYPE_INITIAL_TXN = 5;
    private static final int POINT_TYPE_TARGET_COMPLETE = 6;

    private static ReferralRepository sInstance;
    private FirebaseFirestore mFirestore;
    private static UserReferral myReferralData;

    private ReferralRepository() {
        this.mFirestore = FirebaseFirestore.getInstance();
    }

    public static ReferralRepository getInstance() {
        try {
            if (sInstance == null) {
                synchronized (ReferralRepository.class) {
                    if (sInstance == null) {
                        sInstance = new ReferralRepository();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }

    /**
     * load user referral data in the beginning and use it for local operations, helps in reducing load to db as fetching referral is not required all the time
     */
    public void loadUserReferralData() {
        try {
            getUserReferralData(new OnUserReferralDataCallback() {
                @Override
                public void onUserReferralDataLoaded(UserReferral updatedData) {
                    if (updatedData != null) {
                        ReferralPrefManager.getInstance().setRegistration(true);
                        ReferralPrefManager.getInstance().setCurrentReferralCode(updatedData.referralCodeInUse);
                    }
                    myReferralData = updatedData;
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * generate shareable link for internal deeplink.
     * this function should only be called before sharing referral with a contact.
     * generated link will be in inactive state (it will show 404 on click), until it's shared with a contact.
     * Referral Flow Step 1
     *
     * @param deeplink //generated deeplink
     * @param refCode  //referral code used in deeplink
     * @return ReferralLink  //referral link object with internal shareable link
     */
    public ReferralLink getShareableLink(String deeplink, String refCode) {
        String shareableLink = deeplink;

        return new ReferralLink.Builder()
                .setSender(User.getUserId())
                .setActive(false)
                .setEnabled(false)//set false as this is newly generated link
                .setSharedlink(ReferralUtils.getReferralUrlWithFilledParam(refCode, deeplink))
                .setDeeplink(deeplink)
                .setCode(refCode)
                .setCreatedAt(AppIdGenerator.getCurrentTime()).build();
    }

    /**
     * After successfully sharing referral link with contact, make referralLink available for receiver.
     * It activates provided referral link. if not activated, shared link will lead to 404.
     * Referral Flow Step 2
     *
     * @param referralLink // referral link shared with customer
     * @param bookEntity   //shared referralLink and sender name registered in profile section
     * @return ReferralLink
     */
    public ReferralLink enableSharedReferralLink(ReferralLink referralLink, BookEntity bookEntity,
                                                 String referralCode) {
        try {
            if (myReferralData == null) {
                registerReferralCodeSender(referralLink, bookEntity, referralCode);
            }
            referralLink.enabled = true;
            //add new shared link mapping to db so that it can be used by receiver
            mFirestore.collection(REFERRAL_LINK_COLLECTION)
                    .document(referralLink.code).set(referralLink);

        } catch (Exception e) {
            referralLink.active = false;
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return referralLink;
        }
        return referralLink;
    }

    /**
     * load latest referral data (point,rank, leaderboard name) for given user
     *
     * @param userId
     * @param callback
     */
    private void getCurrentUserReferral(String userId, OnUserReferralDataCallback callback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                callback.onUserReferralDataLoaded(userReferral);
            }
        });
    }

    /**
     * update current user's referralCode
     *
     * @param referralCode
     */
    private void updateMyReferralCode(String referralCode) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId());
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                if (userReferral != null) {
                    userReferral.myReferralCode = referralCode;
                    mFirestore.collection(USER_REFERRAL_COLLECTION)
                            .document(User.getUserId())
                            .set(userReferral);

                    myReferralData = userReferral;
                }
            }
        });
    }

    /**
     * Change or register new referral code by updating active flag of ReferralLink
     *
     * @param newCode  //referral code to register
     * @param userId   // referral code receiverId
     * @param callback
     */
    private void changeInUseReferralLink(String newCode, String userId, OnCurrentReferralChangeCallback callback) {

        mFirestore.collection(REFERRAL_LINK_COLLECTION).whereArrayContains("receiver", userId)
                .get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                ReferralLink currentReferral = new ReferralLink();
                                //deactivate other links for this receiver
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    ReferralLink referralLink = doc.toObject(ReferralLink.class);

                                    if (!referralLink.code.equals(newCode)) {
                                        ReferralReceiver dummyReceiver = new ReferralReceiver();
                                        dummyReceiver.userId = userId;
                                        // remove user from receiver


                                        referralLink.receiver.remove(dummyReceiver);
                                    } else {
                                        currentReferral = referralLink;
                                    }

                                    mFirestore.collection(REFERRAL_LINK_COLLECTION)
                                            .document(referralLink.code)
                                            .set(referralLink);
                                }
                                callback.OnCurrentReferralCodeChanged(currentReferral);
                            }
                        }
                    }
                });
    }

    /**
     * register new received referral link. called after setting new referral code in profile section
     * Handles Referral Flow Step 4 activate referral code is user doesn't have any other activated code
     *
     * @param code            //referral code extracted from deeplink
     * @param leaderboardName //user's leaderboard name,
     * @param forceChange     //referral code extracted from deeplink
     */
    public void registerWithRefCode(final String code, final String leaderboardName, boolean forceChange) throws IllegalArgumentException {

        if (Utility.isBlank(leaderboardName))
            throw new IllegalArgumentException("leaderboard name cannot be empty");

        ReferralRepository.getInstance().getReferralLinkByCode(code,
                new ReferralRepository.OnReferralLinkDataCallback() {
                    @Override
                    public void onReferralLinkDataLoaded(ReferralLink referralLink) {
                        ReferralRepository.getInstance()
                                .activateReceivedReferralLink(referralLink, leaderboardName, forceChange);
                    }
                });
    }

    /**
     * register new received referral link. called after receiving referral code in main activity
     * Handles Referral Flow Step 4 activate referral code is user doesn't have any other activated code
     *
     * @param referralLink      // referral link object to updated earned point and change active status
     * @param myLeaderboardName //name to show in leaderboard
     * @return ReferralLink //updated referral link, ReferralLink.active = false should be used as error/invalud refcode
     */
    private void activateReceivedReferralLink(ReferralLink referralLink, String myLeaderboardName,
                                              boolean forceChange) {

        try {
            getCurrentUserReferral(User.getUserId(), new OnUserReferralDataCallback() {
                @Override
                public void onUserReferralDataLoaded(UserReferral userReferral) {
                    //if user can use multiple ref codes, we can enable this flag to change to new referral code
                    // user have been registered on referral collection
                    // e.g. have been referred before / have added points before by adding trx/utang
                    if (userReferral != null) {
                        //ignore if current user already use referral code
                        if (!Utility.isBlank(userReferral.referralCodeInUse)) return;

                        // USER SHOULD NOT GET POINT IF ALREADY HAVE REFERRAL ACCOUNT
                        //update active referral link, disable all others
//                        changeInUseReferralLink(referralLink.code,
//                                User.getUserId(), new OnCurrentReferralChangeCallback() {
//                            @Override
//                            public void OnCurrentReferralCodeChanged(ReferralLink currentReferralLink) {
//                                //update local referral data copy
//                                myReferralData = userReferral;
//                                //give registration points to referrer
//                                updateUserPoints(referralLink.sender,REGISTRATION_POINTS,true);
//                                //store ref code in pref, can be used in fetching target document easily.
//                                ReferralPrefManager.getInstance().setCurrentReferralCode(referralLink.code);
//                            }
//                        });
                    } else {
                        // User haven't been registered in referral collection
                        if (myReferralData != null && !Utility.isBlank(myReferralData.referralCodeInUse)) {
//                            referralLink.active = false;
                            return;
                        }
                        //change active status for new link
                        referralLink.active = true;

                        ReferralReceiver newReceiver = new ReferralReceiver();
                        newReceiver.userId = User.getUserId();
                        newReceiver.earnedPoints = REGISTRATION_POINTS;
                        //set link receiver
                        referralLink.receiver.add(newReceiver);
                        //initial earned point for registration
                        referralLink.earnedPoints = REGISTRATION_POINTS;
                        mFirestore.collection(REFERRAL_LINK_COLLECTION).document(referralLink.code).set(referralLink);

                        getUserReferralCode(
                                referralCode -> {

                                    // USER FIRST REGISTERED, ALL PROFILE & CARD MUST NOT BE COMPLETED
                                    boolean profileCompleted = false;
                                    boolean cardCompleted = false;

                                    UserReferral initialUserReferral = EntityHelper
                                            .getUserReferralForRegistration(
                                                    referralLink, User.getUserId(),
                                                    myLeaderboardName, referralCode,
                                                    profileCompleted, cardCompleted);
                                    myReferralData = initialUserReferral;
                                    mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId()).set(initialUserReferral).addOnCompleteListener(new OnCompleteListener<Void>() {
                                        @Override
                                        public void onComplete(@NonNull Task<Void> task) {
                                            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                                            propBuilder.put("ref_code", referralLink.code);
                                            propBuilder.put("sender", referralLink.sender);
                                            AppAnalytics.trackEvent("referral_registration_success", propBuilder);
                                            updateUserPoints(referralLink.sender, REGISTRATION_POINTS, true, POINT_TYPE_REG);
                                            updateUserPoints(User.getUserId(), REGISTRATION_POINTS, false, POINT_TYPE_REG);
                                        }
                                    });
                                    ReferralPrefManager.getInstance().setCurrentReferralCode(referralLink.code);
                                }
                        );
                    }
                }
            });
        } catch (Exception e) {
            referralLink.active = false;
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    /**
     * add points to given user
     * it's called after every transaction creation and first time registration to referral system
     *
     * @param userId, pointsToAdd
     */
    private void updateUserPoints(String userId, int pointsToAdd, boolean isUpdate, int pointType) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                userReferral.points += pointsToAdd;
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder().put("points", userReferral.points);
                AppAnalytics.trackEvent("referral_earned_points", propBuilder);
                if (isUpdate) {
                    mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId).set(userReferral);
                }
                //update leaderboard
                updateUserRank(userId, userReferral.points, null, pointType);
            }
        });
    }

    private void updateUserPointsWithCallback(String userId, int pointsToAdd, boolean isUpdate,
                                              OnPointsAddedCallback onPointsAddedCallback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                userReferral.points += pointsToAdd;
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder().put("points", userReferral.points);
                AppAnalytics.trackEvent("referral_earned_points", propBuilder);
                if (isUpdate) {
                    mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId)
                            .set(userReferral)
                            .addOnCompleteListener(task -> {
                                onPointsAddedCallback.onPointsAdded();
                            });
                }
                //update leaderboard
//                updateUserRank(userId,userReferral.points,null);
            }
        });
    }

    /**
     * add points to given user
     * it's called after every transaction creation and first time registration to referral system
     *
     * @param userId, pointsToAdd
     * @deprecated not used anymore
     */
    public void addPoints(String userId, long pointsToAdd, OnUserRankDataCallback callback) {
        try {
            getUserRankPoints(userId, new OnUserRankDataCallback() {
                @Override
                public void onUserRankDataLoaded(UserRank userRank) {
                    userRank.points += pointsToAdd;
                    mFirestore.collection(USER_REFERRAL_COLLECTION)
                            .document(userId).set(userRank);
//                    updateUserRank(userId,userRank.points,callback);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    private void addPointsForProfileCompletion(String userId, OnPointsAddedCallback onPointsAddedCallback) {
        try {
            // can only do once.

            DocumentReference docRef = mFirestore
                    .collection(USER_REFERRAL_COLLECTION)
                    .document(User.getUserId());

            // hack for recurring profile completion points
            boolean hasCompletedProfileLocal;

            try {
                hasCompletedProfileLocal = AppConfigManager.getInstance().referralHasEarnedProfile();
            } catch (Exception ex) {
                hasCompletedProfileLocal = false;
            }

            // needed bcs used in lambda
            boolean finalHasCompletedProfileLocal = hasCompletedProfileLocal;

            docRef.get().addOnCompleteListener(task -> {
                DocumentSnapshot snapshot = task.getResult();
                UserReferral userReferral = snapshot.toObject(UserReferral.class);

                if (!userReferral.completedProfile && !finalHasCompletedProfileLocal) {
                    updateUserPoints(User.getUserId(), POINTS_PROFILE_COMPLETED, true, POINT_TYPE_PROFILE);

                    // mark as already completed profile
                    userReferral.completedProfile = true;
                    if (myReferralData != null)
                        myReferralData.completedProfile = true;

                    // store to Firebase
                    mFirestore.collection(USER_REFERRAL_COLLECTION)
                            .document(User.getUserId())
                            .set(userReferral)
                            .addOnCompleteListener(task1 -> {
                                if (onPointsAddedCallback != null)
                                    onPointsAddedCallback.onPointsAdded();
                                AppAnalytics.trackEvent("referral_profile_completion_points");
                            });

                    try {
                        AppConfigManager.getInstance().setReferralHasEarnedProfile(true);
                        AppAnalytics.trackEvent("referral_profile_completion_local_set");
                    } catch (Exception ex) {
                        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                        propBuilder.put("exception", ex.toString());

                        AppAnalytics.trackEvent("referral_profile_completion_local_exception", propBuilder);
                    }
                } else {
                    if (onPointsAddedCallback != null)
                        onPointsAddedCallback.onPointsAdded();
                }
            });
        } catch (Exception ex) {
            Log.e("ReferralRepository", "Exception", ex);
            if (onPointsAddedCallback != null)
                onPointsAddedCallback.onPointsAdded();
        }
    }

    private void addPointsForCardCompletion(String userId) {
        try {
            // can only do once.

            DocumentReference docRef = mFirestore
                    .collection(USER_REFERRAL_COLLECTION)
                    .document(User.getUserId());

            // hack for recurring profile completion points
            boolean hasCompletedCardLocal;

            try {
                hasCompletedCardLocal = AppConfigManager.getInstance().referralHasEarnedCard();
            } catch (Exception ex) {
                hasCompletedCardLocal = false;
            }

            // needed bcs used in lambda
            boolean finalHasCompletedCardLocal = hasCompletedCardLocal;

            docRef.get().addOnCompleteListener(task -> {
                DocumentSnapshot snapshot = task.getResult();
                UserReferral userReferral = snapshot.toObject(UserReferral.class);

                if (!userReferral.completedCard && !finalHasCompletedCardLocal) {
                    updateUserPoints(User.getUserId(), POINTS_CARD_COMPLETED, true, POINT_TYPE_CARD);

                    // mark as already completed profile
                    userReferral.completedCard = true;
                    if (myReferralData != null)
                        myReferralData.completedCard = true;

                    // store to Firebase
                    mFirestore.collection(USER_REFERRAL_COLLECTION)
                            .document(User.getUserId())
                            .set(userReferral);

                    try {
                        AppConfigManager.getInstance().setReferralHasEarnedProfile(true);
                        AppAnalytics.trackEvent("referral_card_completion_local_set");
                    } catch (Exception ex) {
                        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                        propBuilder.put("exception", ex.toString());

                        AppAnalytics.trackEvent("referral_card_completion_local_exception", propBuilder);
                    }

                    AppAnalytics.trackEvent("referral_business_card_completion_points");
                }
            });
        } catch (Exception ex) {
            Log.e("ReferralRepository", "Exception", ex);
        }
    }

    private void addPointsForProfileAndCardCompletion(String userId, boolean profileCompleted,
                                                      boolean cardCompleted) {
        if (profileCompleted && cardCompleted) {
            // do profile first & then card
            addPointsForProfileCompletion(userId, () -> {
                addPointsForCardCompletion(userId);
            });
        } else if (profileCompleted) {
            // do profile
            addPointsForProfileCompletion(userId, null);
        } else if (cardCompleted) {
            // do card
            addPointsForCardCompletion(userId);
        }
    }

    /**
     * add transaction points, it adds points to sender and receiver
     * 1. Sender receives 25 points on 3rd transaction
     * 2. Receiver receives 3 poins per tranasction for first 10 tranaction, 1 point per transaction after that
     */
    public void addTransactionPoints(final BookEntity bookEntity, boolean isForCardOrProfile) {
        try {
            String leaderboardName = bookEntity.businessOwnerName;
            // User haven't been enrolled in referral collection before
            if (myReferralData == null
                    || myReferralData.referralCodeInUse == null
                    || myReferralData.referralCodeInUse.isEmpty()) {
                String leaderboardToUse;
                if (leaderboardName == null || leaderboardName.isEmpty())
                    leaderboardToUse = "BukuWarung"; // Default
                else
                    leaderboardToUse = leaderboardName;

                DocumentReference docRef = mFirestore
                        .collection(USER_REFERRAL_COLLECTION)
                        .document(User.getUserId());
                docRef.get().addOnCompleteListener(task -> {
                    DocumentSnapshot document = null;
                    try {
                        //TODO: [important] should add offline setting while initializing, urgent fix to avoid crash while creating records.
                        //record creation >>>> earning points
                        //FirebaseFirestoreSettings settings = new FirebaseFirestoreSettings.Builder().setPersistenceEnabled(true)
                        document = task.getResult();
                    } catch (Exception e) {
                        e.printStackTrace();
                        return;
                    }
                    boolean profileCompleted = bookEntity.hasCompletedProfile();
                    boolean cardCompleted = bookEntity.hasCompletedCard();

                    if (document != null && !document.exists()) {
                        // TODO: REMOVE CALLBACK HELL T_T
                        getUserReferralCode(referralCode -> {

                            UserReferral initialUserReferral = EntityHelper
                                    .getEmptyUserReferralForRegistration(referralCode, User.getUserId(),
                                            leaderboardToUse, profileCompleted, cardCompleted);
                            myReferralData = initialUserReferral;

                            mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId())
                                    .set(initialUserReferral).addOnCompleteListener(new OnCompleteListener<Void>() {
                                @Override
                                public void onComplete(@NonNull Task<Void> task) {
                                    ReferralPrefManager.getInstance().setRegistration(true);

                                    if (!isForCardOrProfile) {
                                        updateUserPoints(User.getUserId(), POINTS_PER_TRANSACTION, true, POINT_TYPE_TXN);
                                    } else {
                                        //not necessary as points can be added in new referral object  getEmptyUserReferralForRegistration
//                                        addPointsForProfileAndCardCompletion(
//                                                User.getUserId(), profileCompleted, cardCompleted
//                                        );
                                    }

                                    Log.d("ReferralRepository", "Referral points Added!");
                                }
                            });
                        });
                    } else {
                        // User already registered to referral program on another device

                        UserReferral userReferral = document.toObject(UserReferral.class);

                        if (userReferral != null) {
                            addPointsForProfileAndCardCompletion(
                                    User.getUserId(), profileCompleted, cardCompleted
                            );
                        }

                        if (!isForCardOrProfile)
                            updateUserPoints(User.getUserId(), POINTS_PER_TRANSACTION, true, POINT_TYPE_TXN);
                    }
                });
            } else {
                // User have been enrolled in referral collection before
                String refCode = myReferralData.referralCodeInUse;

                mFirestore.collection(REFERRAL_LINK_COLLECTION).document(refCode).get().addOnCompleteListener(new OnCompleteListener<DocumentSnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<DocumentSnapshot> task) {
                        if (task.isSuccessful()) {
                            DocumentSnapshot document = task.getResult();
                            if (document.exists()) {
                                ReferralLink referralLink = document.toObject(ReferralLink.class);

                                if (referralLink == null) return;

                                ReferralReceiver currentReferralReceiver = null;

                                for (ReferralReceiver receiver : referralLink.receiver) {
                                    if (receiver.userId.equalsIgnoreCase(User.getUserId())) {
                                        currentReferralReceiver = receiver;
                                    }
                                }

                                if (!isForCardOrProfile) {
                                    if (currentReferralReceiver != null) {
                                        int pointsToAdd = 0;
                                        currentReferralReceiver.transactionCount++;
                                        if (currentReferralReceiver.transactionCount == 1) {
                                            pointsToAdd = BONUS_TRANSACTION_POINTS;
                                            updateUserPoints(User.getUserId(), pointsToAdd, true, POINT_TYPE_TARGET_COMPLETE);
                                            updateUserPoints(myReferralData.sender, SENDER_TARGET_COMPLETION_POINTS, true, POINT_TYPE_TARGET_COMPLETE);
                                        } else if (currentReferralReceiver.transactionCount <= 10) {
                                            pointsToAdd = BONUS_TRANSACTION_POINTS;
                                            updateUserPoints(User.getUserId(), pointsToAdd, true, POINT_TYPE_INITIAL_TXN);
                                        } else {
                                            pointsToAdd = POINTS_PER_TRANSACTION;
                                            updateUserPoints(User.getUserId(), pointsToAdd, true, POINT_TYPE_TXN);
                                        }
                                        //update transaction count
                                        currentReferralReceiver.earnedPoints = currentReferralReceiver.earnedPoints + pointsToAdd;
                                        referralLink.earnedPoints = referralLink.earnedPoints + pointsToAdd;
                                        mFirestore.collection(REFERRAL_LINK_COLLECTION).document(refCode).set(referralLink);
                                    } else {
                                        // Just add one points per trx
                                        updateUserPoints(User.getUserId(), POINTS_PER_TRANSACTION, true, POINT_TYPE_TXN);
                                    }
                                }

                                boolean profileCompleted = bookEntity.hasCompletedProfile();
                                boolean cardCompleted = bookEntity.hasCompletedCard();

                                new Handler().postDelayed(() -> {
                                    addPointsForProfileAndCardCompletion(
                                            User.getUserId(), profileCompleted, cardCompleted
                                    );
                                }, 1500); // delayed so original referral trx can finish first.

                            } else {
                                Log.d(TAG, "link doesn't exist", task.getException());
                            }
                        } else {
                            Log.d(TAG, "error getting link; ", task.getException());
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    /**
     * send user points to leaderboard.
     * leaderboard backend keeps track of current ranking.
     *
     * @param userId
     * @param points
     * @param callback //get updated user rank
     */
    private void updateUserRank(String userId, long points, OnUserRankDataCallback callback, int type) {
        RequestQueue queue = Volley.newRequestQueue(Application.getAppContext());
        Map<String, String> params = new HashMap<String, String>();
        params.put("id", userId);
        params.put("type", String.valueOf(type));
        params.put("points", String.valueOf(points));

        final String URL = ReferralPrefManager.getInstance().getLeaderboardServerIP() + "updatepoint";

        JsonObjectRequest req = new JsonObjectRequest(URL, new JSONObject(params),
                new Response.Listener<JSONObject>() {
                    @Override
                    public void onResponse(JSONObject response) {
                        try {
                            if (callback != null) {
                                UserRank userRank = new UserRank(userId, points, response.getInt("rank"));
                                callback.onUserRankDataLoaded(userRank);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                VolleyLog.e("Error: ", error.getMessage());
            }
        }) {
            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> params = new HashMap<String, String>();
                params.put("Content-Type", "application/json; charset=UTF-8");
                params.put("X-Authorization-Firebase", SessionManager.getInstance().getBukuwarungToken());
                return params;
            }
        };
        req.setRetryPolicy(new DefaultRetryPolicy(30000, 0,
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));
        queue.add(req);
    }

    /**
     * handles users who have not received or shared any referral code so far.
     * it initializes points, rank for new referrer/referee
     *
     * @param referralLink
     * @param bookEntity
     * @return boolean //status
     */
    private boolean registerReferralCodeSender(ReferralLink referralLink, BookEntity bookEntity,
                                               String referralCode) {
        try {
            String senderLeaderboardName = bookEntity.businessOwnerName;

            //TODO: point calculation logic for sender
            if (myReferralData != null && myReferralData.points > 0) {
                ReferralPrefManager.getInstance().setRegistration(true);
                return true;
            }


            boolean profileCompleted = false;
            boolean cardCompleted = false;

            try {
                if (bookEntity != null) {
                    profileCompleted = bookEntity.hasCompletedProfile();
                    cardCompleted = bookEntity.hasCompletedCard();
                }
            } catch (Exception ex) {
                Log.e("ReferralRepository", "Exception", ex);
            }

            UserReferral userReferral = EntityHelper
                    .getUserReferralForSharing(referralLink, referralLink.sender,
                            senderLeaderboardName, referralCode,
                            profileCompleted, cardCompleted);

            mFirestore.collection(USER_REFERRAL_COLLECTION)
                    .document(User.getUserId()).set(userReferral);

            addPointsForProfileAndCardCompletion(
                    User.getUserId(), bookEntity.hasCompletedProfile(), bookEntity.hasCompletedCard()
            );

            ReferralPrefManager.getInstance().setRegistration(true);
        } catch (Exception e) {
            ReferralPrefManager.getInstance().setRegistration(false);
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return false;
        }
        return true;
    }

    /**
     * get referralLink by using referral code received in deeplink parameter.
     * Handles Referral Flow Step 3
     *
     * @param referralCode // referralCode received in deeplink parameter
     * @return boolean //status
     */
    private void getReferralLinkByCode(String referralCode, OnReferralLinkDataCallback dataCallBack) {
        DocumentReference docRef = mFirestore.collection(REFERRAL_LINK_COLLECTION).document(referralCode);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                ReferralLink referralLink = documentSnapshot.toObject(ReferralLink.class);
                dataCallBack.onReferralLinkDataLoaded(referralLink);
            }
        });
    }

    /**
     * get User referral code by
     *
     * @return String //referral Code
     */
    public String getUserReferralCode(OnUserReferralCodeCallback callback) {
        if (myReferralData != null && myReferralData.myReferralCode != null
                && !myReferralData.myReferralCode.isEmpty()) {
            callback.onUserReferralCodeLoaded(myReferralData.myReferralCode);
        } else {
            RequestQueue requestQueue = Volley.newRequestQueue(Application.getAppContext());
            StringRequest stringRequest = new
                    StringRequest(Request.Method.GET, ReferralPrefManager.getInstance().getBaseApiUrl(),
                    new Response.Listener<String>() {
                        @Override
                        public void onResponse(String response) {
                            try {
                                StringBuilder codeSb = new StringBuilder(ReferralUtils.getCodePrefix());
                                JSONObject jsonObject = new JSONObject(response);
                                codeSb.append(jsonObject.getString("code"));

                                updateMyReferralCode(codeSb.toString());
                                callback.onUserReferralCodeLoaded(codeSb.toString());
                            } catch (JSONException e) {
                                callback.onUserReferralCodeLoaded(null);
                            }
                        }
                    }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    FirebaseCrashlytics.getInstance().recordException(error.getCause());
                }
            });
            requestQueue.add(stringRequest);
        }
        return null;
    }

    /**
     * load user rank, point info asynchronously
     *
     * @return boolean //status
     */
    public void getUserRankPoints(OnUserRankDataCallback callback) {
        getUserRankPoints(User.getUserId(), callback);
    }

    private void getUserRankPoints(String userId, OnUserRankDataCallback callback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(userId);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                if (userReferral == null) {
                    callback.onUserRankDataLoaded(null);
                    return;
                }
                UserRank userRank = new UserRank(userReferral.leaderboardName, userReferral.points, userReferral.rank);
                getUpdatedUserRank(userId, userRank, callback);
//                callback.onUserRankDataLoaded(userRank);
            }
        });
    }

    private void getUpdatedUserRank(String userId, UserRank userRank, OnUserRankDataCallback callback) {
        RequestQueue requestQueue = Volley.newRequestQueue(Application.getAppContext());
        StringRequest stringRequest = new StringRequest(Request.Method.GET,
                ReferralPrefManager.getInstance().getLeaderboardServerIP() + "rank?userId=" + userId + "&&point=" + userRank.points,
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        try {
                            JSONObject jsonObject = new JSONObject(response);
                            userRank.rank = jsonObject.getInt("rank");
                            callback.onUserRankDataLoaded(userRank);
                        } catch (JSONException e) {
                            callback.onUserRankDataLoaded(null);
                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                Log.d("ReferralRepository", "Get updated rank error ", error);
                callback.onUserRankDataLoaded(null);
                System.out.println("That didn't work!" + error);
            }
        }) {
            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> params = new HashMap<String, String>();
                params.put("X-Authorization-Firebase", SessionManager.getInstance().getBukuwarungToken());
                return params;
            }
        };
        stringRequest.setRetryPolicy(new DefaultRetryPolicy(30000, 0,
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));
        requestQueue.add(stringRequest);
    }


    /**
     * load list of user rank for leaderboard
     */
    public void getLeaderBoard(OnLeaderBoardCallback callback, int sortBy) {
        //TODO remove hardcoded limit
        mFirestore.collection(USER_REFERRAL_COLLECTION).orderBy("points", Query.Direction.DESCENDING).limit(AppConfigManager.getInstance().getLeaderBoardSize()).get()
                .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<QuerySnapshot> task) {
                        List<UserRank> leaderboard = new ArrayList<>();
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                try {
                                    int idx = 1;
                                    for (QueryDocumentSnapshot doc : task.getResult()) {
                                        UserReferral userReferral = doc.toObject(UserReferral.class);
                                        UserRank userRank = new
                                                UserRank(userReferral.leaderboardName, userReferral.points, userReferral.rank);
                                        userRank.rank = idx;
                                        leaderboard.add(userRank);
                                        idx++;
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    FirebaseCrashlytics.getInstance().recordException(e);
                                }
                            }

                        }
                        if (leaderboard.size() < REFERRAL_TARGET) {
                            //TODO: load dummy data from backend API
//                            leaderboard.addAll(ReferralUtils.getDummyLeaderboadData(leaderboard.size(),REFERRAL_TARGET - leaderboard.size()));
                        }
//                        Collections.sort(leaderboard, new UserRankComparator(sortBy));
                        callback.onLeaderBoardLoaded(leaderboard);
                    }
                });
    }

    /**
     * load user referral complete data, UserRank, received referral, sent referral
     *
     * @return boolean //status
     */
    public void updateLeaderboardName(String leaderboardName, OnLeaderBoardUpdateNameCallback callback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId());
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);

                // userReferral would be null if they haven't shared anything before
                if (userReferral != null) {
                    userReferral.leaderboardName = leaderboardName;

                    //update cached referral data
                    myReferralData = userReferral;
                    mFirestore.collection(USER_REFERRAL_COLLECTION)
                            .document(User.getUserId()).set(userReferral);
                }

                callback.onLeaderBoardUpdateNameSuccess();
            }
        });
    }

    /**
     * load user referral complete data, UserRank, received referral, sent referral
     *
     * @return boolean //status
     */
    private void getUserReferralData(OnUserReferralDataCallback callback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId());
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                callback.onUserReferralDataLoaded(userReferral);
            }
        });
    }


    public void getUserReferralDataWithSentLinks(OnUserReferralCompleteDataCallback callback) {
        DocumentReference docRef = mFirestore.collection(USER_REFERRAL_COLLECTION).document(User.getUserId());
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                UserReferral userReferral = documentSnapshot.toObject(UserReferral.class);
                UserReferralModel userReferralModel = new UserReferralModel(userReferral);
                getRelatedReferralLinks(User.getUserId(), "sender", new OnUserReferralHistoryCallback() {
                    @Override
                    public void onUserReferralHistoryLoaded(List<ReferralLink> referralLinkList) {
                        userReferralModel.sent = referralLinkList;
                        callback.onUserReferralCompleteDataLoaded(userReferralModel);
                    }
                });
            }
        });
    }

    /**
     * fetch list of referral links shared/received by user
     *
     * @param userId
     * @param role     //receiver or sender
     * @param callback // list of sent/ received links based on role param
     */
    public void getRelatedReferralLinks(String userId, String role, OnUserReferralHistoryCallback callback) {
        List<ReferralLink> referralLinkList = new ArrayList<>();
        try {
            mFirestore.collection(REFERRAL_LINK_COLLECTION).whereEqualTo(role, userId).get()
                    .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                        @Override
                        public void onComplete(@NonNull Task<QuerySnapshot> task) {
                            if (task.isSuccessful()) {
                                int itemCount = task.getResult().size();
                                if (itemCount > 0) {
                                    for (QueryDocumentSnapshot doc : task.getResult()) {
                                        ReferralLink referralLink = doc.toObject(ReferralLink.class);
                                        if (!referralLink.receiver.isEmpty()) {
                                            referralLinkList.add(referralLink);
                                        }
                                    }
                                }
                            }
                            callback.onUserReferralHistoryLoaded(referralLinkList);
                        }
                    });
        } catch (Exception e) {
            callback.onUserReferralHistoryLoaded(referralLinkList);
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void newRetroCall() {
        callEndpoints(RetrofitClient.getReferralClient(BuildConfig.API_BASE_URL));
    }

    public void callEndpoints(@NonNull Retrofit retrofit) {
        ReferralDataSource referralDataSource = retrofit.create(ReferralDataSource.class);

        try {
            PackageInfo pInfo = Application.getAppContext().getPackageManager().getPackageInfo(Application.getAppContext().getPackageName(), 0);
            ReferralDataPostUpgradeRequestPayload upgradeRequestObj = new ReferralDataPostUpgradeRequestPayload(SetupManager.getInstance().getInstallVersionName(),pInfo.versionName);

            new Handler(Looper.getMainLooper()).post(() -> referralDataSource.postUpgradeReferralData(upgradeRequestObj, new Continuation<ApiResponse<ReferralUpdateResponse>>() {
                @NonNull
                @Override
                public CoroutineContext getContext() {
                    return EmptyCoroutineContext.INSTANCE;
                }

                @Override
                public void resumeWith(@NonNull Object o) {

                }
            }));
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    //TODO: don't keep in repository, move to seperate package
    //callback function to load referral linkn status asynchronously
    public interface OnReferralLinkDataCallback {
        void onReferralLinkDataLoaded(ReferralLink referralLink);
    }

    //callback function to load user's referral data asynchronously
    public interface OnUserReferralDataCallback {
        void onUserReferralDataLoaded(UserReferral userReferral);
    }

    //callback function to load user's referral data asynchronously
    public interface OnPaymentReferralDataCallback {
        void onUserReferralDataLoaded(PaymentUserReferral userReferral);
    }

    public interface OnPaymentNewReferralDataCallback {
        void onUserNewReferralDataLoaded(ReferralDataResponsePayload data);
    }

    public interface OnUserReferralCompleteDataCallback {
        void onUserReferralCompleteDataLoaded(UserReferralModel userReferralModel);
    }

    //callback function to load user's referral data asynchronously
    public interface OnCurrentReferralChangeCallback {
        void OnCurrentReferralCodeChanged(ReferralLink currentReferralLink);
    }

    //callback function to load user's referral data asynchronously
    public interface OnLeaderBoardCallback {
        void onLeaderBoardLoaded(List<UserRank> leaderBoard);
    }

    //callback function to load user's referral data asynchronously
    public interface OnValidateAndRegisterReferralCode {
        void onRegisterReferralCode(Boolean validationStatus, String registeredRefCode);
    }

    //callback function to load user's referral data asynchronously
    public interface OnUserRankDataCallback {
        void onUserRankDataLoaded(UserRank userRank);
    }

    //callback function to load user's referral code asynchronously
    public interface OnUserReferralCodeCallback {
        void onUserReferralCodeLoaded(String referralCode);
    }

    public interface OnUserReferralHistoryCallback {
        void onUserReferralHistoryLoaded(List<ReferralLink> referralLinkList);
    }

    public interface OnPointsAddedCallback {
        void onPointsAdded();
    }

    //callback function to update leaderboard name
    public interface OnLeaderBoardUpdateNameCallback {
        void onLeaderBoardUpdateNameSuccess();
    }
}

