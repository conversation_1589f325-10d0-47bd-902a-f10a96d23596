package com.bukuwarung.database.repository;

import android.content.Context;

import androidx.lifecycle.LiveData;
import androidx.room.Transaction;

import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.InventoryDao;
import com.bukuwarung.database.dao.ProductDao;
import com.bukuwarung.database.dao.TransactionDao;
import com.bukuwarung.database.dto.FrequentProductDto;
import com.bukuwarung.database.dto.TransactionItemDto;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.InventoryOperationType;
import com.bukuwarung.database.entity.ProductCategoryEntity;
import com.bukuwarung.database.entity.ProductEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppExpenseTransSyncManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;


public class ProductRepository {
    private static ProductRepository sInstance;
    private AppDatabase mDatabase;
    private ProductDao productDao;
    private InventoryDao inventoryDao;
    private TransactionDao transactionDao;

    public ProductRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        this.productDao = mDatabase.productDao();
        this.inventoryDao = mDatabase.inventoryDao();
        this.transactionDao = mDatabase.transactionDao();
    }

    public static ProductRepository getInstance(Context context) {
        try {
            if(context == null) {
                FirebaseCrashlytics.getInstance().log("context is null: ProductRepository");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (sInstance == null) {
                synchronized (ProductRepository.class) {
                    if (sInstance == null) {
                        sInstance = new ProductRepository(context);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }


    public List<ProductEntity> getProductsByBook(String bookId) {
        return this.productDao.getProductByBusiness(bookId);
    }

    public LiveData<List<ProductEntity>> getLiveProductsByBook(String bookId) {
        return this.productDao.getLiveProductByBusiness(bookId);
    }

    /**
     * get products list including suspended products for transaction edit.
     *
     * @param bookId
     * @return
     */
    public List<ProductEntity> getProductListIncludingDisabledStock(String bookId) {
        return this.productDao.getProductListIncludingDisabledStock(bookId);
    }

    public LiveData<List<ProductEntity>> getLiveProductListIncludingDisabledStock(String bookId) {
        return this.productDao.getLiveProductListIncludingDisabledStock(bookId);
    }

    public List<ProductEntity> getProductsByName(String name) {
        return this.productDao.getProductByName(name);
    }

    public List<ProductEntity> getProducstById(String id){
        return this.productDao.getProductsById(id);
    }

    public ProductEntity getProductsById(String productId) {
        return this.productDao.getProductById(productId);
    }

    public ProductEntity getAllProductById(String productId) {
        return this.productDao.getAllProductById(productId);
    }

    public ProductEntity getSuspendedProductsById(String productId) {
        return this.productDao.getProductById(productId);
    }

    public LiveData<ProductEntity> getProductsLiveDataById(String productId) {
        return this.productDao.getProductLiveDataById(productId);
    }

    public void insertProductList(List<ProductEntity> productEntities) {
        this.productDao.insertAll(productEntities);
    }

    public void insertInventoryHistoryList(List<InventoryHistoryEntity> inventoryHistoryEntityList) {
        this.inventoryDao.insertAll(inventoryHistoryEntityList);
    }

    /**
     * add new product, for inventory tracking track_inventory should be set to 1.
     * default behaviour is to not track inventory for existing products.
     * products added from stock tab should have track_inventory = 1
     *
     * @param productEntity
     */
    public void insertProduct(ProductEntity productEntity) {
        productEntity = EntityHelper.fillProductMetadata(productEntity);
        this.productDao.insert(productEntity);
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productEntity);
    }

    public void insertProductToDao(ProductEntity productEntity) {
        productEntity = EntityHelper.fillProductMetadata(productEntity);
        this.productDao.insert(productEntity);
    }

    /*
     * update product and sync with backend
     * update product name, stock, min stock and measurements
     * @param productEntity
     */
    public void updateProduct(ProductEntity productEntity) {
        productEntity = EntityHelper.fillProductMetadata(productEntity);
        this.productDao.updateProduct(productEntity);
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productEntity);
    }

    public void updateProductToDao(ProductEntity productEntity) {
        productEntity = EntityHelper.fillProductMetadata(productEntity);
        this.productDao.updateProduct(productEntity);
    }

    public boolean checkProductUsedForTransaction(String productId) {
        int count = this.transactionDao.checkProductUsedForTransaction(productId);
        return count != 0;
    }

    /**
     * hard delete product from local database, it will remove product details from db.
     * @param productEntity
     */
    public void deleteProduct(ProductEntity productEntity) {
        // deleting the local data
        this.productDao.deleteProduct(productEntity);

        // changing the state on firestore
        productEntity.deleted = 1;
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productEntity);
    }

    /**
     * hard delete product from local database, it will remove product details from db.
     * @param productId String
     */
    public void deleteProductByProductId(String productId) {
        // deleting the local data
        ProductEntity productEntity = productDao.getProductById(productId);
        this.productDao.deleteProduct(productEntity);

        // changing the state on firestore
        productEntity.deleted = 1;
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productEntity);
    }

    public void deleteProductFromDao(ProductEntity productEntity) {
        // deleting the local data
        this.productDao.deleteProduct(productEntity);
    }

    @Transaction
    public void mergeGuestRecords() {
        this.productDao.mergeGuestProductData(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
    }

    /*
     * stop inventory tracking for product. merchant can still sell product under transaksi tab
     * disable inventory will remove product from stock management tab
     * @param productEntity
     */
    public void suspendInventoryProduct(String productId){
        productDao.suspendInventoryProduct(productId);
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productDao.getProductById(productId));
    }

    /*
     * hard delete product from local database, it will remove product details from db.
     * @param productEntity
     */
    public void enableInventoryTracking(String productId){
        productDao.disableInventoryTracking(productId);
    }

    @Transaction
    public void updateFavourite(ProductEntity productEntity) {
        productDao.update(productEntity);
    }

    @Transaction
    public String updateStockQuantity(Double quantityChange, ProductEntity productEntity, InventoryOperationType operationType, TransactionItemDto product, double buyingPrice, double sellingPrice, String measurementName) {
        InventoryHistoryEntity inventoryHistory = new InventoryHistoryEntity(Utility.uuid(), productEntity.productId, productEntity.bookId, productEntity.stock, productEntity.minimumStock, 0.0);
        try {
            int totalStockAdd = inventoryDao.totalStockAdd(productEntity.productId);
            int totalStockRemove = inventoryDao.totalStockRemove(productEntity.productId);

            /**
             * new product doesn't have any addition/removal history
             * so we need to preserve the default stock
             * */
            if (totalStockAdd >= 0 && totalStockRemove >= 0) {
                productEntity.stock = Double.valueOf(totalStockAdd - totalStockRemove)+productEntity.initialStock;
            }else{
                productEntity.stock = productEntity.initialStock;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if (operationType == InventoryOperationType.ADD_STOCK || operationType == InventoryOperationType.EXPENSE_TRANSACTION) {
            productEntity.stock = productEntity.stock + quantityChange;
        } else if (operationType == InventoryOperationType.REMOVE_STOCK || operationType == InventoryOperationType.SALE_TRANSACTION) {
            productEntity.stock = productEntity.stock - quantityChange;
        }
        if (product!=null) {
            productEntity.favourite = product.isFavourite;
        }

        inventoryHistory.currentStock = productEntity.stock;
        inventoryHistory.operationType = operationType;
        inventoryHistory.quantityChange = quantityChange;

        inventoryHistory.buyingPrice = buyingPrice;
        inventoryHistory.sellingPrice = sellingPrice;
        inventoryHistory.measurementName = measurementName;

        EntityHelper.fillEntityMetadata(inventoryHistory);
        EntityHelper.fillUpdateEntityMetadata(productEntity);
        inventoryDao.insert(inventoryHistory);
        productDao.update(productEntity);
        AppExpenseTransSyncManager.getInstance().updateInventoryHistory(inventoryHistory);
        AppExpenseTransSyncManager.getInstance().updateUserProducts(productEntity);
        return inventoryHistory.historyId;
    }

    public Integer getProductCount() {
        return productDao.getProductCount();
    }

    public void deleteProductInvetoryHistoryById(@Nullable String inventoryHistoryId) {
        InventoryHistoryEntity inventoryHistoryEntity = inventoryDao.getInventoryHistoryById(inventoryHistoryId);
        if (inventoryHistoryEntity.operationType == InventoryOperationType.ADD_STOCK || inventoryHistoryEntity.operationType == InventoryOperationType.EXPENSE_TRANSACTION) {
            inventoryDao.adjustInventoryBeforeStockDelete(-inventoryHistoryEntity.quantityChange,inventoryHistoryEntity.productId,inventoryHistoryEntity.createdAt);
        } else if (inventoryHistoryEntity.operationType == InventoryOperationType.REMOVE_STOCK || inventoryHistoryEntity.operationType == InventoryOperationType.SALE_TRANSACTION) {
            inventoryDao.adjustInventoryBeforeStockDelete(inventoryHistoryEntity.quantityChange,inventoryHistoryEntity.productId,inventoryHistoryEntity.createdAt);
        }
        inventoryDao.deleteByHistoryId(inventoryHistoryId);
    }

    public String getFrequentProductName(String bookId, String yesterdayDateTime) {
        List<FrequentProductDto> frequentProductDtos = this.transactionDao.getFrequentProductId(bookId, yesterdayDateTime);
        if(!ListUtils.isEmpty(frequentProductDtos)) {
            ProductEntity productEntity = this.productDao.getProductById(frequentProductDtos.get(0).productId);
            return productEntity.name;
        }
        return "";
    }

    public List<FrequentProductDto> getTopFiveFrequentProductName(String bookId, String yesterdayDateTime) {
        List<FrequentProductDto> frequentProductDtos = this.transactionDao.getFrequentProductId(bookId, yesterdayDateTime);

        for(FrequentProductDto productDto : frequentProductDtos){
            ProductEntity productEntity = this.productDao.getProductById(productDto.productId);
            productDto.productName = productEntity.name;
        }

        return frequentProductDtos;
    }

    public List<FrequentProductDto> getFrequentProductByDateRange(String bookId, String startDate,String endDate) {
        List<FrequentProductDto> frequentProductDtos = this.transactionDao.getFrequentProductByDateRange(bookId,startDate,endDate);

        for(FrequentProductDto productDto : frequentProductDtos){
            ProductEntity productEntity = this.productDao.getProductById(productDto.productId);
            productDto.productName = productEntity.name;
        }

        return frequentProductDtos;
    }

    public List<FrequentProductDto> getFrequentProductNamesInDateRange(String bookId,String sdate,String edate) {
        List<FrequentProductDto> frequentProductDtos = this.transactionDao.getFrequentProductIdInDateRange(bookId,sdate,edate);

        Collections.reverse(frequentProductDtos);

        Collections.sort(frequentProductDtos,  new Comparator<FrequentProductDto>() {
            @Override
            public int compare(FrequentProductDto one, FrequentProductDto other) {
                return other.productCount.compareTo(one.productCount);
            }
        });

        for(FrequentProductDto productDto : frequentProductDtos){
            ProductEntity productEntity = this.productDao.getProductById(productDto.productId);
            productDto.productName = productEntity.name;
        }

        return frequentProductDtos;
    }

    public ProductCategoryEntity getBestSellingCategory(@Nullable String businessId, @NotNull String startDate, @NotNull String endDate) {
        return transactionDao.getBestSellingCategory(businessId,startDate,endDate/*, TransactionEntityType.CASH_TRANSACTION*/);
//        return transactionDao.getBestSellingCategory(businessId/*,TransactionEntityType.CASH_TRANSACTION*/);
    }

    @NotNull
    public Double getTotalBuyingPriceForAllCategoriesWithProduct(@Nullable String businessId, @NotNull String startDate, @NotNull String endDate) {
        return transactionDao.getTotalBuyingPriceForAllCategoriesWithProduct(businessId,startDate,endDate);
    }

    @NotNull
    public Double getTotalSellingPriceForAllCategoriesWithProduct(@Nullable String businessId, @NotNull String startDate, @NotNull String endDate) {
        return transactionDao.getTotalSellingPriceForAllCategoriesWithProduct(businessId,startDate,endDate);
    }

    @NotNull
    public Integer getTotalTransactionsWithProducts(@Nullable String businessId, @NotNull String startDate, @NotNull String endDate) {
        return transactionDao.getTotalTransactionsWithProducts(businessId,startDate,endDate);
    }
}
