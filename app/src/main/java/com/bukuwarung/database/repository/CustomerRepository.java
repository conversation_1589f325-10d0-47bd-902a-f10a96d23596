package com.bukuwarung.database.repository;

import android.content.Context;

import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.BusinessDao;
import com.bukuwarung.database.dao.CustomerDao;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.Calendar;
import java.util.List;

import androidx.lifecycle.LiveData;

import javax.annotation.Nullable;


public class CustomerRepository {
    private static CustomerRepository sInstance;
    private CustomerDao customerDao;
    private BusinessDao businessDao;
    private AppDatabase mDatabase;

    public CustomerRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        customerDao = this.mDatabase.customerDao();
    }

    public static CustomerRepository getInstance(Context context) {
        try {
            if(context == null) {
                FirebaseCrashlytics.getInstance().log("context is null: CustomerRepository");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (sInstance == null) {
                synchronized (CustomerRepository.class) {
                    if (sInstance == null) {
                        sInstance = new CustomerRepository(context);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }

    public CustomerEntity getCustomerById(String cstId) {
        return customerDao.getCustomerById(cstId);
    }

    public CustomerEntity getCustomerByMobileNumber(String mobileNumber) {
        return customerDao.getCustomerByMobileNumber(mobileNumber);
    }

    public String saveCustomer(String businessId, String name, String address, String phone, String countryCode, Double balance, String reportId, boolean smsStatus, String customerId) {
        String uuid = customerId != null && !customerId.isEmpty() ? customerId : Utility.uuid();
        CustomerEntity customerEntity = new CustomerEntity(businessId, uuid);
        customerEntity.name = name;
        customerEntity.address = address;
        customerEntity.phone = phone;
        customerEntity.countryCode = countryCode;
        customerEntity.balance = balance;
        customerEntity.deleted = Integer.valueOf(0);
        customerEntity.altCustomerId = reportId;
        customerEntity.language = SessionManager.getInstance().getAppLanguage();
        customerEntity.lastModifiedAt = Utility.getCurrentTime();
        customerEntity.imageUploadPending = Integer.valueOf(0);
        customerEntity.enableSmsAlerts = smsStatus ? 1 : 0;
        EntityHelper.fillCstMetadata(customerEntity);

        // inserting customer to SQLite
        try {
            long res = this.customerDao.insert(customerEntity);
            // inserting customer to SQLite success
            FirebaseCrashlytics.getInstance().log("[SQLite][CustomerEntity]Success.ID:" + uuid);
        } catch (Exception ex) {
            // inserting customer to SQLite fails
            FirebaseCrashlytics.getInstance().log("[SQLite][CustomerEntity]Failed.ID:" + uuid + ",Trace:" + ex.toString());
        }

        //AppCustomerSyncManager.getInstance().updateCustomer(customerEntity);
        return uuid;
    }

    public String saveCustomer(String name, String phone) {
        try {
            // some cst didn't have phone no's but considered different cst.
            if (!Utility.isBlank(phone)) {
                String code = SessionManager.getInstance().getCountryCode();

                List<CustomerEntity> customerEntity = getCustomerByNumber(User.getBusinessId(), code,
                        phone);

                String phoneWithZero = "0" + phone;
                List<CustomerEntity> customerEntityWithZero = getCustomerByNumber(User.getBusinessId(), code,
                        phoneWithZero);

                if ((customerEntity != null && !customerEntity.isEmpty())
                        || (customerEntityWithZero != null && !customerEntityWithZero.isEmpty()))
                    return customerEntity.get(0).customerId;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        CustomerEntity customer = new CustomerEntity(User.getBusinessId(), Utility.uuid());
        customer.name = name;
        customer.countryCode = SessionManager.getInstance().getCountryCode();
        customer.phone = phone;

        EntityHelper.fillCstMetadata(customer);
        this.customerDao.insert(customer);
        AppCustomerSyncManager.getInstance().updateCustomer(customer);

        return customer.customerId;
    }

    public List getCustomerByNumber(String businessId, String countryCode, String phone) {
        return customerDao.getCustomerByNumber(businessId, countryCode, phone);
    }

    public LiveData<List<CustomerEntity>> getCustomersByBusiness(String businessId) {
        return customerDao.getCustomersByBusiness(businessId);
    }

    public LiveData<List<CustomerEntity>> getCustomersByBusinessWithPastDueDate(String businessId, String currentDate) {
        return customerDao.getCustomersByBusinessWithPastDueDate(businessId, currentDate);
    }

    public Integer getCustomerCountForBusiness(String businessId) {
        return customerDao.getCustomerCountForBusiness(businessId);
    }

    public LiveData<CustomerEntity> getObservableCustomerById(String customerId) {
        return customerDao.getObservableCustomerById(customerId);
    }

    public List<CustomerEntity> getCustomerWithTodaysDueDate() {
        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        String timeString = Utility.getStorableDateString(calendar.getTime());
        return customerDao.getCustomerWithTodaysDueDate(timeString);
    }

    /**
     * get list of customers who already have a reminder date set by user.
     *
     * @param bookId
     * @return list of customers sorted by due_date in decending order
     */
    public LiveData<List<CustomerEntity>> getObservableCustomerWithReminderDate(String bookId) {
        return customerDao.getObservableCustomersWithReminderDate(bookId);
    }

    /**
     * get list of customers who don't have reminder date yet.
     *
     * @param bookId
     * @return list of customers sorted by amount in decending order
     */
    public LiveData<List<CustomerEntity>> getObservableCustomerWithoutReminderDate(String bookId) {
        return customerDao.getObservableCustomersWithoutReminderDate(bookId);
    }

    public void setCustomerReminderDate(String userId, String deviceId, String customerId, String reminderDate) {
        long currentTime = AppIdGenerator.getCurrentTime();
        int res = this.customerDao.setReminderDate(customerId, reminderDate, currentTime, userId, deviceId);
        CustomerEntity customerEntity = getCustomerById(customerId);
        AppCustomerSyncManager.getInstance().updateCustomer(customerEntity, true);

    }

    public int updateExistingCustomer(String userId, String deviceId, String customerId, String name, String address, String phone, String country_code, int delete) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        int res = this.customerDao.updateCustomerById(customerId, name, address, phone, country_code, delete, updatedAt, userId, deviceId);
        CustomerEntity cst = this.customerDao.getCustomerById(customerId);
        AppCustomerSyncManager.getInstance().updateCustomer(cst);
        if (delete == 1) {
            this.customerDao.deleteCustomerTransactions(customerId);
        }
        return res;
    }

    public void updateCustomerImageInfo(String customerId, String imageUrl, int uploadedFlg) {
        this.customerDao.updateImageInfo(customerId, imageUrl, uploadedFlg);
        CustomerEntity customerEntity = getCustomerById(customerId);
        AppCustomerSyncManager.getInstance().updateCustomer(customerEntity);
    }

    public List getCustomersWithPhone(String bookId, String countryCode, String phone) {
        return this.customerDao.getCustomerWithPhone(bookId, countryCode, phone);
    }

    public int updateEnableSmsAlertsForCustomer(String userId, String deviceId, String customerId, Integer smsFlg) {
        int res = this.customerDao.updateSMSFlag(customerId, smsFlg, userId, deviceId);
        CustomerEntity customerEntity = getCustomerById(customerId);
        return res;
    }

    public int updateCustomerLanguage(String updatedBy, String deviceId, String customerId, Integer newLang) {
        int res = this.customerDao.updateLanguage(customerId, newLang, updatedBy, deviceId);
        CustomerEntity customerEntity = getCustomerById(customerId);
        AppCustomerSyncManager.getInstance().updateCustomer(customerEntity);
        return res;
    }

    public Integer countAllVisibleCustomersSync(String str) {
        return this.customerDao.countAllVisibleSync(str);
    }

    public void insertCustomerSync(CustomerEntity customerEntity) {
        this.customerDao.insert(customerEntity);
    }

    public void insertCustomerSync(List<CustomerEntity> customerEntity) {
        this.customerDao.insert(customerEntity);
    }

    public void retryCustomerSync(CustomerEntity customerEntity) {
        this.customerDao.insertRetry(customerEntity);
    }

    public List<CustomerEntity> getCustomersListForContacts(String businessId) {
        return customerDao.getCustomersListForContacts(businessId);
    }

    public int deleteCustomerById(String customerId) {
        return customerDao.deleteCustomerById(customerId);
    }

    public void insertList(List<CustomerEntity> customerEntityList) {
        this.customerDao.insertList(customerEntityList);
    }
}
