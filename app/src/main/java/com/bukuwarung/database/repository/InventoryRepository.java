package com.bukuwarung.database.repository;

import android.content.Context;

import androidx.lifecycle.LiveData;
import androidx.room.Transaction;

import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.InventoryDao;
import com.bukuwarung.database.dao.ProductDao;
import com.bukuwarung.database.entity.InventoryHistoryEntity;
import com.bukuwarung.database.entity.InventoryOperationType;
import com.bukuwarung.database.entity.MeasurementEntity;
import com.bukuwarung.database.entity.ProductEntity;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;

import java.util.List;


public class InventoryRepository {

    private static InventoryRepository sInstance;
    private InventoryDao inventoryDao;
    private AppDatabase mDatabase;

    private InventoryRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        inventoryDao = this.mDatabase.inventoryDao();
    }

    public static InventoryRepository getInstance(Context context) {
        if (sInstance == null) {
            synchronized (InventoryRepository.class) {
                if (sInstance == null) {
                    sInstance = new InventoryRepository(context);
                }
            }
        }
        return sInstance;
    }

    @Transaction
    public MeasurementEntity addMeasurementUnit(String measurementName) {
        MeasurementEntity measurementEntityByName = inventoryDao.findMeasurementByName(measurementName);
        if (measurementEntityByName != null)
            return measurementEntityByName;
        MeasurementEntity newMeasurementUnit = new MeasurementEntity(Utility.uuid(), User.getBusinessId(), 0, measurementName);
        EntityHelper.fillEntityMetadata(newMeasurementUnit);
        inventoryDao.insert(newMeasurementUnit);
        return newMeasurementUnit;
    }

    public LiveData<List<InventoryHistoryEntity>> getInventoryHistory(String productId) {
        return inventoryDao.getInventoryHistory(productId);
    }

    public String getInventoryHistoryTransactionId(String inventoryHistoryId) {
        return inventoryDao.getInventoryHistoryTransactionId(inventoryHistoryId);
    }

    public List<MeasurementEntity> getMeasurementList() {
        return inventoryDao.getAllMeasurements();
    }
}
