package com.bukuwarung.database.repository;

import android.content.Context;

import androidx.lifecycle.LiveData;
import androidx.paging.DataSource;
import androidx.room.Transaction;

import com.bukuwarung.Application;
import com.bukuwarung.bulk.model.CashCategory;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.CashDao;
import com.bukuwarung.database.dao.TransactionDao;
import com.bukuwarung.database.dto.CashTransactionDto;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppCustomerCategorySyncManager;
import com.bukuwarung.datasync.AppExpenseTransSyncManager;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.List;

import javax.annotation.Nullable;


public class CashRepository {
    private static CashRepository sInstance;
    private CashDao cashDao;
    private TransactionDao transactionDao;
    private AppDatabase mDatabase;

    public CashRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        cashDao = this.mDatabase.cashDao();
        transactionDao = this.mDatabase.transactionDao();
    }

    public static CashRepository getInstance(Context context) {
        try {
            if(context == null) {
                FirebaseCrashlytics.getInstance().log("context is null: CashRepository");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (sInstance == null) {
                synchronized (CashRepository.class) {
                    if (sInstance == null) {
                        sInstance = new CashRepository(context);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }

    public int updateExistingCash(String userId, String deviceId, String cashId, String name, int delete, int type) {
        //(userId, deviceId, customerId, name,isDeleted.intValue(),type);
        long adjustedCurrentTime = AppIdGenerator.getCurrentTime();
        int res = this.cashDao.updateOne(cashId, name, delete, adjustedCurrentTime, adjustedCurrentTime, userId, deviceId, type);
        CashCategoryEntity cst = CashRepository.getInstance(Application.getAppContext()).getCashCategoryById(cashId);
        AppCustomerCategorySyncManager.getInstance().updateCashCategory(cst);
        return res;
    }

    public CashCategoryEntity getCustomerById(String cstId) {
        return cashDao.getCashCategoryById(cstId);
    }

    public CashCategoryEntity getCashCategoryByName(String name) {
        return cashDao.getCashCategoryByName(name);
    }

    public TransactionEntity getCustomerTransactionById(String customerTransactionId) {
        return transactionDao.getTransactionById(customerTransactionId);
    }

    public String saveCustomer(String businessId, String name, String address, String phone, String countryCode, Double balance, String reportId) {
        String uuid = Utility.uuid();
        CashCategoryEntity cashCategoryEntity = new CashCategoryEntity(businessId, uuid);
        cashCategoryEntity.name = name;
        cashCategoryEntity.balance = balance;
        cashCategoryEntity.deleted = Integer.valueOf(0);

        cashCategoryEntity.lastModifiedAt = Utility.getCurrentTime();
        EntityHelper.fillCashCategoryMetadata(cashCategoryEntity);
        this.cashDao.insert(cashCategoryEntity);
        return uuid;
    }


    public List<CashTransactionDto> getAllCashTransactionsWithDateRange(String businessId, String start, String end) {
        if (Utility.isBlank(start) || Utility.isBlank(end)) {
            return getAllCashTransactions(businessId);
        }
        return this.transactionDao.getCashTransactionsForViewWithRange(businessId, start, end);
    }

    public List<CashTransactionDto> getAllAutoRecordCashTransactionsWithDateRange(String businessId, String start, String end) {
        if (Utility.isBlank(start) || Utility.isBlank(end)) {
            return getAllAutoRecordTransactions(businessId);
        }
        return this.transactionDao.getAllAutoRecordTransactionsForViewWithRange(businessId, start, end,TransactionEntityType.BRICK_TRANSACTION);
    }

    public DataSource.Factory<Integer, CashTransactionDto> getAllCashTransactionsWithDateRangePaging(String businessId, String start, String end) {
        if (Utility.isBlank(start) || Utility.isBlank(end)) {
            return getAllCashTransactionsPaging(businessId);
        }
        return this.transactionDao.getCashTransactionsForViewWithRangePaging(businessId, start, end);
    }

    public int getCountCashTransaction(String businessId) {
        return this.transactionDao.countBusinessCashTransactions(businessId);
    }

    public int getCountAutoRecordTransactions(String businessId) {
        return this.transactionDao.countAutoRecordTransactions(businessId,TransactionEntityType.BRICK_TRANSACTION);
    }

    public int getCountCashCategoryTransaction(String businessId, String categoryId) {
        return this.transactionDao.countBusinessCashCategoryTransactions(businessId, categoryId);
    }

    public List<CashTransactionDto> getAllCashTransactions(String businessId) {
        return this.transactionDao.getCashTransactionsForView(businessId);
    }

    public List<CashTransactionDto> getAllAutoRecordTransactions(String businessId) {
        return this.transactionDao.getAllAutoRecordTransactionsForView(businessId,TransactionEntityType.BRICK_TRANSACTION);
    }

    public List<CashTransactionDto> getAutoRecordCreditTransactions(String businessId) {
        return this.transactionDao.getAutoRecordCreditTransactionsForView(businessId);
    }

    public DataSource.Factory<Integer, CashTransactionDto> getAllCashTransactionsPaging(String businessId) {
        return this.transactionDao.getCashTransactionsForViewPaging(businessId);
    }

    public List<CashTransactionEntity> getAllCategoryTransactions(String cashCategoryId, String businessId) {
        return this.transactionDao.getAllCategoryTransactions(cashCategoryId, businessId);
    }

    public List<CashTransactionEntity> getCategoryTransactionsByRange(String cashCategoryId, String businessId, String sdate, String edate) {
        return this.transactionDao.getCategoryTransactionsByRange(cashCategoryId, businessId,sdate,edate);
    }

    public String getCategoryIdByName(String bookId,String name) {
        return this.cashDao.getCategoryIdByName(bookId,name);
    }

    public int countAllCategoryTransactions(String cashCategoryId, String businessId) {
        return this.transactionDao.countAllCategoryTransactions(cashCategoryId, businessId);
    }

    public int countDistinctDateTransaction(String businessId) {
        return this.transactionDao.countDistinctDateTransaction(businessId);
    }

    public void deleteAllCategoryTransactions(String cashCategoryId, String businessId) {
        this.transactionDao.deleteAllCategoryTransactions(cashCategoryId, businessId);
    }

    public void changeCategoryNameToTransactions(String oldCashCategoryId, String businessId,
                                                 String newCashCategoryId, long updatedAt,
                                                 String updatedBy, String device) {
        this.transactionDao.changeCategoryNameToTransactions(oldCashCategoryId, businessId, newCashCategoryId, updatedAt, updatedBy, device);
    }

    public CashTransactionEntity getSingleRawCashTransaction(String cashTransactionId) {
        return transactionDao.getCashTransactionById(cashTransactionId);
    }

    public CashCategoryEntity getCashCategoryById(String categoryId) {
        return cashDao.getCashCategoryById(categoryId);
    }




    public LiveData<CashTransactionEntity> getObservableCashTransactionById(String cashTransactionId) {
        return transactionDao.getObservableCashTransactionById(cashTransactionId);
    }

    /**
     * same as above method, but with fewer params
     * */
    public String saveNewCashTransaction(String categoryId, double amount, double buyingPrice, String date, String notes, String customerTransactionId, CustomerEntity customerEntity, int status, String attachment, String paymentMethod) {
        CashTransactionEntity newCashTransactionEntity = insertOnlyNewCashTransaction(User.getUserId(), User.getDeviceId(), User.getBusinessId(), categoryId, amount, date, notes, 0, buyingPrice, customerTransactionId, status, customerEntity.customerId, customerEntity.name, customerEntity.phone, 0.0, attachment, paymentMethod);
        refreshCashBalance(categoryId);
        AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(newCashTransactionEntity);
        return newCashTransactionEntity.cashTransactionId;
    }

    public String saveNewCashTransactionForPos(String categoryId, double amount, double customAmount,double buyingPrice, String date, String notes, String customerTransactionId, CustomerEntity customerEntity, int status, String paymentMethod) {
        CashTransactionEntity newCashTransactionEntity = insertOnlyNewCashTransaction(User.getUserId(), User.getDeviceId(), User.getBusinessId(), categoryId, amount, date, notes, 0, buyingPrice, customerTransactionId, status, customerEntity.customerId, customerEntity.name, customerEntity.phone, customAmount, null,paymentMethod);
        refreshCashBalance(categoryId);
        AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(newCashTransactionEntity);
        return newCashTransactionEntity.cashTransactionId;
    }

    public CashTransactionEntity insertOnlyNewCashTransaction(String userId, String deviceId, String businessId, String categoryId, double amount, String transactionDate, String notes, int i, double buyingPrice, @Nullable String customerTransactionId, int status, String customerId, String name, String mobileNumber, @Nullable double customAmount, String attachment, String paymentMethod) {
        CashTransactionEntity transactionEntity = new CashTransactionEntity(businessId, categoryId, AppIdGenerator.resourceUUID());
        transactionEntity.amount = amount;
        transactionEntity.buyingPrice = buyingPrice;
        transactionEntity.date = transactionDate;
        transactionEntity.description = notes;
        transactionEntity.deleted = Integer.valueOf(0);
        long adjustedCurrentTime = AppIdGenerator.getCurrentTime();
        transactionEntity.createdAt = Long.valueOf(adjustedCurrentTime);
        transactionEntity.createdByUser = userId;
        transactionEntity.createdByDevice = deviceId;
        transactionEntity.updatedAt = Long.valueOf(adjustedCurrentTime);
        transactionEntity.updatedByUser = userId;
        transactionEntity.updatedByDevice = deviceId;
        transactionEntity.serverSeq = Long.valueOf(0);
        transactionEntity.dirty = Integer.valueOf(1);
        transactionEntity.attachmentsUploadPending = Integer.valueOf(0);
        transactionEntity.status = status;
        transactionEntity.customerId = customerId;
        transactionEntity.customerTransactionId = customerTransactionId;
        transactionEntity.customerName = name;
        transactionEntity.customerPhoneNumber = mobileNumber;
        transactionEntity.customAmount = customAmount;
        transactionEntity.attachments = attachment;
        transactionEntity.paymentMethod = paymentMethod;
        if ( customAmount > 0 || categoryId.contains("pos::")) {
            transactionEntity.transactionType = TransactionEntityType.POS_TRANSACTION;
        }

        // inserting cash transaction into SQLite
        try {
            Long res = this.transactionDao.insertCashTransaction(transactionEntity);
            // inserting cash transaction into SQLite success
            FirebaseCrashlytics.getInstance().log("[SQLite][CashTransactionEntity]Success.ID:" + transactionEntity.cashTransactionId);
        } catch (Exception ex) {
            // inserting cash transaction into SQLite fails
            FirebaseCrashlytics.getInstance().log("[SQLite][CashTransactionEntity]Failed.ID:" + transactionEntity.cashTransactionId + ",Trace:" + ex.toString());
        }

        return transactionEntity;
    }

    @Transaction
    public void insertCashTransactionWithRefreshedBalance(CashTransactionEntity cashTransactionEntity) {
        this.transactionDao.insertCashTransaction(cashTransactionEntity);
        this.cashDao.refreshCashBalance(cashTransactionEntity.cashCategoryId);
    }

    @Transaction
    public void insertCashTransactionWithRefreshedBalance(List<CashTransactionEntity> cashTransactionEntity) {
        this.transactionDao.insertCashTransaction(cashTransactionEntity);
        for (CashTransactionEntity item : cashTransactionEntity) {
            this.cashDao.refreshCashBalance(item.cashCategoryId);
        }
    }

    public void insertCashTransaction(CashTransactionEntity cashTransactionEntity) {
        this.transactionDao.insertCashTransaction(cashTransactionEntity);
    }

    public int refreshCashBalance(String categoryId) {
        CashCategoryEntity cashCategoryEntity = getCashCategoryById(categoryId);
        int res = this.cashDao.enableAndRefreshBalance(categoryId, cashCategoryEntity.type);
        return res;
    }

    public int updateCategoryBalanceWithSync(String categoryId) {
        CashCategoryEntity cashCategoryEntity = getCashCategoryById(categoryId);
        int res = this.cashDao.enableAndRefreshBalance(categoryId, cashCategoryEntity.type);
        if (cashCategoryEntity != null) {
            AppCustomerCategorySyncManager.getInstance().updateCashCategory(cashCategoryEntity);
        }
        return res;
    }

    public String saveNewCashCategory(String userId, String deviceId, String businessId, String categoryId, String name, Double amount, int type,int frequency) {

        CashCategoryEntity cashCategoryEntity = new CashCategoryEntity(businessId, categoryId);
        cashCategoryEntity.name = name;
        cashCategoryEntity.balance = amount;
        cashCategoryEntity.deleted = Integer.valueOf(0);
        long adjustedCurrentTime = AppIdGenerator.getCurrentTime();
        cashCategoryEntity.createdAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.createdByUser = userId;
        cashCategoryEntity.createdByDevice = deviceId;
        cashCategoryEntity.type = type;
        cashCategoryEntity.updatedAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.updatedByUser = userId;
        cashCategoryEntity.updatedByDevice = deviceId;
        cashCategoryEntity.serverSeq = Long.valueOf(0);
        cashCategoryEntity.lastModifiedAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.dirty = Integer.valueOf(1);
        cashCategoryEntity.frequency = frequency;

        //inserting cash category to SQLite
        try {
//            AppCustomerCategorySyncManager.getInstance().updateCashCategory(cashCategoryEntity);
            Long insertOne = this.cashDao.insert(cashCategoryEntity);
            // inserting cash category to SQLite success
            FirebaseCrashlytics.getInstance().log("[SQLite][CashCategoryEntity]Success.ID:" + cashCategoryEntity.cashCategoryId);
        } catch (Exception ex) {
            // inserting cash category to SQLite fails
            FirebaseCrashlytics.getInstance().log("[SQLITE][CashCategoryEntity]Failed.ID:" + cashCategoryEntity.cashCategoryId + ",Trace:" + ex.getLocalizedMessage());
        }

        return categoryId;
    }

    public String updateCashCategoryEntity(String userId, String deviceId, String businessId, String categoryId, String name, Double amount, int type,int frequency) {

        CashCategoryEntity cashCategoryEntity = new CashCategoryEntity(businessId, categoryId);
        cashCategoryEntity.name = name;
        cashCategoryEntity.balance = amount;
        cashCategoryEntity.deleted = Integer.valueOf(0);
        long adjustedCurrentTime = AppIdGenerator.getCurrentTime();
        cashCategoryEntity.createdAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.createdByUser = userId;
        cashCategoryEntity.createdByDevice = deviceId;
        cashCategoryEntity.type = type;
        cashCategoryEntity.updatedAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.updatedByUser = userId;
        cashCategoryEntity.updatedByDevice = deviceId;
        cashCategoryEntity.serverSeq = Long.valueOf(0);
        cashCategoryEntity.lastModifiedAt = Long.valueOf(adjustedCurrentTime);
        cashCategoryEntity.dirty = Integer.valueOf(1);
        cashCategoryEntity.frequency = frequency;

        //inserting cash category to SQLite
        try {
//            AppCustomerCategorySyncManager.getInstance().updateCashCategory(cashCategoryEntity);
            this.cashDao.insertCashCategoryEntity(cashCategoryEntity);
            // inserting cash category to SQLite success
            FirebaseCrashlytics.getInstance().log("[SQLite][CashCategoryEntity]Success.ID:" + cashCategoryEntity.cashCategoryId);
        } catch (Exception ex) {
            // inserting cash category to SQLite fails
            FirebaseCrashlytics.getInstance().log("[SQLITE][CashCategoryEntity]Failed.ID:" + cashCategoryEntity.cashCategoryId + ",Trace:" + ex.getLocalizedMessage());
        }

        return categoryId;
    }

    /** will update all properties of CashTransaction*/
    public int updateCashTransaction(String cashTransactionId, String categoryId, double amount, String transactionDate, String desc, int deleted, double buyingPrice, @Nullable String customerTransactionId, int status, CustomerEntity customerEntity, String attachment) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        int result = transactionDao.updateCashTransactionWithCategory(cashTransactionId, amount, transactionDate, desc, Integer.valueOf(deleted), updatedAt, User.getUserId(), User.getDeviceId(), buyingPrice, customerTransactionId, status, customerEntity.customerId, customerEntity.name, customerEntity.phone, categoryId, attachment);
        this.refreshCashBalance(categoryId);
        cashDao.setLastModifiedAt(Long.valueOf(updatedAt), cashTransactionId);
        CashTransactionEntity transactionEntity = CashRepository.getInstance(Application.getAppContext()).getSingleRawCashTransaction(cashTransactionId);
        AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(transactionEntity);
        return result;
    }

    public void settleCashTransactionByCustomerTransactionId(String customerTransactionId, int status) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        CashTransactionEntity cashTransactionEntity = transactionDao.getCashTransactionByCusutomerTransactionId(customerTransactionId);
        if (cashTransactionEntity != null) { // cstTrx is connected to cashTrx
            transactionDao.updateCashTransactionStatusByCustomerTransactionId(customerTransactionId, status, updatedAt);
            cashTransactionEntity.status = status;
            cashTransactionEntity.updatedAt = updatedAt;
            AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(cashTransactionEntity);
        }
    }

    public int updateExistingCashTransaction(String updatedBy, String device, String cashTransactionId, String categoryId, double amount, String transactionDate, String desc, int deleted) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        int result = 0;
        if (SessionManager.getInstance().isGuestUser() && deleted == AppConst.HARD_DELETE) {
            //when category update is done record needs to be deleted completly for guest users.
            // if new txn record is created for updated category and old is marked as deleted it will be counted in guest user txn count and user won't be able to create 10 records.
            //record count for guest user takes deleted txn into calculation
            result = transactionDao.hardDeleteCashTransaction(cashTransactionId);
            this.refreshCashBalance(categoryId);
            //old logic of using cashTxnId was incorrect, category is getting updated so we need to use category id
            cashDao.setLastModifiedAt(Long.valueOf(updatedAt), categoryId);
        } else {
            result = transactionDao.updateCashTransaction(cashTransactionId, amount, transactionDate, desc, Integer.valueOf(deleted), updatedAt, updatedBy, device);
            this.refreshCashBalance(categoryId);
            //old logic of using cashTxnId was incorrect, category is getting updated so we need to use category id
            cashDao.setLastModifiedAt(Long.valueOf(updatedAt), categoryId);
            CashTransactionEntity transactionEntity = CashRepository.getInstance(Application.getAppContext()).getSingleRawCashTransaction(cashTransactionId);
            AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(transactionEntity);
        }
        return result;
    }

    @Transaction
    public int updateExistingCashTransactions(String updatedBy, String device, int deleted, List<String> transactionIds, String categoryId,String currentCategory) {
        long updatedAt = AppIdGenerator.getCurrentTime();
        int result = 0;
        if (SessionManager.getInstance().isGuestUser() && deleted == AppConst.HARD_DELETE) {
            //when category update is done record needs to be deleted completly for guest users.
            // if new txn record is created for updated category and old is marked as deleted it will be counted in guest user txn count and user won't be able to create 10 records.
            //record count for guest user takes deleted txn into calculation
//            result = transactionDao.hardDeleteCashTransaction(cashTransactionId);
//            this.refreshCashBalance(categoryId);
            //old logic of using cashTxnId was incorrect, category is getting updated so we need to use category id
//            cashDao.setLastModifiedAt(Long.valueOf(updatedAt), categoryId);
        } else {

            result = transactionDao.updateCashTransactions(deleted,updatedAt, updatedBy, device, transactionIds,categoryId);
            this.refreshCashBalance(categoryId);

            CashCategoryEntity cashCatEntity = cashDao.getCashCategoryByName(currentCategory);
            this.refreshCashBalance(cashCatEntity.cashCategoryId);
            cashDao.setLastModifiedAt(Long.valueOf(updatedAt), cashCatEntity.cashCategoryId);


            //old logic of using cashTxnId was incorrect, category is getting updated so we need to use category id
            cashDao.setLastModifiedAt(Long.valueOf(updatedAt), categoryId);
            transactionIds.stream().forEach(transactionId -> {
                CashTransactionEntity transactionEntity = CashRepository.getInstance(Application.getAppContext()).getSingleRawCashTransaction(transactionId);
                AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(transactionEntity);
            });
        }
        return result;
    }

    public List<CashCategoryEntity> getUniqueCategoryEntries(int categoryType) {
        return this.cashDao.getAllUnique(User.getBusinessId(), categoryType);
    }


    public List<CashCategoryEntity> getCategoryByFrequency(int categoryType) {
        return this.cashDao.getCategoryByFrequency(User.getBusinessId(), categoryType);
    }

    public LiveData<CashCategoryEntity> getObservableCategoryById(String categoryId) {
        return cashDao.getObservableCashCategoryById(categoryId);
    }

    public void insertCashCategorySync(CashCategoryEntity cashCategoryEntity) {
        this.cashDao.insertCashCategoryEntity(cashCategoryEntity);
    }

    public void insertCashCategoriesSync(List<CashCategoryEntity> cashCategoryEntities) {
        this.cashDao.insertCashCategoryEntities(cashCategoryEntities);
    }

    public void retryCashCategorySync(CashCategoryEntity cashCategoryEntity) {
        this.cashDao.retryCashCategorySync(cashCategoryEntity);
    }

    public void deleteExistingCash(CashCategoryEntity cashCategoryEntity, boolean deleteValues, String categoryId) {
        this.cashDao.deleteById(cashCategoryEntity.cashCategoryId);
        this.transactionDao.deleteByCashId(cashCategoryEntity.cashCategoryId);

    }

    public List<CashCategoryEntity> getAllCategoryList(String bookId) {
        return this.cashDao.getAllCategoryList(bookId);
    }

    public List<CashTransactionEntity> getAllCashTransactionsSync(String bookId) {
        return this.transactionDao.getAllCashTransactionByBookId(bookId);
    }

    @Transaction
    public void mergeGuestRecords() {
        this.transactionDao.mergeGuestCashTxnData(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
        this.transactionDao.mergeGuestCustomerTxnData(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
        this.cashDao.mergeGuestCashCategoryData(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
    }

    public CashTransactionEntity getCategoryByOrderId(String orderId) {
        return transactionDao.getCashTransactionByOrderId(orderId);
    }

    public double getCashInByDateRange(String businessId,String sdate,String edate) {
        return this.cashDao.getSummaryTotalCashInDateRange(businessId,sdate,edate);
    }

    //utang amount out
    public double getCashOutByDateRange(String businessId,String sdate,String edate) {
        return this.cashDao.getSummaryTotalCashOutDateRange(businessId,sdate,edate);
    }

}
