package com.bukuwarung.database.repository;

import android.content.Context;
import android.util.Log;

import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.activities.geolocation.data.model.Address;
import com.bukuwarung.activities.profile.summary.SummaryViewModel;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.FirestoreConst;
import com.bukuwarung.database.AppDatabase;
import com.bukuwarung.database.dao.BusinessDao;
import com.bukuwarung.database.dao.CommonDao;
import com.bukuwarung.database.dao.CustomerDao;
import com.bukuwarung.database.entity.AppConfig;
import com.bukuwarung.database.entity.AppNotification;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.UserProfileEntity;
import com.bukuwarung.database.helper.EntityHelper;
import com.bukuwarung.datasync.AppBookSyncManager;
import com.bukuwarung.datasync.migration.SyncTableEnum;
import com.bukuwarung.dialogs.businessselector.BusinessType;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.firebase.firestore.CollectionReference;
import com.google.firebase.firestore.DocumentChange;
import com.google.firebase.firestore.DocumentReference;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.FirebaseFirestoreException;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.Transaction;
import androidx.sqlite.db.SimpleSQLiteQuery;


public class BusinessRepository {
    public static AppConfig appConfig;
    private static BusinessRepository sInstance;
    private static List<AppNotification> notifications;
    private BusinessDao businessDao;
    private CustomerDao customerDao;
    private CommonDao commonDao;
    private AppDatabase mDatabase;
    private FirebaseFirestore mFirestore;

    public BusinessRepository(Context context) {
        this.mDatabase = AppDatabase.getDatabase(context);
        businessDao = this.mDatabase.businessDao();
        customerDao = this.mDatabase.customerDao();
        commonDao = this.mDatabase.commonDao();
        this.mFirestore = FirebaseFirestore.getInstance();
    }

    public static BusinessRepository getInstance(Context context) {
        try {
            if(context == null) {
                FirebaseCrashlytics.getInstance().log("context is null: BusinessRepository");
            }
        } catch (Exception e) {
            Log.e("BusinessRepository", "Exception", e);
        }
        try {
            if (sInstance == null) {
                synchronized (BusinessRepository.class) {
                    if (sInstance == null) {
                        sInstance = new BusinessRepository(context);
                        sInstance.notifications = new ArrayList<>();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sInstance;
    }

    public int businessCount(String ownerId) {
        return this.businessDao.businessCount(ownerId);
    }

    public String getUserName(String userId) {
        List<String> repNameList = this.businessDao.getRepresentativeList(userId);
        for (String repName : repNameList) {
            if (!Utility.isBlank(repName)) {
                return repName;
            }
        }
        return null;
    }

    public BookEntity createDefaultBook() {
        return createBusiness(
                User.getUserId(),
                User.getDeviceId(),
                "BukuWarung",
                AppConst.DEFAULT_BUSINESS_NAME,
                -1,
                ""
        );
    }

    public BookEntity createBusiness(String userId, String device, String ownerNm, String businessNm, int businessType, String businessTypeName) {
        BookEntity bookEntity = new BookEntity(Utility.uuid(), userId, ownerNm, businessNm, businessType);
        bookEntity.bookTypeName = businessTypeName;
        if(Utility.isBlank(businessTypeName) && bookEntity.bookType!=-1){
            bookEntity.bookTypeName = "Lainnya";
            bookEntity.bookType = 36;
        }

        bookEntity.enableSmsAlerts = Integer.valueOf(1);
        bookEntity.businessImageUploadPending = Integer.valueOf(0);
        bookEntity.isGuest = SessionManager.getInstance().isGuestUser() ? 1 : 0;
        EntityHelper.fillBusinessMetadata(bookEntity);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
        this.businessDao.insert(bookEntity);
        return bookEntity;
    }

    public BookEntity createBusinessWithDetails(BookEntity bookEntity) {
        EntityHelper.fillBusinessMetadata(bookEntity);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
        this.businessDao.insert(bookEntity);
        return bookEntity;
    }

    public int updateDeleteFlag(String userId, String deviceId, String businessId, Integer delFlg) {
        int res = this.businessDao.updateDeleteFlag(userId, deviceId, businessId, delFlg, Utility.getCurrentTime());
        BookEntity bookEntity = this.businessDao.getBusinessSync(businessId);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
        return res;
    }

    public LiveData<List<BookEntity>> getBusinessList(String userId) {
        return this.businessDao.getBusinessList(userId);
    }

    public List<BookEntity> getBusinessListRaw(String userId) {
        return this.businessDao.getBusinessListRaw(userId);
    }

    public LiveData<BookEntity> getBusinessById(String businessId) {
        return this.businessDao.getBusinessById(businessId);
    }

    public BookEntity getBusinessByIdSync(String businessId) {
        if (Utility.isBlank(businessId)) return null;
        return this.businessDao.getBusinessSync(businessId);
    }

    public SummaryViewModel getCreditSummary(String businessId) {
        SummaryViewModel summary = new SummaryViewModel(this.customerDao.countCredit(User.getBusinessId()), this.customerDao.countDebit(User.getBusinessId()),
                this.customerDao.getSummaryTotalCredit(User.getBusinessId()), this.customerDao.getSummaryTotalDebit(User.getBusinessId()));
        return summary;
    }

    //utang amount in
    public double getCreditByDate(String businessId,String date) {
        return this.customerDao.getSummaryTotalCreditByDate(businessId,date);
    }

    public void updateDirtyFlag(String tableName,String idColumn, String idValue) {
        SimpleSQLiteQuery query = new SimpleSQLiteQuery("update "+tableName+" set dirty=0 where "+idColumn+" = \""+idValue+"\"");
        this.commonDao.updateDirtyFlag(query);
    }

    public void updateAllDirtyFlag() {
        for(SyncTableEnum syncTable:SyncTableEnum.values()){
            SimpleSQLiteQuery query = new SimpleSQLiteQuery("update "+syncTable.getTableName()+" set dirty=0");
            this.commonDao.updateDirtyFlag(query);
        }

    }

    public void updateDirtyFlagForCompositePK(String tableName,String condition) {
        SimpleSQLiteQuery query = new SimpleSQLiteQuery("update "+tableName+" set dirty=0 where "+condition);
        this.commonDao.updateDirtyFlag(query);
    }

    public double getCreditByDateRange(String businessId,String sdate,String edate) {
        return this.customerDao.getSummaryTotalCreditByDateRange(businessId,sdate,edate);
    }

    //utang amount out
    public double getDebitByDateRange(String businessId,String sdate,String edate) {
        return this.customerDao.getSummaryTotalDebitByDateRange(businessId,sdate,edate);
    }

    public double getDebitByDate(String businessId,String date) {
        return this.customerDao.getSummaryTotalDebitByDate(businessId,date);
    }

    //utang amount in
    public double getCashInByDate(String businessId,String date) {
        return this.customerDao.getSummaryTotalCashInByDate(businessId,date);
    }

    //utang amount out
    public double getCashOutByDate(String businessId,String date) {
        return this.customerDao.getSummaryTotalCashOutByDate(businessId,date);
    }

    public double getBuyingPriceCashOutByDate(String businessId,String date) {
        Double buyingPriceByDate =  this.customerDao.getSummaryTotalBuyingPriceByDate(businessId,date);
        if(buyingPriceByDate == null)
            return 0d;
        else
            return buyingPriceByDate;
    }

    public SummaryViewModel getCashSummary(String businessId) {
        SummaryViewModel summary = new SummaryViewModel(this.customerDao.countCredit(User.getBusinessId()), this.customerDao.countDebit(User.getBusinessId()),
                this.customerDao.getSummaryTotalCredit(User.getBusinessId()), this.customerDao.getSummaryTotalDebit(User.getBusinessId()));
        return summary;
    }

    public void updateBusinessProfile(String userId, String deviceId, String businessId, String businessName, Integer businessType, String businessTypeName, String businessOwnerName) {
        this.businessDao.updateBusinessProfile(userId, deviceId, businessId, businessName, businessType, businessTypeName, businessOwnerName, Utility.getCurrentTime());
        AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_NAME, businessName);
        AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE_NAME, businessTypeName);
        AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE, businessType+"");
        BookEntity bookEntity = this.businessDao.getBusinessSync(businessId);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
    }

    public void updateBusinessProfileImage(String id, String imageUri, int isPending) {
        this.businessDao.updateBusinessProfileImage(id, imageUri, isPending);
        BookEntity bookEntity = this.businessDao.getBusinessSync(id);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
    }

    public void updateBusinessProfileImageInDao(String id, String imageUri, int isPending) {
        this.businessDao.updateBusinessProfileImage(id, imageUri, isPending);
    }

    public void updateUserProfileImageInDao(String id, String imageUri) {
        this.businessDao.updateUserProfileImage(id, imageUri);
    }

    public void updateInvoicePref(String bookId, String imageUrl, String newPhone, String newAddress, int isPending) {
        if (imageUrl == null) {
            this.businessDao.updateInvoicePrefWithoutLogo(bookId, newPhone, newAddress, isPending);
        } else {
            this.businessDao.updateInvoicePref(bookId, imageUrl, newPhone, newAddress, isPending);
        }
    }

    public void updateBusinessProfileName(String id, String businessName) {
        this.businessDao.updateBusinessProfileName(id, businessName);
        BookEntity bookEntity = this.businessDao.getBusinessSync(id);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
    }

    public void enablePayments(String bookId) {
        this.businessDao.enablePayments(bookId);
        BookEntity bookEntity = this.businessDao.getBusinessSync(bookId);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
    }

    public void enablePaymentsInBusinessDao(String bookId) {
        this.businessDao.enablePayments(bookId);
    }

    public void updateBusinessProfileComplete(String userId, String deviceId, String businessId, String businessName, String businessOwnerName, String businessTagLine, String businessPhone,
                                              String businessAddress, String businessEmail) {
        this.businessDao.updateBusinessProfileComplete(userId, deviceId, businessId, businessName, businessOwnerName, businessTagLine, businessPhone, businessAddress, businessEmail,
                Utility.getCurrentTime());
        BookEntity bookEntity = this.businessDao.getBusinessSync(businessId);
        AppBookSyncManager.getInstance().updateBook(bookEntity);
    }

    public void getNotifications() {
        sInstance.notifications = new ArrayList<>();
        mFirestore.collection(FirestoreConst.COLLECTION_APP_INBOX)
                .addSnapshotListener(this::notificationsEventListener);
    }

    public void getUserNotifications() {
        try {
            mFirestore.collection(FirestoreConst.COLLECTION_APP_USER_INBOX)
                    .whereEqualTo("userId", User.getUserId())
                    .addSnapshotListener(this::notificationsEventListener);
        }catch (Exception e){
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    private void notificationsEventListener(@Nullable QuerySnapshot snapshots,
                                            @Nullable FirebaseFirestoreException e) {
        if (e != null) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return;
        }
        try {
            if (snapshots != null) {
                for (DocumentChange dc : snapshots.getDocumentChanges()) {
                    DocumentSnapshot doc = dc.getDocument();
                    AppNotification notification = doc.toObject(AppNotification.class);
                    if (notification.active) {
                        sInstance.notifications.add(notification);
                    }
                }
            }

            if (FeaturePrefManager.getInstance().getNotificationCount() < notifications.size()) {
                FeaturePrefManager.getInstance().setUnseenNotification(true);
            }
            FeaturePrefManager.getInstance().setNotificationCount(notifications.size());
            Collections.sort(notifications, new Comparator<AppNotification>() {
                @Override
                public int compare(AppNotification notification, AppNotification newNotification) {
                    return notification.startAt.compareTo(newNotification.startAt);
                }
            });

        } catch (Exception se) {
            se.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public List<AppNotification> getNotificationsList() {
        return notifications;
    }

    public AppConfig getConfig() {
        return appConfig;
    }

    public void insertBookSync(BookEntity bookEntity) {
        this.businessDao.insert(bookEntity);
    }

    public void insertBookSync(List<BookEntity> bookEntities) {
        this.businessDao.insert(bookEntities);
    }

    public void loadAppConfig(Application application) {
        DocumentReference docRef = mFirestore.collection("app_config").document(BuildConfig.APP_CONFIG_VERSION);
        docRef.get().addOnSuccessListener(new OnSuccessListener<DocumentSnapshot>() {
            @Override
            public void onSuccess(DocumentSnapshot documentSnapshot) {
                appConfig = documentSnapshot.toObject(AppConfig.class);
                AppConfigManager.getInstance().setNotificationIcon(appConfig.notificationIcon);
                AppConfigManager.getInstance().isBureauEnabled(appConfig.enableBureau);
                AppConfigManager.getInstance().setinformasiOptionalCount(appConfig.informasiOptionalCount);
                AppConfigManager.getInstance().setManualReminderPaymentLinkCount(appConfig.manualReminderPaymentLinkCount);
                AppConfigManager.getInstance().setReportApi(appConfig.reportApi);
                AppConfigManager.getInstance().setExcelReportApi(appConfig.excelReportApi);
                AppConfigManager.getInstance().setEnableNewAmplitudeProject(appConfig.enableNewAmplitudeProject);
                AppConfigManager.getInstance().setSmsApi(appConfig.smsApi);
                AppConfigManager.getInstance().setTxnApi(appConfig.txnApi);
                AppConfigManager.getInstance().setReferralLeaderboardUrl(appConfig.leaderboardWebUrl);
                AppConfigManager.getInstance().enableTokokoDownload(appConfig.enableTokokoDownload);
                AppConfigManager.getInstance().setLunasApi(appConfig.lunaskanApi);
                AppConfigManager.getInstance().setWhatsappAuth(appConfig.whatsappAuth);
                AppConfigManager.getInstance().setOTPApi(appConfig.otpApi);
                AppConfigManager.getInstance().setStreaksApi(appConfig.streaksApi);
                AppConfigManager.getInstance().setStreakDuration(appConfig.streakDuration);
                AppConfigManager.getInstance().setLatestVersion(appConfig.latestVersion);
                AppConfigManager.getInstance().setEnableFlexibleUpdate(appConfig.useFlexibleUpdate);
                AppConfigManager.getInstance().setEnableGuestFeature(appConfig.enableGuestFeature);
                AppConfigManager.getInstance().setGuestTxnLimit(appConfig.guestTxnLimit);
                AppConfigManager.getInstance().setMoApi(appConfig.moapi);
                AppConfigManager.getInstance().setAfApi(appConfig.afapi);
                AppConfigManager.getInstance().setAmpApi(appConfig.ampapi);
                AppConfigManager.getInstance().allowContactUpload(appConfig.uploadContact);
                AppConfigManager.getInstance().useWebView(appConfig.useWebView);
                AppConfigManager.getInstance().setEnableLiveSync(appConfig.enableLiveSync);
                AppConfigManager.getInstance().setPullRefresh(appConfig.pullRefresh);
                AppConfigManager.getInstance().setFileUploadApi(appConfig.fileUploadApi);
                AppConfigManager.getInstance().setFileBucket(appConfig.fileBucket);
                AppConfigManager.getInstance().setReferralTitle(appConfig.referralTitle);
                AppConfigManager.getInstance().setReferralBanner(appConfig.referralBanner);
                AppConfigManager.getInstance().setReferralPrizeBanner(appConfig.referralPrizeBanner);
                ReferralPrefManager.getInstance().setBaseInviteUrl(appConfig.referralBaseUrl);
                if (!Utility.isBlank(appConfig.rewardBaseUrl)) {
                    ReferralPrefManager.getInstance().setBaseRewardUrl(appConfig.rewardBaseUrl);
                    ReferralPrefManager.getInstance().enablePaymentReferral(appConfig.enablePaymentReferral);
                    ReferralPrefManager.getInstance().enableReferralReward(appConfig.enableReferralReward);
                }
                ReferralPrefManager.getInstance().setBaseApiUrl(appConfig.referralApiUrl);
                ReferralPrefManager.getInstance().setLeaderboardServerIP(appConfig.leaderboardApiUrl);
                AppConfigManager.getInstance().setReferralSteps(appConfig.referralSteps);
                AppConfigManager.getInstance().setReferralPrizes(appConfig.referralPrizes);
                AppConfigManager.getInstance().setReferralTncs(appConfig.referralTncs);
                AppConfigManager.getInstance().setReferralTxnVal(appConfig.referralTransactionVal);
                AppConfigManager.getInstance().setLeaderBoardSize(appConfig.leaderBoardSize);
//                AppConfigManager.getInstance().setEnableGamifyDialog(appConfig.enableGamifyDialog);
                AppConfigManager.getInstance().setUseReferral(appConfig.referralFeatureActive);
                AppConfigManager.getInstance().setUseReferralSharing(appConfig.referralSharingActive);
                AppConfigManager.getInstance().setUseCollectionCalendar(appConfig.collectionCalendarFeatureActive);
                AppConfigManager.getInstance().setReferralMessage(appConfig.referralMessage);
                AppConfigManager.getInstance().setReferralLeaderboardMessage(appConfig.referralLeaderboardMessage);
                AppConfigManager.getInstance().isSyncEnabled(appConfig.syncEnabled);
                AppConfigManager.getInstance().setUseServerCategory(appConfig.useServerCategory);
                AppConfigManager.getInstance().setWaBotMsg(appConfig.defaultWABotMessage);
                FeaturePrefManager.getInstance().setWhatsappId(appConfig.whatsappId);
                AppConfigManager.getInstance().setWhatsappId(appConfig.whatsappId);
                AppConfigManager.getInstance().setSmsTemplateCredit(appConfig.smsTemplateCredit);
                AppConfigManager.getInstance().setSmsTemplateDebit(appConfig.smsTemplateDebit);
                AppConfigManager.getInstance().setAppConfigDefaultCash(appConfig.defaultCashOption);
                AppConfigManager.getInstance().setDefaultTabLogin(appConfig.defaultTabLogin);
                AppConfigManager.getInstance().setDefaultTabName(appConfig.defaultTabName);
                AppConfigManager.getInstance().setBukuPayLogoActive(appConfig.bukuPayLogoActive);
                AppConfigManager.getInstance().setWelcomeText(appConfig.welcomeText);
                AppConfigManager.getInstance().setPaymentIntervalInMin(appConfig.paymentIntervalInMin);
                AppConfigManager.getInstance().setPaymentInMsg(appConfig.paymentInMsg);
                AppConfigManager.getInstance().setPaymentOutMsg(appConfig.paymentOutMsg);

                AppConfigManager.getInstance().setUtangReviewDate(appConfig.utangReviewDate);
                AppConfigManager.getInstance().setUtangUserImage(appConfig.utangUserImage);
                AppConfigManager.getInstance().setUtangUserMsg(appConfig.utangMessage);
                AppConfigManager.getInstance().setUtangUserName(appConfig.utangUserName);

                AppConfigManager.getInstance().setKycCriteriaAmount(appConfig.kycCriteriaAmount);
                AppConfigManager.getInstance().setKycCriteriaTrxCount(appConfig.kycCriteriaTrxCount);
                AppConfigManager.getInstance().setKycCriteriaType(appConfig.kycCriteriaType);

                AppConfigManager.getInstance().setTransaksiReviewDate(appConfig.transaksiReviewDate);
                AppConfigManager.getInstance().setTransaksiUserImage(appConfig.transaksiUserImage);
                AppConfigManager.getInstance().setTransaksiUserMsg(appConfig.transaksiMessage);
                AppConfigManager.getInstance().setTransaksiUserName(appConfig.transaksiUserName);
                AppConfigManager.getInstance().setPaymentFreeChargeStatus(appConfig.paymentFreeChargeStatus);
                AppConfigManager.getInstance().showCustomPermissionDialog(appConfig.showCustomPermissionDialog);
                AppConfigManager.getInstance().setProfileSetupTarget(appConfig.profileSetupTarget);
                AppConfigManager.getInstance().usePayloadFromApp(appConfig.usePayloadFromApp);
                AppConfigManager.getInstance().setTokokoDownloadLink(appConfig.tokokoDownloadLink);
                AppConfigManager.getInstance().setStockTransactionTarget(appConfig.stockTransactionTarget);
            }
        });

        if (!AppConfigManager.getInstance().hasBusinessTypes() || AppConfigManager.getInstance().useServerCategory())
            loadBusinessType(mFirestore);

        loadCategory(mFirestore);
    }

    private void loadBusinessType(FirebaseFirestore firestore) {
        CollectionReference collectionReference = firestore.collection("app_business_category");
        collectionReference.get().addOnSuccessListener(queryDocumentSnapshots -> {
            List<BusinessType> businessTypes = queryDocumentSnapshots.toObjects(BusinessType.class);
            AppConfigManager.getInstance().setBusinessTypes(businessTypes);
        });
    }

    private void loadCategory(FirebaseFirestore firestore) {
        // TODO: LOAD CATEGORY
//        if (AppConfigManager.getInstance().useServerCategory())
//            loadCashInCategory(firestore);
//
//        if (AppConfigManager.getInstance().useServerCategory())
//            loadCashOutCategory(firestore);
    }

    @Transaction
    public void deleteGuestBook() {
        this.customerDao.deleteCustomerGuestData(User.getBusinessId());
        this.customerDao.deleteCashCategoryGuestData(User.getBusinessId());
        this.businessDao.deleteGuestBook();
    }

    @Transaction
    public void mergeGuestRecords() {
        this.customerDao.mergeGuestCustomerData(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
        this.businessDao.mergeGuestBook(User.getUserId(), SessionManager.getInstance().getGuestBusinessId());
    }

    public int getOldBusinessCategoryCount(String[] oldBusinessCategory) {
        return this.businessDao.getOldBusinessCategoryCount(oldBusinessCategory,"Lainnya");
    }

    public long getStartDate(String bookId) {
        return this.businessDao.getJoiningDate(bookId);
    }


    public LiveData<UserProfileEntity> getUserProfile(String userId) {
        return this.businessDao.getUserProfile(userId);
    }

    public UserProfileEntity getUserProfileEntity(String userId) {
        return this.businessDao.getUserProfileEntity(userId);
    }


    @Transaction
    public Long setUserProfile(UserProfileEntity userProfile) {
        return this.businessDao.insertUserProfile(userProfile);
    }

    @Transaction
    public void deleteUserProfile() {
        int logoutStatus =this.businessDao.deleteUserProfile();
        Log.d("logoutStatus ->",""+logoutStatus);

    }

    @Transaction
    public void updateBusinessAdministrativeData(String bookId, Address address){
        this.businessDao.updateBusinessAdministrativeData(
                bookId,
                address.getProvince(),
                address.getCity(),
                address.getDistrict(),
                address.getSubDistrict(),
                address.getPostalCode(),
                address.getFullAddress()
        );
    }

    @Transaction
    public void updateExistingBookNames(String bookId, int bookType, String bookTypeName) {
        this.businessDao.migrateExistingBook(bookId, bookType, bookTypeName);
    }

    public BookEntity getBnplBookId() {
        return this.businessDao.getBnplBookId();
    }


//    private void loadCashInCategory(FirebaseFirestore firestore) {
//        CollectionReference collectionReference =
//                firestore.collection("app_cash_category").document("app_cash_in_category").collection("cash");
//        collectionReference.get().addOnSuccessListener(queryDocumentSnapshots -> {
//            List<Category> categories = queryDocumentSnapshots.toObjects(Category.class);
//            AppConfigManager.getInstance().setCashCategory(categories, 1);
//        });
//    }
//
//    private void loadCashOutCategory(FirebaseFirestore firestore) {
//        CollectionReference collectionReference =
//                firestore.collection("app_cash_category").document("app_cash_out_category").collection("cash");
//        collectionReference.get().addOnSuccessListener(queryDocumentSnapshots -> {
//            List<Category> categories = queryDocumentSnapshots.toObjects(Category.class);
//            AppConfigManager.getInstance().setCashCategory(categories, -1);
//        });
//    }

}
