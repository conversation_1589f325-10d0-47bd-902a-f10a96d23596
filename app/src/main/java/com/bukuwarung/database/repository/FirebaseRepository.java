package com.bukuwarung.database.repository;

import android.content.Context;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.FirestoreConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.EoyEntry;
import com.bukuwarung.database.entity.UserConfig;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.firestore.CollectionReference;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class FirebaseRepository {
    private static FirebaseRepository sInstance;
    private FirebaseFirestore mFirestore;

    private FirebaseRepository(Context context) {
        this.mFirestore = FirebaseFirestore.getInstance();
    }

    public static FirebaseRepository getInstance(Context context) {
        try{
            if(context == null) {
                FirebaseCrashlytics.getInstance().log("context is null: FirebaseRepository");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        try {
            if (sInstance == null) {
                synchronized (FirebaseRepository.class) {
                    if (sInstance == null) {
                        sInstance = new FirebaseRepository(context);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return sInstance;
    }

    public void createInitialUserConfig() {
        UserConfig userConfig = new UserConfig();
        userConfig.userId = User.getUserId();
        userConfig.lastBackup = System.currentTimeMillis();
        userConfig.lastRestore = System.currentTimeMillis();
        userConfig.activeAccounts = 1;
        userConfig.hasAgreedTnc = false;
        userConfig.tncTimestamp =0;
        mFirestore.collection(FirestoreConst.COLLECTION_USER_CONFIG)
                .document(User.getUserId()).set(userConfig);

    }

    public void checkAgreedTnC() {
        mFirestore.collection(FirestoreConst.COLLECTION_USER_CONFIG)
                .whereEqualTo("userId", User.getUserId())
                .get()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        int itemCount = task.getResult().size();
                        if (itemCount > 0) {
                            for (QueryDocumentSnapshot doc : task.getResult()) {
                                UserConfig userConfig = doc.toObject(UserConfig.class);
                                SessionManager.getInstance().setHasAgreedTnC(userConfig.hasAgreedTnc);
                            }
                        }
                    }
                });
    }

    public void setAgreedTnC(boolean isAgree) {
        Map<String, Object> toTnCUpdate = new HashMap<>();
        toTnCUpdate.put(UserConfig.HAS_AGREED_TNC, isAgree);
        toTnCUpdate.put(UserConfig.TNC_TIMESTAMP, new Date().getTime());

        mFirestore.collection(FirestoreConst.COLLECTION_USER_CONFIG)
                .document(User.getUserId())
                .update(toTnCUpdate);

        SessionManager.getInstance().setHasAgreedTnC(isAgree);
    }
}
