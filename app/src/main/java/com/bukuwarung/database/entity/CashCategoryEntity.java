package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "cash_category")
public class CashCategoryEntity extends AppEntity {
    @ColumnInfo(name = "balance")
    public Double balance = Double.valueOf(0.0d);
    @ColumnInfo(name = "book_id")
    public String bookId;
    @ColumnInfo(name = "cash_category_id")
    @PrimaryKey
    @NonNull
    public String cashCategoryId;
    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);
    @ColumnInfo(name = "due_date")
    public String dueDate;
    @ColumnInfo(name = "type")
    public Integer type = 0;
    @ColumnInfo(name = "language")
    public Integer language = null;
    @ColumnInfo(name = "last_modified_at")
    public Long lastModifiedAt = Long.valueOf(0);
    @ColumnInfo(name = "name")
    public String name;
    @ColumnInfo(name = "frequency")
    public Integer frequency = Integer.valueOf(0);
    public CashCategoryEntity(String str, String str2) {
        this.bookId = str;
        this.cashCategoryId = str2;
    }
    public CashCategoryEntity(){}

    public CashCategoryEntity(String cashCategoryId, String bookId, String name,
                              Double balance, Integer deleted, Integer type,
                              Integer frequency, Long lastModifiedAt) {
        this.cashCategoryId = cashCategoryId;
        this.bookId = bookId;
        this.name = name;
        this.balance = balance;
        this.deleted = deleted;
        this.type = type;
        this.frequency = frequency;
        this.lastModifiedAt = lastModifiedAt;

    }
}
