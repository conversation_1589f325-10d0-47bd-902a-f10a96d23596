package com.bukuwarung.database.entity;

import android.text.TextUtils;

import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.Utility;
import com.google.firebase.firestore.Exclude;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "books")
public class BookEntity extends AppEntity {
    @ColumnInfo(name = "alt_book_id")
    public String altBookId;

    @ColumnInfo(name = "book_id")
    @PrimaryKey
    @NonNull
    public String bookId;
    @ColumnInfo(name = "book_name")
    public String bookName;
    @ColumnInfo(name = "book_type")
    public Integer bookType;
    @ColumnInfo(name = "book_type_name")
    public String bookTypeName;
    @ColumnInfo(name = "business_address")
    public String businessAddress;
    @ColumnInfo(name = "business_email")
    public String businessEmail;
    @ColumnInfo(name = "business_image")
    public String businessImage;
    @ColumnInfo(name = "business_image_upload_pending")
    public Integer businessImageUploadPending = Integer.valueOf(0);
    @ColumnInfo(name = "business_name")
    public String businessName;
    @ColumnInfo(name = "business_owner_name")
    public String businessOwnerName;
    @ColumnInfo(name = "business_phone")
    public String businessPhone;
    @ColumnInfo(name = "business_tag_line")
    public String businessTagLine;
    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);
    @ColumnInfo(name = "enable_sms_alerts")
    public Integer enableSmsAlerts = Integer.valueOf(0);
    @ColumnInfo(name = "enable_txn_detail_sharing")
    public Integer enableTxnDetailSharing;
    @ColumnInfo(name = "language")
    public Integer language = Integer.valueOf(-1);
    @ColumnInfo(name = "owner_id")
    public String ownerId;
    @ColumnInfo(name = "enabled_payment")
    public Integer enabledPayment = Integer.valueOf(0);
    @ColumnInfo(name = "is_guest")
    public Integer isGuest = Integer.valueOf(0);
    @ColumnInfo(name = "business_logo")
    public String businessLogo;
    @ColumnInfo(name = "business_logo_upload_pending")
    public Integer businessLogoUploadPending = Integer.valueOf(0);
    @NonNull
    @ColumnInfo(name = "is_daily_business_update_seen")
    public Integer isDailyBusinessUpdateSeen = Integer.valueOf(0);
    @ColumnInfo(name = "operating_hours_start")
    public String operatingHourStart="";
    @ColumnInfo(name = "operating_hours_end")
    public String operatingHourEnd="";
    @ColumnInfo(name = "operating_days")
    public String operatingDays="";
    @ColumnInfo(name = "emp_count")
    public Integer empCount=Integer.valueOf(0);
    @ColumnInfo(name = "outlet_count")
    public Integer outletCount=Integer.valueOf(0);
    @ColumnInfo(name = "establishment_year")
    public String establishmentYear="";
    @ColumnInfo(name = "profile_completion_progress")
    public Integer profileCompletionProgress=Integer.valueOf(0);

    @ColumnInfo(name = "province")
    public String province="";

    @ColumnInfo(name = "city")
    public String city="";

    @ColumnInfo(name = "district")
    public String district="";

    @ColumnInfo(name = "subdistrict")
    public String subdistrict=""; // subDistrict

    @ColumnInfo(name = "postal_code")
    public String postalCode="";

    @ColumnInfo(name = "production")
    public String production="";
    @ColumnInfo(name = "product_buyer")
    public String productBuyer="";
    @ColumnInfo(name = "monthly_turnover")
    public Integer monthlyTurnover=Integer.valueOf(0);

    public BookEntity(String str, String str2) {
        this.bookId = str;
        this.ownerId = str2;
    }

    @Ignore
    public BookEntity() {
    }

    @Ignore
    public BookEntity(String altBookId, String bookId, String bookName, Integer bookType, String bookTypeName, String businessAddress, String businessEmail, String businessImage,
                      Integer businessImageUploadPending, String businessName, String businessOwnerName, String businessPhone, String businessTagLine, Integer deleted,
                      Integer enableSmsAlerts, Integer enableTxnDetailSharing, Integer language, String ownerId, Integer enabledPayment) {
        this.altBookId = altBookId;
        this.bookId = bookId;
        this.bookName = bookName;
        this.bookType = bookType;
        this.bookTypeName = bookTypeName;
        this.businessAddress = businessAddress;
        this.businessEmail = businessEmail;
        this.businessImage = businessImage;
        this.businessImageUploadPending = businessImageUploadPending;
        this.businessName = businessName;
        this.businessOwnerName = businessOwnerName;
        this.businessPhone = businessPhone;
        this.businessTagLine = businessTagLine;
        this.deleted = deleted;
        this.enableSmsAlerts = enableSmsAlerts;
        this.enableTxnDetailSharing = enableTxnDetailSharing;
        this.language = language;
        this.ownerId = ownerId;
        this.enabledPayment = enabledPayment;
    }

    @Ignore
    public BookEntity(String altBookId, String bookId, Integer bookType, String bookName, String businessName) {
        this.altBookId = altBookId;
        this.bookId = bookId;
        this.bookName = bookName;
        this.bookType = bookType;
        this.businessName = businessName;

    }

    public BookEntity(String id, String ownerId, String ownerNm, String businessNm, int businessType) {
        this.bookId = id;
        this.ownerId = ownerId;
        this.businessName = businessNm;
        bookName = businessNm;
        this.businessOwnerName = ownerNm;
        this.businessPhone = SessionManager.getInstance().isGuestUser()?"":ownerId;
        this.businessName = businessNm;
        this.bookType = businessType;
    }

    public BookEntity(String bookId, String businessName, Integer businessType, String businessTypeName, String businessAddress, String businessEmail, String businessImage, Integer businessImageUploadPending,
                      String businessOwnerName, String businessPhone, String businessTagLine, Integer deleted, Integer enableSmsAlerts, Integer enableTxnDetailSharing, Integer language,
                      String ownerId) {

        this.bookId = bookId;
        this.businessName = businessName;
        bookName = businessName;
        this.bookType = businessType;
        this.bookTypeName = businessTypeName;
        this.businessAddress = businessAddress;
        this.businessEmail = businessEmail;
        this.businessImage = businessImage;
        this.businessImageUploadPending = businessImageUploadPending;
        this.businessName = businessName;
        this.businessOwnerName = businessOwnerName;
        this.businessPhone = SessionManager.getInstance().isGuestUser()?"":ownerId;

        this.businessTagLine = businessTagLine;
        this.deleted = deleted;
        this.enableSmsAlerts = enableSmsAlerts;
        this.enableTxnDetailSharing = enableTxnDetailSharing;
        this.language = language;
        this.ownerId = ownerId;
    }

    @Ignore
    public BookEntity(String bookId, Integer bookType, String businessName) {
        this.bookId = bookId;
        this.businessName = businessName;
        bookName = businessName;
        this.bookType = bookType;
    }

    public BookEntity(String ownerId, String bookId, Integer language, String bookName, Integer bookType, String bookTypeName, String businessAddress, String businessEmail, String businessImage,
                      String businessName, String businessOwnerName, String businessPhone, String businessTagLine,
                      Integer deleted, Integer enableSmsAlerts, Integer enabledPayment) {
        this.bookId = bookId;
        this.bookName = bookName;
        this.bookType = bookType;
        this.bookTypeName = bookTypeName;
        this.businessAddress = businessAddress;
        this.businessEmail = businessEmail;
        this.businessImage = businessImage;
        this.businessName = businessName;
        this.businessOwnerName = businessOwnerName;
        this.businessPhone = businessPhone;
        this.businessTagLine = businessTagLine;
        this.deleted = deleted;
        this.enableSmsAlerts = enableSmsAlerts;
        this.language = language;
        this.enabledPayment = enabledPayment;
        this.ownerId = ownerId;
    }

    public boolean hasCompletedProfile() {
        boolean ret = this.businessName != null && !businessName.isEmpty()
                && !this.businessName.equalsIgnoreCase("Usaha Saya")
                && this.businessOwnerName != null && !this.businessOwnerName.isEmpty()
                && !this.businessOwnerName.equalsIgnoreCase("BukuWarung")
                && this.bookType != null && this.bookType != -1;
        return ret;
    }

    public boolean hasCompletedProfileWithoutOwnerName() {
        boolean ret = this.businessName != null && !businessName.isEmpty()
                && !this.businessName.equalsIgnoreCase("Usaha Saya") && !this.businessName.equalsIgnoreCase("BukuWarung")
                && this.bookType != null && this.bookType != -1;
        return ret;
    }

    public boolean hasCompletedProfileWithOwnerName() {
        boolean ret = this.businessOwnerName != null && !businessOwnerName.isEmpty()
                && !this.businessOwnerName.equalsIgnoreCase("Usaha Saya") && !this.businessOwnerName.equalsIgnoreCase("BukuWarung") && !this.businessOwnerName.equalsIgnoreCase("Profil Saya");
        return ret;
    }

    public boolean hasCompletedCard() {
        boolean ret = this.businessName != null && !businessName.isEmpty()
                && !this.businessName.equalsIgnoreCase("Usaha Saya")
                && this.businessOwnerName != null && !this.businessOwnerName.isEmpty()
                && !this.businessOwnerName.equalsIgnoreCase("BukuWarung")
                && this.bookType != null && this.bookType != -1 && this.businessTagLine != null
                && !this.businessTagLine.isEmpty() && this.businessAddress != null
                && !this.businessAddress.isEmpty() && this.businessPhone != null
                && !this.businessPhone.isEmpty() && this.businessEmail != null
                && !this.businessEmail.isEmpty();
        return ret;
    }

    public boolean hasCompletedCardNoEmail() {
        boolean ret = this.businessName != null && !businessName.isEmpty()
                && !this.businessName.equalsIgnoreCase("Usaha Saya")
                && this.businessOwnerName != null && !this.businessOwnerName.isEmpty()
                && !this.businessOwnerName.equalsIgnoreCase("BukuWarung")
                && this.bookType != null && this.businessTagLine != null
                && !this.businessTagLine.isEmpty() && this.businessAddress != null
                && !this.businessAddress.isEmpty() && this.businessPhone != null
                && !this.businessPhone.isEmpty();
        return ret;
    }

    @Exclude
    public String getInitial() {
        return TextUtils.isEmpty(businessName) ? "?" : String.valueOf(businessName.charAt(0)).toUpperCase();
    }

}
