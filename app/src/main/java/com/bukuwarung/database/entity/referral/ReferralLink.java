package com.bukuwarung.database.entity.referral;

import java.util.ArrayList;
import java.util.List;

public class ReferralLink {
    public String deeplink;
    public String sharedlink;
    public String sender;
    public String code;
    public List<ReferralReceiver> receiver;
    public boolean enabled;
    public boolean active;
    public long createdAt;
    public long earnedPoints;

    public static class Builder {
        private ReferralLink referralLink;
        public Builder() {
            referralLink = new ReferralLink();
            referralLink.receiver = new ArrayList();
        }
        public Builder setDeeplink(String deeplink) {
            referralLink.deeplink = deeplink;
            return this;
        }

        public Builder setSharedlink(String sharedlink) {
            referralLink.sharedlink = sharedlink;
            return this;
        }

        public Builder setSender(String sender) {
            referralLink.sender = sender;
            return this;
        }

        public Builder setReceiver(List<ReferralReceiver> receiver) {
            referralLink.receiver = receiver;
            return this;
        }

        public Builder setActive(boolean active) {
            referralLink.active = active;
            return this;
        }

        public Builder setEnabled(boolean enabled) {
            referralLink.enabled = enabled;
            return this;
        }

        public Builder setCode(String code) {
            referralLink.code = code;
            return this;
        }

        public Builder setEarnedPoints(long earnedPoints) {
            referralLink.earnedPoints = earnedPoints;
            return this;
        }

        public Builder setCreatedAt(long createdAt) {
            referralLink.createdAt = createdAt;
            return this;
        }
        public ReferralLink build() {
            return referralLink;
        }
    }
}
