package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.bukuwarung.database.helper.AttechmentSerializer;
import com.google.firebase.firestore.Exclude;
import com.google.gson.annotations.JsonAdapter;

import javax.annotation.Nullable;

@Entity(tableName = "cash_transactions")
public class CashTransactionEntity extends AppEntity {

    //selling price
    @ColumnInfo(name = "amount")
    public Double amount = Double.valueOf(0.0d);
    @ColumnInfo(name = "buying_price")
    public Double buyingPrice = Double.valueOf(0.0d);

    @ColumnInfo(name = "attachments")
    @JsonAdapter(AttechmentSerializer.AttachmentsSerDes.class)
    public String attachments;
    @ColumnInfo(name = "attachments_upload_pending")
    public Integer attachmentsUploadPending = Integer.valueOf(0);
    @ColumnInfo(name = "book_id")
    public String bookId;
    @ColumnInfo(name = "cash_category_id")
    public String cashCategoryId;
    @ColumnInfo(name = "date")
    public String date;
    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);
    @ColumnInfo(name = "is_offline")
    public Integer isOffline = 0;
    @ColumnInfo(name = "description")
    public String description;
    @ColumnInfo(name = "transaction_type")
    public TransactionEntityType transactionType = TransactionEntityType.DEFAULT;
    @ColumnInfo(name = "order_id")
    public String orderId="";

    @ColumnInfo(name = "cash_transaction_id")
    @PrimaryKey
    @NonNull
    public String cashTransactionId;

    @ColumnInfo(name = "customer_transaction_id")
    @Nullable
    public String customerTransactionId;

    @ColumnInfo(name = "customer_id")
    @Nullable
    public String customerId;

    @ColumnInfo(name = "customer_name")
    @Nullable
    public String customerName;

    @ColumnInfo(name = "mobile_number")
    @Nullable
    public String customerPhoneNumber;
    /**
     * 0 = Belum Lunas
     * 1 = Lunas
     */
    @ColumnInfo(name = "status")
    public int status = -1;

    @ColumnInfo(name = "custom_amount")
    public Double customAmount = 0.0;

    @ColumnInfo(name = "payment_method")
    public String paymentMethod = "CASH";

    @ColumnInfo(name = "brick_institution_id")
    public int brickInstitutionId = -1;

    //flag to identify transactions that require item restore when opening transaction detail screen
    @Exclude
    @ColumnInfo(name = "restore_transaction_items")
    public int restoreTransactionItems = 0;

    public CashTransactionEntity(String str, String str2, String str3) {
        this.bookId = str;
        this.cashCategoryId = str2;
        this.cashTransactionId = str3;
    }

    public CashTransactionEntity() {
    }

    public CashTransactionEntity(Double amount, Double buyingPrice, String attachments,
                                 String bookId, String cashCategoryId,
                                 String date, Integer deleted, String description,
                                 String orderId, String cashTransactionId, String customerTransactionId,
                                 String customerId, String customerName, String customerPhoneNumber,
                                 Integer status, String paymentMethod) {
        this.amount = amount;
        this.buyingPrice = buyingPrice;
        this.attachments = attachments;
        this.bookId = bookId;
        this.description = description;
        this.cashCategoryId = cashCategoryId;
        this.date = date;
        this.deleted = deleted;
        this.orderId = orderId;
        this.cashTransactionId = cashTransactionId;
        this.customerTransactionId = customerTransactionId;
        this.customerId = customerId;
        this.customerName = customerName;
        this.customerPhoneNumber = customerPhoneNumber;
        this.status = status;
        this.paymentMethod = paymentMethod;
    }
}
