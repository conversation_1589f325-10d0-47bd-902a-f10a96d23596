package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "transactions")
public class TransactionEntity extends AppEntity {
    @ColumnInfo(name = "amount")
    public Double amount = Double.valueOf(0.0d);
    @ColumnInfo(name = "attachments")
    public String attachments;
    @ColumnInfo(name = "attachments_upload_pending")
    public Integer attachmentsUploadPending = Integer.valueOf(0);
    @ColumnInfo(name = "book_id")
    public String bookId;
    @ColumnInfo(name = "customer_id")
    public String customerId;
    @ColumnInfo(name = "date")
    public String date;//41ce387847a2d1bfd706f0eda622b5af7003512a
    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);
    @ColumnInfo(name = "description")
    public String description;
    @ColumnInfo(name = "sms_status")
    public Integer smsStatus = Integer.valueOf(0);
    @ColumnInfo(name = "transaction_type")
    public TransactionEntityType transactionType = TransactionEntityType.DEFAULT;
    @ColumnInfo(name = "is_offline")
    public Integer isOffline = 0;
    @ColumnInfo(name = "payment_disbursable_id")
    public String paymentDisbursableId;
    @ColumnInfo(name = "transaction_id")
    @PrimaryKey
    @NonNull
    public String transactionId;

    public TransactionEntity(String str, String str2, String str3) {
        this.bookId = str;
        this.customerId = str2;
        this.transactionId = str3;
    }

    public TransactionEntity(String transactionId, String attachments, String bookId, String customerId, String date, String description,
                             Double amount,
                             Integer deleted, Integer isOffline, String paymentDisbursableId) {
        this.transactionId = transactionId;
        this.bookId = bookId;
        this.customerId = customerId;
        this.date = date;
        this.description = description;
        this.amount = amount;
        this.deleted = deleted;
        this.isOffline = isOffline;
        this.attachments = attachments;
        this.paymentDisbursableId = paymentDisbursableId;
    }

    public TransactionEntity() {
    }

    public boolean isPaymentTransaction() {
        return transactionType == TransactionEntityType.PAYMENT;
    }
}
