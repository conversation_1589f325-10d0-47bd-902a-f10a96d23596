package com.bukuwarung.database.entity;

import java.util.List;

public class AppConfig {
    public String chatId;
    public String whatsappId;
    public String retry;
    public String leaderboardWebUrl;
    public String latestVersion;
    public int useFlexibleUpdate;
    public String updateInfo;
    public String updateInfoEn;
    public String updateInfoId;
    public String reportApi;
    public String syncApi;
    public String fileUploadApi;
    public String fileBucket;
    public int transactionCountForNewDesign;
    public int updateFreq;
    public int manualReminderPaymentLinkCount;
    public int informasiOptionalCount;
    public int whatsappAuth;
    public int tooltipTarget;
    public int rateTargetCount;
    public int enableVideo;
    public int otpWaitTimeInSec;
    public int enableReportId;
    public int enableReportPdf;
    public int enableCstPdf;
    public int enableGuestFeature;
    public int guestTxnLimit;
    public int enableLiveSync;
    public int referralTransactionVal;
    public int leaderBoardSize;
    public int enableGamifyDialog;
    public String smsApi;
    public String txnApi;
    public String ampapi;
    public String afapi;
    public String moapi;
    public String streaksApi;
    public int streakDuration;
    public String otpApi;
    public String notificationIcon;
    public boolean useServerCategory = false;
    public boolean enableCashModule = false;
    public boolean useWebView = false;
    public boolean uploadContact = false;

    public int kycCriteriaAmount;
    public int kycCriteriaTrxCount;
    public String kycCriteriaType;

    public String referralTitle;
    public String referralBanner;
    public String referralPrizeBanner;
    public String referralBaseUrl;
    public String referralApiUrl;
    public String leaderboardApiUrl;
    public List<String> referralSteps;
    public List<String> referralPrizes;
    public List<String> referralTncs;
    public boolean referralFeatureActive;
    public boolean referralSharingActive;
    public boolean syncEnabled;
    public boolean collectionCalendarFeatureActive;
    public String referralMessage;
    public String referralLeaderboardMessage;
    public boolean pinCodeFeatureActive;
    public String defaultWABotMessage;
    public boolean bukuPayLogoActive;

    public int forceUpdateVersionCode;
    public int softUpdateVersionCode;

    public String smsTemplateCredit;
    public String smsTemplateDebit;

    public int defaultTabLogin;
    public String defaultTabName;
    public int defaultTabTarget;
    public int defaultCashOption;
    public int trxThresholdPaymentTab;
    public int trxThresholdPpob;
    public String welcomeText;

    public int paymentIntervalInMin;
    public String paymentInMsg;
    public String paymentOutMsg;
    public int pullRefresh;
    public boolean paymentFreeChargeStatus;

    public String utangMessage;
    public String utangReviewDate;
    public String utangUserName;
    public String utangUserImage;

    public String transaksiMessage;
    public String transaksiReviewDate;
    public String transaksiUserName;
    public String transaksiUserImage;
    public boolean showCustomPermissionDialog;
    public String rewardBaseUrl;
    public boolean enablePaymentReferral;
    public boolean enableBureau=true;
    public boolean enableReferralReward;
    public String excelReportApi;
    public boolean usePayloadFromApp;
    public int stockTransactionTarget;
    public int profileSetupTarget;
    public List<Integer> ppobCategoryIdList;
    public String lunaskanApi="";
    public boolean enableTokokoDownload;
    public boolean enableNewAmplitudeProject;
    public String tokokoDownloadLink;

    public AppConfig(
            String str, String seq, String latest, int useFlexibleUpdate, String updateInfo, String updateInfoEn, String updateInfoId,int informasiOptionalCount,
            int updateFreq, String whatsappId, int enableReportId, String reportApi, int enableReportPdf,int transactionCountForNewDesign,
            int enableCstPdf, String syncApi, String fileUploadApi, int tooltipTarget, int rateTargetCount, int manualReminderPaymentLinkCount,
            int enableVideo, int whatsappAuth, String moapi, String afapi, String ampapi, String smsApi, String txnApi,
            String otpApi, String streaksApi, String notificationIcon, Boolean useServerCategory, boolean enableCashModule, int otpWaitTimeInSec,
            boolean useWebView, int enableLiveSync, String referralTitle, String referralBanner, String referralPrizeBanner,
            String referralBaseUrl, String referralApiUrl, String leaderboardApiUrl, List<String> referralSteps,
            List<String> referralPrizes, List<String> referralTncs, boolean referralFeatureActive, boolean referralSharingActive,
            boolean collectionCalendarFeatureActive, String referralMessage, String referralLeaderboardMessage,
            boolean pinCodeFeatureActive, boolean uploadContact, int forceUpdateVersionCode, int softUpdateVersionCode,
            String defaultWABotMessage, String smsTemplateCredit, String smsTemplateDebit, int referralTransactionVal, int leaderBoardSize, int enableGamifyDialog,
            int defaultTabLogin, String defaultTabName, int trxThresholdPaymentTab, int defaultTabTarget, int defaultCashOption,  boolean bukuPayLogoActive, String welcomeText,
            int paymentIntervalInMin, String paymentInMsg, String paymentOutMsg, int pullRefresh, String fileBucket,
            String utangMessage, String utangUserName, String utangReviewDate, String utangUserImage, boolean syncEnabled,
            String transaksiMessage, String transaksiReviewDate, String transaksiUserName, String transaksiUserImage, int enableGuestFeature, int guestTxnLimit, boolean paymentFreeChargeStatus, boolean showCustomPermissionDialog,
            boolean ppobPulsaAvailable, boolean ppobPostpaidPulsaAvailable, boolean ppobListrikAvailable, boolean ppobPostpaidListrikAvailable, boolean ppobEwalletAvailable, boolean ppobGamingVoucherAvailable, boolean ppobPaketDataAvailable, int trxThresholdPpob, List<Integer> ppobCategoryIdList,
            int kycCriteriaAmount, int kycCriteriaTrxCount, String kycCriteriaType, boolean ppobBpjsAvailable, boolean enableNewAmplitudeProject, boolean enableBureau) {

        this.chatId = str;
        this.retry = seq;
        this.enableBureau = enableBureau;
        this.streaksApi = streaksApi;
        this.whatsappId = whatsappId;
        this.enableReportId = enableReportId;
        this.guestTxnLimit = guestTxnLimit;
        this.latestVersion = latest;
        this.useFlexibleUpdate = useFlexibleUpdate;
        this.updateInfo = updateInfo;
        this.updateInfoEn = updateInfoEn;
        this.updateInfoId = updateInfoId;
        this.syncEnabled = syncEnabled;
        this.updateFreq = updateFreq;
        this.reportApi = reportApi;
        this.enableCstPdf = enableCstPdf;
        this.enableGuestFeature = enableGuestFeature;
        this.enableReportPdf = enableReportPdf;
        this.syncApi = syncApi;
        this.enableLiveSync = enableLiveSync;
        this.fileUploadApi = fileUploadApi;
        this.tooltipTarget = tooltipTarget;
        this.rateTargetCount = rateTargetCount;
        this.enableVideo = enableVideo;
        this.enableNewAmplitudeProject = enableNewAmplitudeProject;
        this.whatsappAuth = whatsappAuth;
        this.manualReminderPaymentLinkCount = manualReminderPaymentLinkCount;
        this.transactionCountForNewDesign = transactionCountForNewDesign;
        this.smsApi = smsApi;
        this.txnApi = txnApi;
        this.moapi = moapi;
        this.ampapi = ampapi;
        this.afapi = afapi;
        this.otpApi = otpApi;
        this.informasiOptionalCount = informasiOptionalCount;
        this.notificationIcon = notificationIcon;
        this.useServerCategory = useServerCategory;
        this.enableCashModule = enableCashModule;
        this.otpWaitTimeInSec = otpWaitTimeInSec;
        this.useWebView = useWebView;
        this.referralTitle = referralTitle;
        this.referralBanner = referralBanner;
        this.referralTransactionVal = referralTransactionVal;
        this.referralPrizeBanner = referralPrizeBanner;
        this.referralBaseUrl = referralBaseUrl;
        this.referralApiUrl = referralApiUrl;
        this.leaderboardApiUrl = leaderboardApiUrl;
        this.referralSteps = referralSteps;
        this.referralPrizes = referralPrizes;
        this.referralTncs = referralTncs;
        this.referralFeatureActive = referralFeatureActive;
        this.referralSharingActive = referralSharingActive;
        this.collectionCalendarFeatureActive = collectionCalendarFeatureActive;
        this.referralMessage = referralMessage;
        this.referralLeaderboardMessage = referralLeaderboardMessage;
        this.pinCodeFeatureActive = pinCodeFeatureActive;
        this.uploadContact = uploadContact;
        this.forceUpdateVersionCode = forceUpdateVersionCode;
        this.softUpdateVersionCode = softUpdateVersionCode;
        this.defaultWABotMessage = defaultWABotMessage;
        this.smsTemplateCredit = smsTemplateCredit;
        this.smsTemplateDebit = smsTemplateDebit;
        this.leaderBoardSize = leaderBoardSize;
        this.enableGamifyDialog = enableGamifyDialog;
        this.defaultCashOption = defaultCashOption;
        this.defaultTabLogin = defaultTabLogin;
        this.defaultTabName = defaultTabName;
        this.trxThresholdPaymentTab = trxThresholdPaymentTab;
        this.defaultTabTarget = defaultTabTarget;
        this.bukuPayLogoActive = bukuPayLogoActive;
        this.welcomeText = welcomeText;
        this.paymentIntervalInMin = paymentIntervalInMin;
        this.paymentInMsg = paymentInMsg;
        this.paymentOutMsg = paymentOutMsg;
        this.pullRefresh = pullRefresh;
        this.fileBucket = fileBucket;
        this.utangMessage = utangMessage;
        this.utangReviewDate = utangReviewDate;
        this.utangUserName = utangUserName;
        this.utangUserImage = utangUserImage;

        this.transaksiMessage = transaksiMessage;
        this.transaksiReviewDate = transaksiReviewDate;
        this.transaksiUserName = transaksiUserName;
        this.transaksiUserImage = transaksiUserImage;

        this.paymentFreeChargeStatus = paymentFreeChargeStatus;
        this.showCustomPermissionDialog = showCustomPermissionDialog;
        this.trxThresholdPpob = trxThresholdPpob;
        this.ppobCategoryIdList = ppobCategoryIdList;

        this.kycCriteriaAmount = kycCriteriaAmount;
        this.kycCriteriaTrxCount = kycCriteriaTrxCount;
        this.kycCriteriaType = kycCriteriaType;
    }


    public AppConfig() {
    }
}
