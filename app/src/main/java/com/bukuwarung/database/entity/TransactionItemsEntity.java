package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

@Entity(tableName = "transaction_items", primaryKeys = {"transaction_id","product_id"})
public class TransactionItemsEntity extends AppEntity {

    @ColumnInfo(name = "quantity")
    public Double quantity = 0.0d;

    @NonNull
    @ColumnInfo(name = "name")
    public String name="";

    @NonNull
    @ColumnInfo(name = "buying_price")
    public Double buyingPrice=Double.valueOf(0.0d);

    @NonNull
    @ColumnInfo(name = "selling_price")
    public Double sellingPrice=Double.valueOf(0.0d);

    @NonNull
    @ColumnInfo(name = "measurement_name")
    public String measurementName="";

    @ColumnInfo(name = "transaction_id")
    @NonNull
    public String transactionId;

    //transaction type can be utang or cash txn
    @ColumnInfo(name = "type")
    public Integer type = Integer.valueOf(0);

    @ColumnInfo(name = "product_id")
    @NonNull
    public String productId;

    @ColumnInfo(name = "inventory_history_id")
    public String inventoryHistoryId;



    public TransactionItemsEntity(@NonNull String name, @NonNull Double buyingPrice, @NonNull Double sellingPrice, @NonNull String measurementName, @NonNull String transactionId, @NonNull String productId, Double quantity) {
        this.transactionId = transactionId;
        this.productId = productId;
        this.quantity = quantity;
        this.name = name;
        this.buyingPrice = buyingPrice;
        this.sellingPrice = sellingPrice;
        this.measurementName = measurementName;
    }

    @Ignore
    public TransactionItemsEntity(){}

    @Ignore
    public TransactionItemsEntity(Double quantity,Integer type,String transactionId,String productId){
        this.transactionId=transactionId;
        this.productId=productId;
        this.quantity=quantity;
        this.type=type;
    }
}
