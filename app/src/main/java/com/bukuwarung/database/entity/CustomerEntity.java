package com.bukuwarung.database.entity;

import android.text.TextUtils;

import com.bukuwarung.utils.DateTimeUtils;
import com.google.firebase.firestore.Exclude;

import java.io.Serializable;
import java.util.Date;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "customers")
public class CustomerEntity extends AppEntity implements Serializable {
    @ColumnInfo(name = "address")
    public String address;
    @ColumnInfo(name = "alt_customer_id")
    public String altCustomerId;
    @ColumnInfo(name = "balance")
    public Double balance = Double.valueOf(0.0d);
    @ColumnInfo(name = "book_id")
    public String bookId;
    @ColumnInfo(name = "country_code")
    public String countryCode;
    @ColumnInfo(name = "customer_id")
    @PrimaryKey
    @NonNull
    public String customerId;
    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);
    @ColumnInfo(name = "due_date")
    public String dueDate;
    @ColumnInfo(name = "enable_sms_alerts")
    public Integer enableSmsAlerts = Integer.valueOf(1);
    @ColumnInfo(name = "enable_txn_detail_sharing")
    public Integer enableTxnDetailSharing;
    @ColumnInfo(name = "image")
    public String image;
    @ColumnInfo(name = "image_upload_pending")
    public Integer imageUploadPending = Integer.valueOf(0);
    @ColumnInfo(name = "language")
    public Integer language = null;
    @ColumnInfo(name = "last_modified_at")
    public Long lastModifiedAt = Long.valueOf(0);
    @ColumnInfo(name = "name")
    public String name;
    @ColumnInfo(name = "phone")
    public String phone;

    public CustomerEntity(String bookId, @NonNull String customerId) {
        this.bookId = bookId;
        this.customerId = customerId;
    }

    public CustomerEntity() {
    }

    public CustomerEntity(String bookId, String address, String altCustomerId, String customerId,
                          String countryCode, String dueDate, String image, String name, String phone,
                          Double balance, Integer deleted, Integer language,
                          Long lastModifiedAt) {
        this.bookId = bookId;
        this.address = address;
        this.altCustomerId = altCustomerId;
        this.customerId = customerId;
        this.countryCode = countryCode;
        this.dueDate = dueDate;
        this.image = image;
        this.name = name;
        this.phone = phone;
        this.balance = balance;
        this.deleted = deleted;
        this.language = language;
        this.lastModifiedAt = lastModifiedAt;

    }

    @Exclude
    public String getInitial() {
        return TextUtils.isEmpty(name) ? "?" : String.valueOf(name.charAt(0)).toUpperCase();
    }

    @Exclude
    public boolean isDueDatePassed() {
        return !TextUtils.isEmpty(dueDate) && DateTimeUtils.convertToDateYYYYMMDD(dueDate).getTime() <= new Date().getTime();
    }

    @Exclude
    public String getFormattedDueDate() {
        if (TextUtils.isEmpty(dueDate)) {
            return "";
        }

        return DateTimeUtils.getReadableDate(DateTimeUtils.convertToDateYYYYMMDD(dueDate).getTime());
    }

    // Overriding equals() to compare two Complex objects
    @Override
    public boolean equals(Object o) {
        try {
            // If the object is compared with itself then return true
            if (o == this) {
                return true;
            }

        /* Check if o is an instance of ReferralReceiver or not
          "null instanceof [type]" also returns false */
            if (!(o instanceof CustomerEntity)) {
                return false;
            }

            // typecast o to Complex so that we can compare data members
            CustomerEntity c = (CustomerEntity) o;

            // Compare the data members and return accordingly
            return phone.equalsIgnoreCase(c.phone);
        } catch (Exception ex) {
            return false;
        }
    }
}
