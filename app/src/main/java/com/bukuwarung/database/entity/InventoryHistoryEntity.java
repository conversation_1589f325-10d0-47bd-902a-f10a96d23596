package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "inventory_history")
public class InventoryHistoryEntity extends AppEntity {

    @ColumnInfo(name = "history_id")
    @PrimaryKey
    @NonNull
    public String historyId;

    @ColumnInfo(name = "product_id")
    @NonNull
    public String productId;

    @ColumnInfo(name = "book_id")
    @NonNull
    public String bookId;

    @ColumnInfo(name = "current_stock")
    public Double currentStock = 0.0;

    @ColumnInfo(name = "minimum_stock")
    public Integer minimumStock = 0;

    @ColumnInfo(name = "quantity_change")
    public Double quantityChange = 0.0;

    @ColumnInfo(name = "operation_type")
    public InventoryOperationType operationType = InventoryOperationType.DEFAULT;

    @NonNull
    @ColumnInfo(name = "buying_price")
    public Double buyingPrice = Double.valueOf(0.0d);

    @NonNull
    @ColumnInfo(name = "selling_price")
    public Double sellingPrice = Double.valueOf(0.0d);

    @NonNull
    @ColumnInfo(name = "measurement_name")
    public String measurementName = "";

    public InventoryHistoryEntity(){}

    public InventoryHistoryEntity(@NonNull String historyId, @NonNull String productId, @NonNull String bookId, Double currentStock, Integer minimumStock, Double updatedQuantity) {
        this.historyId = historyId;
        this.productId = productId;
        this.bookId = bookId;
        this.currentStock = currentStock;
        this.minimumStock = minimumStock;
        this.quantityChange = updatedQuantity;
    }
}
