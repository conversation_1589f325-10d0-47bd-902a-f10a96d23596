package com.bukuwarung.database.entity.enums;

import com.bukuwarung.R;

public enum ReminderCategory {
    UTANG("Utang Piutang", R.drawable.ic_hutang_inactive),
    TRANSAKSI("Transaksi",R.drawable.ic_transaction_inactive),
    PAYMENT("Pembayaran",R.drawable.pembayaran);
    private final String text;
    private final int icon;

    ReminderCategory(final String text, final int icon) {
        this.text = text;
        this.icon = icon;
    }

    @Override
    public String toString() {
        return text;
    }

    public int getIcon(){
        return this.icon;
    }
}
