package com.bukuwarung.database.entity.referral;

public class ReferralReceiver {
    public String userId;
    public int transactionCount = 0;
    public int earnedPoints = 0;

    public ReferralReceiver(
            String userId,
            int transactionCount,
            int earnedPoints
    ) {
        this.userId = userId;
        this.transactionCount = transactionCount;
        this.earnedPoints = earnedPoints;
    }

    public ReferralReceiver() { }

    // Overriding equals() to compare two Complex objects
    @Override
    public boolean equals(Object o) {

        // If the object is compared with itself then return true
        if (o == this) {
            return true;
        }

        /* Check if o is an instance of ReferralReceiver or not
          "null instanceof [type]" also returns false */
        if (!(o instanceof ReferralReceiver)) {
            return false;
        }

        // typecast o to Complex so that we can compare data members
        ReferralReceiver c = (ReferralReceiver) o;

        // Compare the data members and return accordingly
        return userId.equalsIgnoreCase(c.userId);
    }
}
