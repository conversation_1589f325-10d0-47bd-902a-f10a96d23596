package com.bukuwarung.database.entity.referral;

import org.jetbrains.annotations.Nullable;

public class PaymentUserReferral {
    public String leaderboardName;
    public String referralCodeInUse;
    public long referralCodeRegisteredAt;
    public String sender;
    public long points=0;
    public int rank=-1;
    public boolean active;
    public long createdAt=0;
    public long updatedAt=0;
    public String myReferralCode;
    public String altRefCode;
    public String userId;

    public PaymentUserReferral(String leaderboardName, String referralCodeInUse, long referralCodeRegisteredAt, String sender, long points, int rank, boolean active, long createdAt, long updatedAt, String myReferralCode, String shareableLink, int level) {
        this.leaderboardName = leaderboardName;
        this.referralCodeInUse = referralCodeInUse;
        this.referralCodeRegisteredAt = referralCodeRegisteredAt;
        this.sender = sender;
        this.points = points;
        this.rank = rank;
        this.active = active;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.myReferralCode = myReferralCode;
        this.shareableLink = shareableLink;
        this.level = level;
    }

    public String shareableLink;
    public int level=-1;

    public PaymentUserReferral(){}
}
