
 package com.bukuwarung.database.entity;

import com.google.firebase.firestore.IgnoreExtraProperties;

 @IgnoreExtraProperties
 public class EoyEntry {

     public static final String SHOP_NAME = "shopName";
     public static final String OWNER_NAME = "ownerName";
     public static final String OWNER_PHONE = "ownerPhone";
     public static final String MESSAGE = "message";
     public static final String CREATEDAT = "createdAt";
     public static final String TYPE = "type";
     public static final String RECEIVER = "receiver";

     private String shopName;
     private int unseen;

     public int getUnseen() {
         return unseen;
     }

     public void setUnseen(int unseen) {
         this.unseen = unseen;
     }

     private String ownerName;
     private String ownerPhone;
     private String message;

     public String getNote() {
         return note;
     }

     public void setNote(String note) {
         this.note = note;
     }

     private String note;
     private long createdAt;

     public String getReceiver() {
         return receiver;
     }

     public void setReceiver(String receiver) {
         this.receiver = receiver;
     }

     private String receiver;
     private int type;

     public EoyEntry() {}

     public EoyEntry(String shopName, String ownerName, String ownerPhone, String receiver, String message, String note, long createdAt, int type) {
         this.shopName = shopName;
         this.ownerName = ownerName;
         this.ownerPhone = ownerPhone;
         this.message = message;
         this.note = note;
         this.createdAt = createdAt;
         this.receiver = receiver;
         this.type = type;
     }

     public String getShopName() {
         return shopName;
     }

     public void setShopName(String shopName) {
         this.shopName = shopName;
     }

     public String getOwnerName() {
         return ownerName;
     }

     public void setOwnerName(String ownerName) {
         this.ownerName = ownerName;
     }

     public String getOwnerPhone() {
         return ownerPhone;
     }

     public void setOwnerPhone(String ownerPhone) {
         this.ownerPhone = ownerPhone;
     }

     public String getMessage() {
         return message;
     }

     public void setMessage(String message) {
         this.message = message;
     }

     public long getCreatedAt() {
         return createdAt;
     }

     public void setCreatedAt(long createdAt) {
         this.createdAt = createdAt;
     }

     public int getType() {
         return type;
     }

     public void setType(int type) {
         this.type = type;
     }






 }
