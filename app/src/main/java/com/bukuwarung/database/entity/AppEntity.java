package com.bukuwarung.database.entity;

import androidx.room.ColumnInfo;

import com.google.gson.annotations.SerializedName;

public class AppEntity {
    @SerializedName("created_at")
    @ColumnInfo(name = "created_at")
    public Long createdAt = Long.valueOf(0);
    @ColumnInfo(name = "created_by_device")
    public String createdByDevice;
    @ColumnInfo(name = "created_by_user")
    public String createdByUser;
    @ColumnInfo(name = "dirty")
    public Integer dirty = Integer.valueOf(0);
    @ColumnInfo(name = "server_seq")
    public Long serverSeq = Long.valueOf(0);
    @ColumnInfo(name = "updated_at")
    public Long updatedAt = Long.valueOf(0);
    @ColumnInfo(name = "updated_by_device")
    public String updatedByDevice;
    @ColumnInfo(name = "updated_by_user")
    public String updatedByUser;
}
