package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.bukuwarung.database.entity.enums.ReminderCategory;

@Entity(tableName = "self_reminder")
public class SelfReminderEntity extends AppEntity {

    @ColumnInfo(name = "reminder_id")
    @PrimaryKey
    @NonNull
    public String reminderId;

    @ColumnInfo(name = "hour")
    public Integer hour = Integer.valueOf(0);

    @ColumnInfo(name = "minute")
    public Integer minute = Integer.valueOf(0);

    @ColumnInfo(name = "notes")
    public String notes;

    @ColumnInfo(name = "reminder_category")
    @NonNull
    public Integer reminderCategory = ReminderCategory.UTANG.ordinal();

    @ColumnInfo(name = "is_active")
    public Integer isActive = Integer.valueOf(0);

    public SelfReminderEntity(){}

    public SelfReminderEntity(Integer hour, Integer minute, String notes, ReminderCategory reminderCategory, Integer isActive, @NonNull String reminderId) {
        this.hour = hour;
        this.minute = minute;
        this.notes = notes;
        this.reminderCategory = reminderCategory.ordinal();
        this.isActive = isActive;
        this.reminderId = reminderId;
    }
}
