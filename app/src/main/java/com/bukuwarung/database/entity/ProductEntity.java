package com.bukuwarung.database.entity;

import android.os.Parcel;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.bukuwarung.constants.AppConst;

@Entity(tableName = "products")
public class ProductEntity extends AppEntity {

    //not used in phase 1
    @ColumnInfo(name = "unit_price")
    public Double unitPrice = Double.valueOf(0.0d);

    @ColumnInfo(name = "buying_price")
    public Double buyingPrice = 0.0d;

    //can be used in future for inventory management
    @ColumnInfo(name = "stock")
    public Double stock = 0.0d;

    @ColumnInfo(name = "initial_stock")
    public Double initialStock = 0.0d;

    @ColumnInfo(name = "minimum_stock")
    public Integer minimumStock = 1;

    @ColumnInfo(name = "book_id")
    @NonNull
    public String bookId;

    @ColumnInfo(name = "measurement_id")
    public String measurementId = "5";

    @ColumnInfo(name = "measurement_name")
    public String measurementName = "Pcs";

    @ColumnInfo(name = "deleted")
    public Integer deleted = Integer.valueOf(0);

    @ColumnInfo(name = "track_inventory")
    public Integer trackInventory = AppConst.INVENTORY_TRACKING_DISABLED;

    //can be used in future for product category id
    @ColumnInfo(name = "category")
    public Integer category = Integer.valueOf(0);

    @ColumnInfo(name = "product_id")
    @PrimaryKey
    @NonNull
    public String productId;

    @ColumnInfo(name = "name")
    @NonNull
    public String name;

    @ColumnInfo(name = "code")
    public String code;

    @ColumnInfo(name = "has_updated_price")
    @NonNull
    public boolean hasUpdatedPrice = false;

    @ColumnInfo(name = "favourite")
    @NonNull
    public boolean favourite = false;

    @ColumnInfo(name = "is_imported_from_catalog")
    @NonNull
    public boolean isImportedFromCatalog = false;

    @Ignore
    public int yesterdayCount = Integer.valueOf(0);

    public ProductEntity(String bookId, String productId, String name) {
        this.bookId = bookId;
        this.productId = productId;
        this.name = name;
    }

    public ProductEntity() {
    }

    protected ProductEntity(Parcel in) {
        if (in.readByte() == 0) {
            unitPrice = null;
        } else {
            unitPrice = in.readDouble();
        }
        if (in.readByte() == 0) {
            stock = null;
        } else {
            stock = in.readDouble();
        }
        bookId = in.readString();
        if (in.readByte() == 0) {
            deleted = null;
        } else {
            deleted = in.readInt();
        }
        if (in.readByte() == 0) {
            category = null;
        } else {
            category = in.readInt();
        }
        productId = in.readString();
        name = in.readString();
    }

}
