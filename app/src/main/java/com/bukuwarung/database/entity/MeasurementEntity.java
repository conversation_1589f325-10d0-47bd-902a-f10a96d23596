package com.bukuwarung.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "measurements")
public class MeasurementEntity extends AppEntity {

    @ColumnInfo(name = "measurement_id")
    @PrimaryKey
    @NonNull
    public String measurementId;

    @ColumnInfo(name = "book_id")
    @NonNull
    public String bookId;

    @ColumnInfo(name = "is_default")
    public Integer isDefault = 0;


    @ColumnInfo(name = "measurement_name")
    @NonNull
    public String measurementName;

    public MeasurementEntity(){}

    public MeasurementEntity(@NonNull String unitId, @NonNull String bookId, Integer isDefault, @NonNull String name) {
        this.measurementId = unitId;
        this.bookId = bookId;
        this.isDefault = isDefault;
        this.measurementName = name;
    }
}
