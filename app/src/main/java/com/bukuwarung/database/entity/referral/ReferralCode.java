package com.bukuwarung.database.entity.referral;

public class ReferralCode {
    public String code;
    public boolean active;
    public String deeplink;
    public String userId; //sender or receiver user id (may need to remove it, it helps in reducing quantity of db calls)
    public long earnedPoint;
    public long activatedAt;
    public long disabledAt;
    public long createdAt;
    public long updatedAt;

    public ReferralCode(String code, boolean active, String link, String userId, long earnedPoint, long activatedAt, long disabledAt, long createdAt, long updatedAt) {
        this.code = code;
        this.active = active;
        this.deeplink = link;
        this.userId = userId;
        this.earnedPoint = earnedPoint;
        this.activatedAt = activatedAt;
        this.disabledAt = disabledAt;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
}
