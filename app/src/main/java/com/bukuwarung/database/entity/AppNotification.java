package com.bukuwarung.database.entity;

public class AppNotification {

    public String bannerUrl;
    public String body;
    public String id;
    public String thumbnailUrl;
    public String title;
    public String link;
    public Boolean share=true;
    public Boolean support=false;
    public Boolean useWebview = false;
    public Boolean active;
    public Long startAt;
    public int minTrxToShow = 0;

    public AppNotification(String bannerUrl, String body, String id, String thumbnailUrl, String title, Boolean share, Boolean support, String link, Boolean active, Long startAt, Boolean useWebview,
                           int minTrxToShow) {
        this.bannerUrl = bannerUrl;
        this.body = body;
        this.id = id;
        this.thumbnailUrl = thumbnailUrl;
        this.title = title;
        this.share = share;
        this.useWebview = useWebview;
        this.support = support;
        this.link = link;
        this.active = active;
        this.startAt=startAt;
        this.minTrxToShow=minTrxToShow;
    }

    public AppNotification(){}
}
