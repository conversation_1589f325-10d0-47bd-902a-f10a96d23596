package com.bukuwarung.database.entity;

public enum TransactionEntityType {
    DEFAULT("DEFAULT"),
    PAYMENT("PAYMENT"),
    POS_TRANSACTION("POS_TRANSACTION"),
    CASH_TRANSACTION("CASH_TRANSACTION"),
    BRICK_TRANSACTION("BRICK_TRANSACTION");
    private final String text;

    TransactionEntityType(final String text) {
        this.text = text;
    }

    @Override
    public String toString() {
        return text;
    }
}
