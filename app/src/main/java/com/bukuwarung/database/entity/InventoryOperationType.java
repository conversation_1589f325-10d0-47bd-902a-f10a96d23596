package com.bukuwarung.database.entity;

public enum InventoryOperationType {
    DEFAULT("NONE"),
    SALE_TRANSACTION("SALE_TRANSACTION"),
    EXPENSE_TRANSACTION("EXPENSE_TRANSACTION"),
    ADD_STOCK("ADD_STOCK"),
    <PERSON><PERSON>OVE_STOCK("REMOVE_STOCK"),
    DISABLE_STOCK_TRACKING("DISABLE_STOCK_TRACKING"),
    ENABLE_STOCK_TRACKING("ENABLE_STOCK_TRACKING"),
    PERUBAHAN_HARGA_JUAL("PERUBAHAN_HARGA_JUAL"),
    PERUBAHAN_HARGA_BELI("PERUBAHAN_HARGA_BELI");

    private final String text;

    InventoryOperationType(final String text) {
        this.text = text;
    }

    public static InventoryOperationType of(String operationName) {
        try {
            return valueOf(operationName);
        } catch (Exception var2) {
            return DEFAULT;
        }
    }

    @Override
    public String toString() {
        return text;
    }
}
