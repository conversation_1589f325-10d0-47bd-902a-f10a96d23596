package com.bukuwarung.database.dto;

import androidx.room.ColumnInfo;

import java.util.List;

import static com.bukuwarung.constants.AppConst.BELUM_LUNAS;


public class BookSummaryModel {
    @ColumnInfo(name = "amountIn")
    public double amountIn = 0.0;
    @ColumnInfo(name = "amountOut")
    public double amountOut = 0.0;
    @ColumnInfo(name = "profit")
    public double profit = 0.0;
    @ColumnInfo(name = "customerUnpaid")
    public double customerUnpaid = 0.0;


    public BookSummaryModel(double amountIn, double amountOut) {
        this.amountIn = amountIn;
        this.amountOut = amountOut;
    }

    public BookSummaryModel(List<CashTransactionDto> transactionDtos) {
        if(transactionDtos  ==  null)  return;

        for (CashTransactionDto trx : transactionDtos) {
            if (trx == null)
                continue;
            if (trx.transactionAmount > 0) {
                this.amountIn += trx.transactionAmount;
                this.amountOut += -1 * trx.buyingPrice;
                if(trx.status != null && trx.status == BELUM_LUNAS) {
                    this.customerUnpaid += trx.transactionAmount;
                }
            } else {
                this.amountOut += trx.transactionAmount;
            }
        }

        this.profit = Math.abs(amountIn) - Math.abs(amountOut);
    }

    public BookSummaryModel() {
    }
}
