package com.bukuwarung.database.dto;


import com.bukuwarung.session.User;

import java.io.Serializable;

public class InstalledAppInfo implements Serializable {

    public String appname = "";
    public String owner = "";
    public String pname = "";
    public String versionName = "";
    public Integer versionCode = 0;
    public Long firstInstallTime = 0l;

    public InstalledAppInfo(String appname, String owner, String pname, String versionName, Integer versionCode, Long firstInstallTime) {
        this.appname = appname;
        this.owner = owner;
        this.pname = pname;
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.firstInstallTime = firstInstallTime;
    }



    public InstalledAppInfo(String appname, String pname, String versionName, int versionCode, long firstInstallTime) {
        this.appname = appname;
        this.owner = User.getUserId();
        this.pname = pname;
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.firstInstallTime = firstInstallTime;
    }

    public InstalledAppInfo(){

    }
}
