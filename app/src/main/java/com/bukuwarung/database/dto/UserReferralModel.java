package com.bukuwarung.database.dto;

import com.bukuwarung.database.entity.referral.ReferralLink;
import com.bukuwarung.database.entity.referral.UserReferral;

import java.util.List;

public class UserReferralModel {
    public String leaderboardName;
    public String referralCodeInUse;
    public String sender;
    public long points;
    public int rank;
    public boolean active;
    public long createdAt;
    public List<ReferralLink> sent;
    public List<ReferralLink> received;
    public UserReferralModel(UserReferral userReferral){
        this.leaderboardName = userReferral.leaderboardName;
        this.referralCodeInUse = userReferral.referralCodeInUse;
        this.sender = userReferral.sender;
        this.points = userReferral.points;
        this.rank = userReferral.rank;
        this.active = userReferral.active;
        this.createdAt = userReferral.createdAt;
    }
}
