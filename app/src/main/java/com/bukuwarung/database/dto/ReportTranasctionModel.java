package com.bukuwarung.database.dto;

import androidx.room.ColumnInfo;


public class ReportTranasctionModel {
    @ColumnInfo(name = "customer_id")
    public String customerId;
    @ColumnInfo(name = "transaction_id")
    public String transactionId;
    @ColumnInfo(name = "cash_category_id")
    public String cashCategoryId;
    @ColumnInfo(name = "cash_transaction_id")
    public String cashTransactionId;
    @ColumnInfo(name = "name")
    public String customerName;
    @ColumnInfo(name = "amount")
    public double amount;
    @ColumnInfo(name = "buying_price")
    public double buyingPrice=0;
    @ColumnInfo(name = "date")
    public String date;
    @ColumnInfo(name = "description")
    public String notes;

    public ReportTranasctionModel(String cstId, String transactionId, String customerName, String date, double amount, String notes) {
        this.customerId = cstId;
        this.transactionId = transactionId;
        this.customerName = customerName;
        this.date = date;
        this.amount = amount;
        this.notes = notes;
    }

    public ReportTranasctionModel(String cstId, String transactionId, String customerName, String date, double amount, double buyingPrice,String notes,String cashCategoryId,String cashTransactionId) {
        this.customerId = cstId;
        this.transactionId = transactionId;
        this.cashCategoryId = cashCategoryId;
        this.cashTransactionId = cashTransactionId;
        this.customerName = customerName;
        this.date = date;
        this.amount = amount;
        this.notes = notes;
    }

    public ReportTranasctionModel() {
    }

    public final String getTransactionId() {
        return this.transactionId;
    }

    public final String getCustomerName() {
        return this.customerName;
    }

    public final String getDate() {
        return this.date;
    }

    public final double getAmount() {
        return this.amount;
    }

    public final double getBuyingPrice() {
        return this.buyingPrice;
    }

    public final String getNotes() {
        return this.notes;
    }
}
