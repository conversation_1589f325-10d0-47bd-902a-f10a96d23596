package com.bukuwarung.database.dto;

import androidx.room.ColumnInfo;
import androidx.room.Ignore;

import java.util.List;

import static com.bukuwarung.constants.AppConst.BELUM_LUNAS;


public class FrequentProductDto {
    @ColumnInfo(name = "product_id")
    public String productId = "";
    @ColumnInfo(name = "product_count")
    public Integer productCount = 0;

    @Ignore
    public String productName = "";

    public FrequentProductDto() {
    }
}
