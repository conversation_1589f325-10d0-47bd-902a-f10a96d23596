package com.bukuwarung.database.dto;

import com.bukuwarung.database.entity.TransactionItemsEntity;
import com.bukuwarung.session.User;

import java.util.List;

public class TransactionItemListDto {

    public List<TransactionItemsEntity> itemList;
    public String createdByUser;

    public TransactionItemListDto(List<TransactionItemsEntity> itemList,String createdByUser) {
        this.itemList = itemList;
        this.createdByUser = createdByUser;
    }

    public TransactionItemListDto() {
        this.createdByUser = User.getUserId();
    }
}
