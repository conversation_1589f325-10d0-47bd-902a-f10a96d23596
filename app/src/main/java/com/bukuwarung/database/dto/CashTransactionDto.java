package com.bukuwarung.database.dto;

import androidx.room.ColumnInfo;

import com.bukuwarung.database.entity.TransactionEntityType;


public class CashTransactionDto {
    @ColumnInfo(name = "cash_category_id")
    public String cashCategoryId;
    @ColumnInfo(name = "name")
    public String categoryName;
    @ColumnInfo(name = "amount")
    public double transactionAmount;
    @ColumnInfo(name = "buying_price")
    public double buyingPrice;
    @ColumnInfo(name = "date")
    public String transactionDate;
    @ColumnInfo(name = "created_at")
    public long createdAt;
    @ColumnInfo(name = "updated_at")
    public long updatedAt;
    @ColumnInfo(name = "description")
    public String transactionDescription;
    @ColumnInfo(name = "type")
    public Integer type;
    @ColumnInfo(name = "status")
    public Integer status;
    @ColumnInfo(name = "customer_name")
    public String customerName;
    @ColumnInfo(name = "mobile_number")
    public String customerMobilePhone;
    @ColumnInfo(name = "customer_id")
    public String customerId;
    @ColumnInfo(name = "transaction_type")
    public String transactionType;
    @ColumnInfo(name = "brick_institution_id")
    public Integer brickInstitutionId;

    public Integer getBrickInstitutionId() {
        return brickInstitutionId;
    }

    public void setBrickInstitutionId(Integer brickInstitutionId) {
        this.brickInstitutionId = brickInstitutionId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCashCategoryId() {
        return cashCategoryId;
    }

    public void setCashCategoryId(String cashCategoryId) {
        this.cashCategoryId = cashCategoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String customerName) {
        this.categoryName = customerName;
    }

    public double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getTransactionDescription() {
        return transactionDescription;
    }

    public void setTransactionDescription(String transactionDescription) {
        this.transactionDescription = transactionDescription;
    }

    public String getCashTransactionid() {
        return cashTransactionid;
    }

    public void setCashTransactionid(String cashTransactionid) {
        this.cashTransactionid = cashTransactionid;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    @ColumnInfo(name = "cash_transaction_id")
    public String cashTransactionid;

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CashTransactionDto(categoryId=");
        sb.append(this.cashCategoryId);
        sb.append(", transactionId=");
        sb.append(this.cashTransactionid);
        sb.append(", category=");
        sb.append(this.categoryName);
        sb.append(", transactionDate=");
        sb.append(this.transactionDate);
        sb.append(", transactionAmount=");
        sb.append(this.transactionAmount);
        sb.append(", transactionDescription=");
        sb.append(this.transactionDescription);
        sb.append(", transactionType=");
        sb.append(this.transactionType);
        sb.append(")");
        return sb.toString();
    }

    public CashTransactionDto(String id, String cashTransactionid, String categoryName, String transactionDate, double amount, String desc) {

        this.cashCategoryId = id;
        this.cashTransactionid = cashTransactionid;
        this.categoryName = categoryName;
        this.transactionDate = transactionDate;
        this.transactionAmount = amount;
        this.transactionDescription = desc;
    }

    public CashTransactionDto(String id, String cashTransactionid, String categoryName, String transactionDate, double amount, String desc, String customerName) {

        this.cashCategoryId = id;
        this.cashTransactionid = cashTransactionid;
        this.categoryName = categoryName;
        this.transactionDate = transactionDate;
        this.transactionAmount = amount;
        this.transactionDescription = desc;
        this.customerName = customerName;
    }

    public CashTransactionDto(){}


}
