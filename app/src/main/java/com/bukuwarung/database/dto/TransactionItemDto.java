package com.bukuwarung.database.dto;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Nullable;

import com.bukuwarung.baseui.DefaultRVAdapterModel;


public class TransactionItemDto extends DefaultRVAdapterModel implements Parcelable {
    public String transactionId; //can be null for new txn
    public String productName; //cannot be null
    public String productId; //cannot be null
    public Double quantity = 0.0;
    public Double sellingPrice = 0.0;
    public Double buyingPrice = 0.0;
    public String measurementUnit = "";
    public Boolean isFavourite = false;

    public TransactionItemDto() {
    }

    // only to create dummy object
    public TransactionItemDto(String productName, Double quantity) {
        this.productName = productName;
        this.quantity = quantity;
    }

    protected TransactionItemDto(Parcel in) {
        transactionId = in.readString();
        productName = in.readString();
        productId = in.readString();
        if (in.readByte() == 0) {
            quantity = null;
        } else {
            quantity = in.readDouble();
        }
        if (in.readByte() == 0) {
            sellingPrice = null;
        } else {
            sellingPrice = in.readDouble();
        }
        if (in.readByte() == 0) {
            buyingPrice = null;
        } else {
            buyingPrice = in.readDouble();
        }
        if (in.readByte() == 0) {
            measurementUnit = null;
        } else {
            measurementUnit = in.readString();
        }
        isFavourite = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(transactionId);
        dest.writeString(productName);
        dest.writeString(productId);
        if (quantity == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(quantity);
        }
        if (sellingPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(sellingPrice);
        }
        if (buyingPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(buyingPrice);
        }
        if (measurementUnit == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeString(measurementUnit);
        }
        dest.writeByte((byte) (isFavourite ? 1 : 0));
    }

    public static final Creator<TransactionItemDto> CREATOR = new Creator<TransactionItemDto>() {
        @Override
        public TransactionItemDto createFromParcel(Parcel in) {
            return new TransactionItemDto(in);
        }

        @Override
        public TransactionItemDto[] newArray(int size) {
            return new TransactionItemDto[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public int getId() {
        if (productName == null)
            return 0;
        return productName.hashCode();
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        try {
            return obj instanceof TransactionItemDto && ((TransactionItemDto) obj).productName.equals(productName)
                    && ((TransactionItemDto) obj).quantity.equals(quantity);
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }
}
