package com.bukuwarung.locale;

import com.bukuwarung.session.SessionManager;

import java.util.ArrayList;
import java.util.List;

public final class Countries {

    private static List<Country> globalCountryList;

    public static int getDefaultCountryIndex() {
        SessionManager instance = SessionManager.getInstance();
        String countryCode = instance.getCountryCode();
        List countryList = new Countries().getCountryList();
        int size = countryList.size();
        for (int i = 0; i < size; i++) {
            if (((Country) countryList.get(i)).getCode().equals(countryCode)) {
                return i;
            }
        }
        return 0;
    }

    public static List<Country> getCountryList() {
        if (globalCountryList != null) {
            return globalCountryList;
        }
        initCountryList();
        return globalCountryList;
    }

    private static void initCountryList() {
        List<Country> arrayList = new ArrayList<>();
        arrayList.add(new Country("AFG", "Afghanistan", "+93", "AFN"));
        arrayList.add(new Country("ALA", "Åland Islands", "+358", "EUR"));
        arrayList.add(new Country("ALB", "Albania", "+355", "ALL"));
        arrayList.add(new Country("DZA", "Algeria", "+213", "DZD"));
        arrayList.add(new Country("ASM", "American Samoa", "+1", "$"));
        arrayList.add(new Country("AND", "Andorra", "+376", "EUR"));
        arrayList.add(new Country("AGO", "Angola", "+244", "AOA"));
        arrayList.add(new Country("AIA", "Anguilla", "+1", "XCD"));
        arrayList.add(new Country("ATG", "Antigua and Barbuda", "+1", "XCD"));
        arrayList.add(new Country("ARG", "Argentina", "+54", "ARS"));
        arrayList.add(new Country("ARM", "Armenia", "+374", "AMD"));
        arrayList.add(new Country("ABW", "Aruba", "+297", "AWG"));
        arrayList.add(new Country("AUS", "Australia", "+61", "AUD"));
        arrayList.add(new Country("AUT", "Austria", "+43", "EUR"));
        arrayList.add(new Country("AZE", "Azerbaijan", "+994", "AZN"));
        arrayList.add(new Country("BHS", "Bahamas", "+1", "BSD"));
        arrayList.add(new Country("BHR", "Bahrain", "+973", "BHD"));
        arrayList.add(new Country("BGD", "Bangladesh", "+880", "৳"));
        arrayList.add(new Country("BRB", "Barbados", "+1", "BBD"));
        arrayList.add(new Country("BLR", "Belarus", "+375", "BYN"));
        arrayList.add(new Country("BEL", "Belgium", "+32", "EUR"));
        arrayList.add(new Country("BLZ", "Belize", "+501", "BZD"));
        arrayList.add(new Country("BEN", "Benin", "+229", "XOF"));
        arrayList.add(new Country("BMU", "Bermuda", "+1", "BMD"));
        arrayList.add(new Country("BTN", "Bhutan", "+975", "BTN"));
        arrayList.add(new Country("BOL", "Bolivia, Plurinational State Of", "+591", "BOB"));
        arrayList.add(new Country("BIH", "Bosnia And Herzegovina", "+387", "BAM"));
        arrayList.add(new Country("BWA", "Botswana", "+267", "BWP"));
        arrayList.add(new Country("BRA", "Brazil", "+55", "BRL"));
        arrayList.add(new Country("IOT", "British Indian Ocean Territory", "+246", "$"));
        arrayList.add(new Country("VGB", "British Virgin Islands", "+1", "$"));
        arrayList.add(new Country("BRN", "Brunei Darussalam", "+673", "BND"));
        arrayList.add(new Country("BGR", "Bulgaria", "+359", "BGN"));
        arrayList.add(new Country("BFA", "Burkina Faso", "+226", "XOF"));
        arrayList.add(new Country("BDI", "Burundi", "+257", "BIF"));
        arrayList.add(new Country("KHM", "Cambodia", "+855", "KHR"));
        arrayList.add(new Country("CMR", "Cameroon", "+237", "XAF"));
        arrayList.add(new Country("CAN", "Canada", "+1", "CAD"));
        arrayList.add(new Country("CPV", "Cape Verde", "+238", "CVE"));
        arrayList.add(new Country("CYM", "Cayman Islands", "+1", "KYD"));
        arrayList.add(new Country("CAF", "Central African Republic", "+236", "XAF"));
        arrayList.add(new Country("TCD", "Chad", "+235", "XAF"));
        arrayList.add(new Country("CHL", "Chile", "+56", "CLP"));
        arrayList.add(new Country("CHN", "China", "+86", "CNY"));
        arrayList.add(new Country("CXR", "Christmas Island", "+61", "AUD"));
        arrayList.add(new Country("CCK", "Cocos (keeling) Islands", "+61", "AUD"));
        arrayList.add(new Country("COL", "Colombia", "+57", "COP"));
        arrayList.add(new Country("COM", "Comoros", "+269", "KMF"));
        arrayList.add(new Country("COG", "Congo", "+242", "XAF"));
        arrayList.add(new Country("COD", "Congo, The Democratic Republic Of The", "+243", "CDF"));
        arrayList.add(new Country("COK", "Cook Islands", "+682", "NZD"));
        arrayList.add(new Country("CRI", "Costa Rica", "+506", "CRC"));
        arrayList.add(new Country("CIV", "Côte D'ivoire", "+225", "XOF"));
        arrayList.add(new Country("HRV", "Croatia", "+385", "HRK"));
        arrayList.add(new Country("CUB", "Cuba", "+53", "CUP"));
        arrayList.add(new Country("CUW", "Curaçao", "+599", "ANG"));
        arrayList.add(new Country("CYP", "Cyprus", "+357", "EUR"));
        arrayList.add(new Country("CZE", "Czech Republic", "+420", "CZK"));
        arrayList.add(new Country("DNK", "Denmark", "+45", "DKK"));
        arrayList.add(new Country("DJI", "Djibouti", "+253", "DJF"));
        arrayList.add(new Country("DMA", "Dominica", "+1", "XCD"));
        arrayList.add(new Country("DOM", "Dominican Republic", "+1", "DOP"));
        arrayList.add(new Country("ECU", "Ecuador", "+593", "$"));
        arrayList.add(new Country("EGY", "Egypt", "+20", "EGP"));
        arrayList.add(new Country("SLV", "El Salvador", "+503", "$"));
        arrayList.add(new Country("GNQ", "Equatorial Guinea", "+240", "XAF"));
        arrayList.add(new Country("ERI", "Eritrea", "+291", "ERN"));
        arrayList.add(new Country("EST", "Estonia", "+372", "EUR"));
        arrayList.add(new Country("ETH", "Ethiopia", "+251", "ETB"));
        arrayList.add(new Country("FLK", "Falkland Islands (malvinas)", "+500", "FKP"));
        arrayList.add(new Country("FRO", "Faroe Islands", "+298", "DKK"));
        arrayList.add(new Country("FJI", "Fiji", "+679", "FJD"));
        arrayList.add(new Country("FIN", "Finland", "+358", "EUR"));
        arrayList.add(new Country("FRA", "France", "+33", "EUR"));
        arrayList.add(new Country("GUF", "French Guyana", "+594", "EUR"));
        arrayList.add(new Country("PYF", "French Polynesia", "+689", "XPF"));
        arrayList.add(new Country("GAB", "Gabon", "+241", "XAF"));
        arrayList.add(new Country("GMB", "Gambia", "+220", "GMD"));
        arrayList.add(new Country("GEO", "Georgia", "+995", "GEL"));
        arrayList.add(new Country("DEU", "Germany", "+49", "EUR"));
        arrayList.add(new Country("GHA", "Ghana", "+233", "GHS"));
        arrayList.add(new Country("GIB", "Gibraltar", "+350", "GIP"));
        arrayList.add(new Country("GRC", "Greece", "+30", "EUR"));
        arrayList.add(new Country("GRL", "Greenland", "+299", "DKK"));
        arrayList.add(new Country("GRD", "Grenada", "+1", "XCD"));
        arrayList.add(new Country("GLP", "Guadeloupe", "+590", "EUR"));
        arrayList.add(new Country("GUM", "Guam", "+1", "$"));
        arrayList.add(new Country("GTM", "Guatemala", "+502", "GTQ"));
        arrayList.add(new Country("GGY", "Guernsey", "+44", "£"));
        arrayList.add(new Country("GIN", "Guinea", "+224", "GNF"));
        arrayList.add(new Country("GNB", "Guinea-bissau", "+245", "XOF"));
        arrayList.add(new Country("GUY", "Guyana", "+592", "GYD"));
        arrayList.add(new Country("HTI", "Haiti", "+509", "HTG"));
        arrayList.add(new Country("VAT", "Holy See (vatican City State)", "+379", "EUR"));
        arrayList.add(new Country("HND", "Honduras", "+504", "HNL"));
        arrayList.add(new Country("HKG", "Hong Kong", "+852", "HKD"));
        arrayList.add(new Country("HUN", "Hungary", "+36", "HUF"));
        arrayList.add(new Country("ISL", "Iceland", "+354", "ISK"));
        arrayList.add(new Country("IND", "India", "+91", "₹"));
        arrayList.add(new Country("IDN", "Indonesia", "+62", "Rp"));
        arrayList.add(new Country("IRN", "Iran, Islamic Republic Of", "+98", "IRR"));
        arrayList.add(new Country("IRQ", "Iraq", "+964", "IQD"));
        arrayList.add(new Country("IRL", "Ireland", "+353", "EUR"));
        arrayList.add(new Country("IMN", "Isle Of Man", "+44", "£"));
        arrayList.add(new Country("ISR", "Israel", "+972", "ILS"));
        arrayList.add(new Country("ITA", "Italy", "+39", "EUR"));
        arrayList.add(new Country("JAM", "Jamaica", "+1", "JMD"));
        arrayList.add(new Country("JPN", "Japan", "+81", "JPY"));
        arrayList.add(new Country("JEY", "Jersey", "+44", "£"));
        arrayList.add(new Country("JOR", "Jordan", "+962", "JOD"));
        arrayList.add(new Country("KAZ", "Kazakhstan", "+7", "KZT"));
        arrayList.add(new Country("KEN", "Kenya", "+254", "KES"));
        arrayList.add(new Country("KIR", "Kiribati", "+686", "AUD"));
        arrayList.add(new Country("KWT", "Kuwait", "+965", "KWD"));
        arrayList.add(new Country("KGZ", "Kyrgyzstan", "+996", "KGS"));
        arrayList.add(new Country("LAO", "Lao People's Democratic Republic", "+856", "LAK"));
        arrayList.add(new Country("LVA", "Latvia", "+371", "EUR"));
        arrayList.add(new Country("LBN", "Lebanon", "+961", "LBP"));
        arrayList.add(new Country("LSO", "Lesotho", "+266", "ZAR"));
        arrayList.add(new Country("LBR", "Liberia", "+231", "LRD"));
        arrayList.add(new Country("LBY", "Libya", "+218", "LYD"));
        arrayList.add(new Country("LIE", "Liechtenstein", "+423", "CHF"));
        arrayList.add(new Country("LTU", "Lithuania", "+370", "EUR"));
        arrayList.add(new Country("LUX", "Luxembourg", "+352", "EUR"));
        arrayList.add(new Country("MAC", "Macau", "+853", "MOP"));
        arrayList.add(new Country("MKD", "Macedonia (FYROM)", "+389", "MKD"));
        arrayList.add(new Country("MDG", "Madagascar", "+261", "MGA"));
        arrayList.add(new Country("MWI", "Malawi", "+265", "MWK"));
        arrayList.add(new Country("MYS", "Malaysia", "+60", "RM"));
        arrayList.add(new Country("MDV", "Maldives", "+960", "MVR"));
        arrayList.add(new Country("MLI", "Mali", "+223", "XOF"));
        arrayList.add(new Country("MLT", "Malta", "+356", "EUR"));
        arrayList.add(new Country("MHL", "Marshall Islands", "+692", "$"));
        arrayList.add(new Country("MTQ", "Martinique", "+596", "EUR"));
        arrayList.add(new Country("MRT", "Mauritania", "+222", "MRO"));
        arrayList.add(new Country("MUS", "Mauritius", "+230", "MUR"));
        arrayList.add(new Country("MYT", "Mayotte", "+262", "EUR"));
        arrayList.add(new Country("MEX", "Mexico", "+52", "MXN"));
        arrayList.add(new Country("FSM", "Micronesia, Federated States Of", "+691", "$"));
        arrayList.add(new Country("MDA", "Moldova, Republic Of", "+373", "MDL"));
        arrayList.add(new Country("MCO", "Monaco", "+377", "EUR"));
        arrayList.add(new Country("MNG", "Mongolia", "+976", "MNT"));
        arrayList.add(new Country("MNE", "Montenegro", "+382", "EUR"));
        arrayList.add(new Country("MSR", "Montserrat", "+1", "XCD"));
        arrayList.add(new Country("MAR", "Morocco", "+212", "MAD"));
        arrayList.add(new Country("MOZ", "Mozambique", "+258", "MZN"));
        arrayList.add(new Country("MMR", "Myanmar", "+95", "MMK"));
        arrayList.add(new Country("NAM", "Namibia", "+264", "NAD"));
        arrayList.add(new Country("NRU", "Nauru", "+674", "AUD"));
        arrayList.add(new Country("NPL", "Nepal", "+977", "रू"));
        arrayList.add(new Country("NLD", "Netherlands", "+31", "EUR"));
        arrayList.add(new Country("NCL", "New Caledonia", "+687", "XPF"));
        arrayList.add(new Country("NZL", "New Zealand", "+64", "NZD"));
        arrayList.add(new Country("NIC", "Nicaragua", "+505", "NIO"));
        arrayList.add(new Country("NER", "Niger", "+227", "XOF"));
        arrayList.add(new Country("NGA", "Nigeria", "+234", "NGN"));
        arrayList.add(new Country("NIU", "Niue", "+683", "NZD"));
        arrayList.add(new Country("NFK", "Norfolk Islands", "+672", "AUD"));
        arrayList.add(new Country("PRK", "North Korea", "+850", "KPW"));
        arrayList.add(new Country("MNP", "Northern Mariana Islands", "+1", "$"));
        arrayList.add(new Country("NOR", "Norway", "+47", "NOK"));
        arrayList.add(new Country("OMN", "Oman", "+968", "OMR"));
        arrayList.add(new Country("PAK", "Pakistan", "+92", "₨"));
        arrayList.add(new Country("PLW", "Palau", "+680", "$"));
        arrayList.add(new Country("PSE", "Palestine", "+970", "ILS"));
        arrayList.add(new Country("PAN", "Panama", "+507", "PAB"));
        arrayList.add(new Country("PNG", "Papua New Guinea", "+675", "PGK"));
        arrayList.add(new Country("PRY", "Paraguay", "+595", "PYG"));
        arrayList.add(new Country("PER", "Peru", "+51", "PEN"));
        arrayList.add(new Country("PHL", "Philippines", "+63", "PHP"));
        arrayList.add(new Country("PCN", "Pitcairn Islands", "+870", "NZD"));
        arrayList.add(new Country("POL", "Poland", "+48", "PLN"));
        arrayList.add(new Country("PRT", "Portugal", "+351", "EUR"));
        arrayList.add(new Country("PRI", "Puerto Rico", "+1", "$"));
        arrayList.add(new Country("QAT", "Qatar", "+974", "QAR"));
        arrayList.add(new Country("REU", "Réunion", "+262", "EUR"));
        arrayList.add(new Country("ROU", "Romania", "+40", "RON"));
        arrayList.add(new Country("RUS", "Russian Federation", "+7", "RUB"));
        arrayList.add(new Country("RWA", "Rwanda", "+250", "RWF"));
        arrayList.add(new Country("BLM", "Saint Barthélemy", "+590", "EUR"));
        arrayList.add(new Country("SHN", "Saint Helena, Ascension And Tristan Da Cunha", "+290", "SHP"));
        arrayList.add(new Country("KNA", "Saint Kitts and Nevis", "+1", "XCD"));
        arrayList.add(new Country("LCA", "Saint Lucia", "+1", "XCD"));
        arrayList.add(new Country("MAF", "Saint Martin", "+590", "EUR"));
        arrayList.add(new Country("SPM", "Saint Pierre And Miquelon", "+508", "EUR"));
        arrayList.add(new Country("VCT", "Saint Vincent & The Grenadines", "+1", "XCD"));
        arrayList.add(new Country("WSM", "Samoa", "+685", "WST"));
        arrayList.add(new Country("SMR", "San Marino", "+378", "EUR"));
        arrayList.add(new Country("STP", "Sao Tome And Principe", "+239", "STD"));
        arrayList.add(new Country("SAU", "Saudi Arabia", "+966", "SAR"));
        arrayList.add(new Country("SEN", "Senegal", "+221", "XOF"));
        arrayList.add(new Country("SRB", "Serbia", "+381", "RSD"));
        arrayList.add(new Country("SYC", "Seychelles", "+248", "SCR"));
        arrayList.add(new Country("SLE", "Sierra Leone", "+232", "SLL"));
        arrayList.add(new Country("SGP", "Singapore", "+65", "SGD"));
        arrayList.add(new Country("SXM", "Sint Maarten", "+1", "ANG"));
        arrayList.add(new Country("SVK", "Slovakia", "+421", "EUR"));
        arrayList.add(new Country("SVN", "Slovenia", "+386", "EUR"));
        arrayList.add(new Country("SLB", "Solomon Islands", "+677", "SBD"));
        arrayList.add(new Country("SOM", "Somalia", "+252", "SOS"));
        arrayList.add(new Country("ZAF", "South Africa", "+27", "ZAR"));
        arrayList.add(new Country("KOR", "South Korea", "+82", "KRW"));
        arrayList.add(new Country("SSD", "South Sudan", "+211", "SSP"));
        arrayList.add(new Country("ESP", "Spain", "+34", "EUR"));
        arrayList.add(new Country("LKA", "Sri Lanka", "+94", "LKR"));
        arrayList.add(new Country("SDN", "Sudan", "+249", "SDG"));
        arrayList.add(new Country("SUR", "Suriname", "+597", "SRD"));
        arrayList.add(new Country("SWZ", "Swaziland", "+268", "SZL"));
        arrayList.add(new Country("SWE", "Sweden", "+46", "SEK"));
        arrayList.add(new Country("CHE", "Switzerland", "+41", "CHF"));
        arrayList.add(new Country("SYR", "Syrian Arab Republic", "+963", "SYP"));
        arrayList.add(new Country("TWN", "Taiwan", "+886", "TWD"));
        arrayList.add(new Country("TJK", "Tajikistan", "+992", "TJS"));
        arrayList.add(new Country("TZA", "Tanzania, United Republic Of", "+255", "TZS"));
        arrayList.add(new Country("THA", "Thailand", "+66", "THB"));
        arrayList.add(new Country("TLS", "Timor-leste", "+670", "$"));
        arrayList.add(new Country("TGO", "Togo", "+228", "XOF"));
        arrayList.add(new Country("TKL", "Tokelau", "+690", "NZD"));
        arrayList.add(new Country("TON", "Tonga", "+676", "TOP"));
        arrayList.add(new Country("TTO", "Trinidad & Tobago", "+1", "TTD"));
        arrayList.add(new Country("TUN", "Tunisia", "+216", "TND"));
        arrayList.add(new Country("TUR", "Turkey", "+90", "TRY"));
        arrayList.add(new Country("TKM", "Turkmenistan", "+993", "TMT"));
        arrayList.add(new Country("TCA", "Turks and Caicos Islands", "+1", "$"));
        arrayList.add(new Country("TUV", "Tuvalu", "+688", "AUD"));
        arrayList.add(new Country("UGA", "Uganda", "+256", "UGX"));
        arrayList.add(new Country("UKR", "Ukraine", "+380", "UAH"));
        arrayList.add(new Country("ARE", "United Arab Emirates (UAE)", "+971", "AED"));
        arrayList.add(new Country("GBR", "United Kingdom", "+44", "£"));
        arrayList.add(new Country("USA", "United States (USA)", "+1", "$"));
        arrayList.add(new Country("URY", "Uruguay", "+598", "UYU"));
        arrayList.add(new Country("VIR", "US Virgin Islands", "+1", "$"));
        arrayList.add(new Country("UZB", "Uzbekistan", "+998", "UZS"));
        arrayList.add(new Country("VUT", "Vanuatu", "+678", "VUV"));
        arrayList.add(new Country("VEN", "Venezuela, Bolivarian Republic Of", "+58", "VEF"));
        arrayList.add(new Country("VNM", "Vietnam", "+84", "VND"));
        arrayList.add(new Country("WLF", "Wallis And Futuna", "+681", "XPF"));
        arrayList.add(new Country("YEM", "Yemen", "+967", "YER"));
        arrayList.add(new Country("ZMB", "Zambia", "+260", "ZMW"));
        arrayList.add(new Country("ZWE", "Zimbabwe", "+263", "$"));
        globalCountryList = arrayList;
    }
}
