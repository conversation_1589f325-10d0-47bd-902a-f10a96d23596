package com.bukuwarung.session;

import static com.bukuwarung.session.User.DEF_USER_STR;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.PrefrenceConstant;
import com.bukuwarung.database.entity.BankAccount;
import com.bukuwarung.enums.Language;
import com.bukuwarung.payments.pref.PaymentPrefManager;
import com.bukuwarung.preference.SyncManager;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.Utility;
import com.google.gson.Gson;

import java.util.Calendar;

public class SessionManager {
    private static final String DAILY_DUE_DATE_LAST_RUN = "DAILY_DUE_DATE_LAST_RUN";
    private static final String DAILY_DUE_DATE_BROADCAST_LAST_RUN = "DAILY_DUE_DATE_BROADCAST_LAST_RUN";
    private static final String HAS_CREATED_CASH_TRANSACTION_RECORD = "HAS_CREATED_CASH_TRANSACTION_RECORD";
    private static final String HAS_CREATED_UTANG_RECORD = "HAS_CREATED_UTANG_RECORD";
    private static final String CURRENT_CHOSEN_BANK_ACCOUNT = "CURRENT_CHOSEN_BANK_ACCOUNT";
    private static final String LOGIN_METHOD = "LOGIN_METHOD";
    private static final String FIRST_LAUNCH = "FIRST_LAUNCH";
    private static final String HAS_FB_DATA = "HAS_FB_DATA";

    private static final String KYC_ACCOUNT_ID = "KYC_ACCOUNT_ID";
    private static final String HAS_EDIT_CARD = "HAS_EDIT_CARD";

    private static final String UUID = "UUID";
    private static final String APP_INSTANCE_ID = "app_instance_id";
    private final String HAS_SEEN_PPOB_PULSA = "HAS_SEEN_PPOB_PULSA";
    private final String HAS_SEEN_PPOB_LISTRIK = "HAS_SEEN_PPOB_LISTRIK";
    private final String HAS_SEEN_PPOB_PULSA_COUNT = "HAS_SEEN_PPOB_PULSA_COUNT";
    private final String HAS_SEEN_PPOB_LISTRIK_COUNT = "HAS_SEEN_PPOB_LISTRIK_COUNT";
    private final String LOYALTY_TIER_NAME = "loyalty_tier_name";
    private final String IS_BNPL_REGISTERED = "is_bnpl_registered";
    private final String BNPL_LIMIT = "bnpl_limit";
    private final String IS_BNPL_USER_WHITELISTED = "IS_BNPL_USER_WHITELISTED";
    private final String GAME_RULE_NAME = "game_rule_name";

    private final String PAYMENT_ACCOUNT_ID ="payment_account_id";
    private static final String SHOULD_RETRY_POST_FCM_TOKEN = "should_retry_post_fcm_token";
    private static final String FCM_DEVICE_ID = "fcm_device_id";
    private static final String LAST_STORED_FCM_TOKEN = "last_stored_fcm_token";

    private static SessionManager sessionManager;
    private Context context;
    private Editor editor;
    private Editor syncEditor;
    public SharedPreferences sessionPref;
    public SharedPreferences syncPref;

    private boolean tokenRefreshOnProgress = false;
    private boolean hasTokenRefreshFailed = false;
    private boolean isDeeplinkManagerStarted = false;

    public SessionManager(Context context) {
        this.context = context;
        this.sessionPref = context.getSharedPreferences("BookBookPref", PrefrenceConstant.PRIVATE_MODE);;
        this.syncPref = context.getSharedPreferences("BukuWarungSyncPrefs", PrefrenceConstant.PRIVATE_MODE);
        this.editor = this.sessionPref.edit();
        this.syncEditor = this.syncPref.edit();
    }

    public static SessionManager getInstance() {
        if (sessionManager == null) {
            sessionManager = new SessionManager(Application.getAppContext());
        }
        return sessionManager;
    }

    public static SessionManager getInstance(Context ctx) {
        if (sessionManager == null) {
            sessionManager = new SessionManager(ctx);
        }
        return sessionManager;
    }

    public int getAppLanguage() {
        return this.sessionPref.getInt("APP_LANG", Language.DEFAULT.getLangCd());
    }

    public void setAppLanguage(int i) {
        this.editor.putInt("APP_LANG", i);
        this.editor.apply();
    }

    public String getBukuwarungToken() {
        return this.syncPref.getString("BUKUWARUNG_TOKEN", null);
    }

    public void setBukuwarungToken(String str) {
        PaymentPrefManager.Companion.getInstance().setKycTierFromToken(str);
        this.syncEditor.putString("BUKUWARUNG_TOKEN", str);
        this.syncEditor.apply();
    }

    public Boolean getUserLoggedInWithPin() {
        return this.syncPref.getBoolean("IS_USER_LOGGED_IN_WITH_PIN", false);
    }

    public void setUserLoggedInWithPin(Boolean isUserLoggedInWithPin) {
        this.syncEditor.putBoolean("IS_USER_LOGGED_IN_WITH_PIN", isUserLoggedInWithPin);
        this.syncEditor.apply();
    }

    public Boolean getUserLoggedInWithPinAtleastOnce() {
        return this.syncPref.getBoolean("IS_USER_LOGGED_IN_WITH_PIN_ONCE", false);
    }

    public void setUserLoggedInWithPinAtleastOnce(Boolean isUserLoggedInWithPinOnce) {
        this.syncEditor.putBoolean("IS_USER_LOGGED_IN_WITH_PIN_ONCE", isUserLoggedInWithPinOnce);
        this.syncEditor.apply();
    }

    public boolean isRefreshingToken() {
        return tokenRefreshOnProgress;
    }

    public void setRefreshingToken(boolean flag) {
        this.tokenRefreshOnProgress = flag;
    }

    public boolean hasExistingBusiness() {
        return this.syncPref.getBoolean("temp_existing_business", true);
    }

    public void setExistingBusinessFlag(boolean flag) {
        this.syncEditor.putBoolean("temp_existing_business", flag);
        this.syncEditor.apply();
    }

    public boolean isExistingOldUser() {
        return this.syncPref.getBoolean("is_existing_app_user", true);
    }

    public void setIsExistingOldUser(boolean flag) {
        this.syncEditor.putBoolean("is_existing_app_user", flag);
        this.syncEditor.apply();
    }

    public boolean hasMigratedBusiness() {
        return this.syncPref.getBoolean("has_migrated_business", false);
    }

    public void hasMigratedBusiness(boolean flag) {
        this.syncEditor.putBoolean("has_migrated_business", flag);
        this.syncEditor.apply();
    }

    public String getUserIdForExistingBusinessCheck() {
        return this.syncPref.getString("EXISTING_BUSINESS_CHECK_USER", null);
    }

    public String getSessionValueByKey(String id) {
        return this.syncPref.getString(id, "");
    }

    public void setUserIdForExistingBusinessCheck(String str) {
        this.syncEditor.putString("EXISTING_BUSINESS_CHECK_USER", str);
        this.syncEditor.apply();
    }

    public boolean hasTokenRefreshFailed() {
        return hasTokenRefreshFailed;
    }

    public void setRefreshingTokenFails(boolean flag) {
        this.hasTokenRefreshFailed = flag;
    }

    public String getSessionToken() {
        return this.syncPref.getString("SESSION_TOKEN", null);
    }

    public void setBureauSessionId(String str) {
        this.syncEditor.putString("BUREAU_SESSION_ID", str);
        this.syncEditor.apply();
    }

    public String getBureauSessionId() {
        return this.syncPref.getString("BUREAU_SESSION_ID", "");
    }

    public void setSessionToken(String str) {
        this.syncEditor.putString("SESSION_TOKEN", str);
        this.syncEditor.apply();
    }

    public String getOpToken() {
        return this.syncPref.getString("OP_TOKEN", null);
    }

    public void setOpToken(String str) {
        this.syncEditor.putString("OP_TOKEN", str);
        this.syncEditor.apply();
    }

    public Boolean getIsPinVerified() {
        return this.syncPref.getBoolean("pin_change_verified", false);
    }

    public void setIsPinVerified(Boolean flag) {
        this.syncEditor.putBoolean("pin_change_verified", flag);
        this.syncEditor.apply();
    }

    public long getSessionStart() {
        return this.syncPref.getLong("SESSION_TIME", System.currentTimeMillis());
    }

    public void setSessionStart() {
        this.syncEditor.putLong("SESSION_TIME", System.currentTimeMillis());
        this.syncEditor.apply();
    }

    public String getUserId() {
        if (!isLoggedIn()) {
            return DEF_USER_STR;
        }

        String userId = this.syncPref.getString("USER_ID", null);
        if(Utility.isBlank(userId)){
            userId = Utility.getUserIdFromToken();
            setUserId(userId);
        }
        return userId;
    }

    public void setUserId(String str) {
        this.syncEditor.putString("USER_ID", str);
        this.syncEditor.apply();
    }

    public String getAdvertisingId() {
        return this.syncPref.getString("ADVERTISING_ID", "");
    }

    public void setAdvertisingId(String str) {
        this.syncEditor.putString("ADVERTISING_ID", str);
        this.syncEditor.apply();
    }

    public String getAndroidId() {
        return this.syncPref.getString("ANDROID_ID", "");
    }

    public void setAndroidId(String str) {
        this.syncEditor.putString("ANDROID_ID", str);
        this.syncEditor.apply();
    }

    public String getImeiNumber() {
        return this.syncPref.getString("IMEI_NUMBER", "");
    }

    public void setImeiNumber(String str) {
        this.syncEditor.putString("IMEI_NUMBER", str);
        this.syncEditor.apply();
    }

    public Boolean isGuestUser() {
        return this.syncPref.getBoolean("GUEST_USER", false);
    }

    public void isGuestUser(Boolean isGuest) {
        this.syncEditor.putBoolean("GUEST_USER", isGuest);
        this.syncEditor.apply();
    }

    public Boolean hasCheckedAdId() {
        return this.syncPref.getBoolean("HAS_CONFIRMED_ADID", false);
    }

    public void hasCheckedAdId(Boolean isGuest) {
        this.syncEditor.putBoolean("HAS_CONFIRMED_ADID", isGuest);
        this.syncEditor.apply();
    }

    public Boolean trackGuestLoginSuccess() {
        return this.syncPref.getBoolean("TRACK_GUEST_LOGIN_SUCCESS", false);
    }

    public void trackGuestLoginSuccess(Boolean isGuest) {
        this.syncEditor.putBoolean("TRACK_GUEST_LOGIN_SUCCESS", isGuest);
        this.syncEditor.apply();
    }

    public boolean contactListSynched() {
        return false;
    }

    public void setContactListSynched(boolean flg) {
        this.syncEditor.putBoolean("CONTACT_SYNCHED", flg);
        this.syncEditor.apply();
    }

    public String getCountryCode() {
        return this.sessionPref.getString("COUNTRY_CD", "+62");
    }

    public void setCountryCode(String str) {
        this.editor.putString("COUNTRY_CD", str);
        this.editor.apply();
    }

    public String getDeviceId() {
        return this.syncPref.getString("DEVICE_ID", null);
    }

    public void setDeviceId(String str) {
        this.syncEditor.putString("DEVICE_ID", str);
        this.syncEditor.apply();
    }

    public String getDeviceGUID() {
        return this.syncPref.getString("DEVICE_GUID", null);
    }

    public void setDeviceGUID(String str) {
        this.syncEditor.putString("DEVICE_GUID", str);
        this.syncEditor.apply();
    }

    public String getBusinessId() {
        if (!isLoggedIn()) {
            return "0000011111";
        }
        if(isGuestUser()){
            return this.syncPref.getString("GUEST_BOOK_ID", null);
        }
        return this.syncPref.getString("BOOK_ID", null);
    }

    public String getGuestBusinessId() {
        return this.syncPref.getString("GUEST_BOOK_ID", null);
    }

    public void setBusinessId(String str) {
        if(isGuestUser()){
            this.syncEditor.putString("GUEST_BOOK_ID", str);
            this.syncEditor.apply();
        }else {
            this.syncEditor.putString("BOOK_ID", str);
            this.syncEditor.apply();
        }
        AppAnalytics.setUserBusinessAttr();
    }

    public void setSelectedBookName(String str) {
        this.syncEditor.putString("SELECTED_BOOK_NAME", str);
        this.syncEditor.apply();
    }

    public String getSelectedBookName() {
        if (!isLoggedIn()) {
            return "0000011111";
        }
        return this.syncPref.getString("SELECTED_BOOK_NAME", null);
    }

    public void setAppState(int i) {
        this.editor.putInt("APP_STATE", i);
        this.editor.apply();
    }

    public void setFbDeeplinkData(boolean data) {
        this.editor.putBoolean(HAS_FB_DATA, data);
        this.editor.apply();
    }

    public boolean hasFbDeeplinkData() {
        return this.sessionPref.getBoolean(HAS_FB_DATA, false);
    }

    public int getAppState() {
        return this.sessionPref.getInt("APP_STATE", 0);
    }

    public boolean isLoggedIn() {
        return this.getBukuwarungToken() != null || isGuestUser();
    }
    public boolean isFirstLaunchComplete() {
        return this.syncPref.getBoolean(FIRST_LAUNCH, false);
    }

    public void setFirstLaunchComplete(boolean status) {
         syncPref.edit().putBoolean(FIRST_LAUNCH, status).apply();
    }

    public void logOutInstance() {
        this.tokenRefreshOnProgress = false;
        this.hasTokenRefreshFailed = false;
        //MoEHelper.getInstance(Application.getAppContext()).logoutUser();
    }

    public void forceLogout() {
        SessionManager.getInstance().clearAll();
        SyncManager.getInstance().clearAll();
        SetupManager.getInstance().clearAll();
    }

    public int getTryCount() {
        return this.sessionPref.getInt("retry_count", 1);
    }

    public void setTryCount(int count) {
        this.editor.putInt("retry_count", count);
        this.editor.apply();
    }

    public int getInvalidOtpCount() {
        return this.sessionPref.getInt("otp_count", 1);
    }

    public void setInvalidOtpCount(int count) {
        this.editor.putInt("otp_count", count);
        this.editor.apply();
    }

    public void setUseNewLogin(boolean flg) {
        this.editor.putBoolean("login_new", flg);
        this.editor.apply();
    }

    public boolean isMigrated() {
        return this.sessionPref.getBoolean("migrated", false);
    }

    public void setMigrated(boolean flg) {
        this.editor.putBoolean("migrated", flg);
        this.editor.apply();
    }

    public boolean isCreateOrForgotPasswordInitiated() {
        return this.sessionPref.getBoolean("createOrForgotPasswordInitiated", false);
    }

    public void setCreateOrForgotPasswordInitiated(boolean flg) {
        this.editor.putBoolean("createOrForgotPasswordInitiated", flg);
        this.editor.apply();
    }

    public int getCstSeq() {
        return this.sessionPref.getInt("shop_cst_seq", 1);
    }

    public void setCstSeq(int count) {
        this.editor.putInt("shop_cst_seq", count);
        this.editor.apply();
    }

    public int getTransSeq() {
        return this.sessionPref.getInt("cst_trans_seq", 1);
    }

    public void setTransSeq(int count) {
        this.editor.putInt("cst_trans_seq", count);
        this.editor.apply();
    }

    public String getOTPApi() {
        return this.sessionPref.getString("otp_api", "https://4xnzt2xbn7.execute-api.ap-southeast-1.amazonaws.com/dev/auth");
    }

    public void setOTPApi(String apiUrl) {
        this.editor.putString("otp_api", apiUrl);
        this.editor.apply();
    }

    public void setPrevTab(int i) {
        this.editor.putInt("PREV_TAB", i);
        this.editor.apply();
    }

    public int getPrevTab() {
        return this.sessionPref.getInt("PREV_TAB", 0);
    }

    public void enablePinLock() {
        this.editor.putBoolean(PIN_LOCK_FLAG, true);
        this.editor.apply();
    }

    public void disablePinLock() {
        this.editor.putBoolean(PIN_LOCK_FLAG, false);
        this.editor.apply();
    }

    public boolean pinlockEnabled() {
        return this.sessionPref.getBoolean(PIN_LOCK_FLAG, false);
    }

    private static final String PIN_LOCK_FLAG = "PIN_LOCK";

    public boolean hasRegisteredSessionDevice() {
        return this.sessionPref.getBoolean("HAS_REGISTERED_DEVICe", false);
    }

    public void setRegisteredSessionDevice(boolean flg) {
        this.editor.putBoolean("HAS_REGISTERED_DEVICe", flg);
        this.editor.apply();
    }

    public boolean hasClosedCalendarConfetti() {
        return this.sessionPref.getBoolean("HAS_CLOSED_CALENDAR_CONFETTI", false);
    }

    public void setHasClosedCalendarConfetti(boolean flg) {
        this.editor.putBoolean("HAS_CLOSED_CALENDAR_CONFETTI", flg);
        this.editor.apply();
    }

    public boolean hasCreatedCashTransactionRecord() {
        return this.sessionPref.getBoolean(HAS_CREATED_CASH_TRANSACTION_RECORD, false);
    }

    public void setHasCreatedCashTransactionRecord(boolean flg) {
        this.editor.putBoolean(HAS_CREATED_CASH_TRANSACTION_RECORD, flg);
        this.editor.apply();
    }

    public boolean hasCreatedUtangRecord() {
        return this.sessionPref.getBoolean(HAS_CREATED_UTANG_RECORD, false);
    }

    public void setHasCreatedUtangRecord(boolean flg) {
        this.editor.putBoolean(HAS_CREATED_UTANG_RECORD, flg);
        this.editor.apply();
    }

    public void setHasEditCard(boolean flag) {
        this.editor.putBoolean(HAS_EDIT_CARD, flag);
        this.editor.apply();
    }

    public boolean hasEditCard() {
        return this.sessionPref.getBoolean(HAS_EDIT_CARD, false);
    }

    // TODO: just temporary solution because its hard to send activity result from nav component to normal activity. will remove after both pages are refactored
    public void setCurrentBankAccount(BankAccount bankAccount) {
        String json = null;
        if (bankAccount != null) {
            json = new Gson().toJson(bankAccount);
        }
        this.editor.putString(CURRENT_CHOSEN_BANK_ACCOUNT, json);
        this.editor.apply();
    }

    public BankAccount getCurrentBankAccount() {
        String json = sessionPref.getString(CURRENT_CHOSEN_BANK_ACCOUNT, null);
        if (json != null) {
            try {
                return new Gson().fromJson(json, BankAccount.class);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public long getDailyDueDateLastRun() {
        return sessionPref.getLong(DAILY_DUE_DATE_LAST_RUN, -1);
    }

    public void setDailyDueDateLastRun() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 0);
        calendar.add(Calendar.MINUTE, 0);
        calendar.add(Calendar.SECOND, 0);
        editor.putLong(DAILY_DUE_DATE_LAST_RUN, calendar.getTimeInMillis());
        editor.apply();
    }

    public long getDailyBroadcastRun() {
        return sessionPref.getLong(DAILY_DUE_DATE_BROADCAST_LAST_RUN, -1);
    }

    public void setLoginMethod(String method) {
        editor.putString(LOGIN_METHOD, method);
        editor.apply();
    }

    public String getLoginMethod() {
        return sessionPref.getString(LOGIN_METHOD, null);
    }

    public void setKycAccountId(String kycAccountId) {
        editor.putString(KYC_ACCOUNT_ID, kycAccountId);
        editor.apply();
    }

    public String getKycAccountId() {
        return sessionPref.getString(KYC_ACCOUNT_ID, null);
    }

    public void setDailyBroadcastRun() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 0);
        calendar.add(Calendar.MINUTE, 0);
        calendar.add(Calendar.SECOND, 0);
        editor.putLong(DAILY_DUE_DATE_BROADCAST_LAST_RUN, calendar.getTimeInMillis());
        editor.apply();
    }

    public void clearAll() {
        editor.clear();
        editor.apply();
        syncEditor.clear();
        syncEditor.apply();
    }

    public boolean isReloggedInUser() {
        String userId = syncPref.getString("USER_ID", null);
        if (userId != null && !User.DEF_USER_STR.equals(userId)) {
            return true;
        }
        return false;
    }

    public void setHasAgreedTnC(boolean hasAgreedTnC) {
        syncEditor.putBoolean("HAS_AGREED_TNC", hasAgreedTnC);
        syncEditor.apply();
    }

    public void setUUID(String auth_token) {
        editor.putString(UUID, auth_token);
        editor.apply();
    }

    public String getUUID() {
        return sessionPref.getString(UUID, null);
    }

    public boolean getHasAgreedTnc() {
        return syncPref.getBoolean("HAS_AGREED_TNC", false);
    }

    public void setAppInstanceId(String appInstanceId) {
        editor.putString(APP_INSTANCE_ID, appInstanceId);
        editor.apply();
    }

    public String getAppInstanceId() {
        return sessionPref.getString(APP_INSTANCE_ID, " ");
    }

    public void setPpobPulsaBottomSheetSeen() {
        editor.putBoolean(HAS_SEEN_PPOB_PULSA, true);
        editor.apply();
    }

    public void setPpobListrikBottomSheetSeen() {
        editor.putBoolean(HAS_SEEN_PPOB_LISTRIK, true);
        editor.apply();
    }

    public boolean hasPpobPulsaSeen() {
        return sessionPref.getBoolean(HAS_SEEN_PPOB_PULSA, false);
    }

    public boolean hasPpobListrikSeen() {
        return sessionPref.getBoolean(HAS_SEEN_PPOB_LISTRIK, false);
    }

    public void setPpobPulsaBottomSheetSeenCount() {
        editor.putInt(HAS_SEEN_PPOB_PULSA_COUNT, hasPpobPulsaSeenCount() + 1);
        editor.apply();
    }

    public void setPpobListrikBottomSheetSeenCount() {
        editor.putInt(HAS_SEEN_PPOB_LISTRIK_COUNT, hasPpobListrikSeenCount() + 1);
        editor.apply();
    }

    public int hasPpobPulsaSeenCount() {
        return sessionPref.getInt(HAS_SEEN_PPOB_PULSA_COUNT, 0);
    }

    public int hasPpobListrikSeenCount() {
        return sessionPref.getInt(HAS_SEEN_PPOB_LISTRIK_COUNT, 0);
    }

    public void setBnplInfo(boolean isRegistered, double bnplLimit) {
        editor.putBoolean(IS_BNPL_REGISTERED, isRegistered);
        editor.putLong(BNPL_LIMIT, (long)bnplLimit);
        editor.apply();
    }

    public boolean isBnplRegistered() {
        return sessionPref.getBoolean(IS_BNPL_REGISTERED, false);
    }

    public double getBnplLimit() {
        return sessionPref.getLong(BNPL_LIMIT, 0);
    }

    public void setBnplUserWhiteListed(int isWhitelisted) {
        editor.putInt(IS_BNPL_USER_WHITELISTED, isWhitelisted);
        editor.apply();
    }

    public int isBnplUserWhiteListed() {
        return sessionPref.getInt(IS_BNPL_USER_WHITELISTED, -1);
    }

    public void hasRefreshedBnplData(boolean refreshed) {
        editor.putBoolean("HAS_REFRESHED_BNPL_DATA", refreshed);
        editor.apply();
    }

    public boolean hasRefreshedBnplData() {
        return sessionPref.getBoolean("HAS_REFRESHED_BNPL_DATA", false);
    }

    public void setLoyaltyTier(String tierName) {
        editor.putString(LOYALTY_TIER_NAME, tierName);
        editor.apply();
    }

    public String getLoyaltyTierName() {
        return sessionPref.getString(LOYALTY_TIER_NAME, null);
    }

    public void setGameRuleName(String gameRuleName) {
        editor.putString(GAME_RULE_NAME, gameRuleName);
        editor.apply();
    }

    public String getGameRuleName() {
        return sessionPref.getString(GAME_RULE_NAME, null);
    }

    public String getPaymentAccountId(){
        return sessionPref.getString(PAYMENT_ACCOUNT_ID,"");
    }
    public void setPaymentAccountId(String paymentAccountId){
        editor.putString(PAYMENT_ACCOUNT_ID,paymentAccountId);
        editor.apply();
    }

    public void setShouldRetryPostFcmToken(boolean value) {
        this.editor.putBoolean(SHOULD_RETRY_POST_FCM_TOKEN, value);
        this.editor.apply();
    }

    public boolean getShouldRetryPostFcmToken() {
        return this.sessionPref.getBoolean(SHOULD_RETRY_POST_FCM_TOKEN, true);
    }

    public String getFcmDeviceId() {
        String deviceId = this.sessionPref.getString(FCM_DEVICE_ID, null);
        if (deviceId == null) {
            deviceId = AppIdGenerator.resourceUUID();
            editor.putString(FCM_DEVICE_ID, deviceId);
            editor.apply();
        }
        return deviceId;
    }

    public String getLastStoredFcmToken() {
        return this.sessionPref.getString(LAST_STORED_FCM_TOKEN, null);
    }

    public void setLastStoredFcmToken(String value) {
        this.editor.putString(LAST_STORED_FCM_TOKEN, value);
        this.editor.apply();
    }
 }
