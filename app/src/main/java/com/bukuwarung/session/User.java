package com.bukuwarung.session;

public final class User implements UserUniqueIds {

        public static String DEF_USER_STR = "0000011111";

        public static String getUserId() {

            SessionManager sessionManager = SessionManager.getInstance();

            if (!sessionManager.isLoggedIn()) {
                return DEF_USER_STR;
            }

            String userId = sessionManager.getInstance().getUserId();

            return userId;
        }

    @Override
    public String userId() {
        return User.getUserId();
    }

    @Override
    public String deviceId() {
        return User.getDeviceId();
    }

    @Override
    public String businessId() {
        return User.getBusinessId();
    }

    public static String getDeviceId() {
        SessionManager sessionManager = SessionManager.getInstance();

        if (!sessionManager.isLoggedIn()) {
            return "0000011111";
        }

        return sessionManager.getInstance().getDeviceId();
    }

    public static String getBusinessId() {
        SessionManager sessionManager = SessionManager.getInstance();

        if (!sessionManager.isLoggedIn()) {
            return "0000011111";
        }

        return sessionManager.getInstance().getBusinessId();
    }

    public static boolean hasAgreedTnC() {
        return SessionManager.getInstance().getHasAgreedTnc();
    }
}
