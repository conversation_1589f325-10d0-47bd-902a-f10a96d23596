package com.bukuwarung.session;

import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;

import com.android.volley.DefaultRetryPolicy;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.auth0.android.jwt.Claim;
import com.auth0.android.jwt.JWT;
import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.BukuWarungKeys;
import com.bukuwarung.activities.maintainance.MaintenanceActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.AppBookSyncManager;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.tutor.utils.Constants;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.DeviceUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.bureau.devicefingerprint.BureauAPI;
import com.bureau.devicefingerprint.models.ErrorResponse;
import com.bureau.devicefingerprint.models.SubmitResponse;
import com.bureau.devicefingerprint.tools.DataCallback;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

public class AuthHelper {

    public static final long SESSION_TIME = 30 * 60 * 1000;
    private static long lastRefreshAttempt = 0;


    public static void newSession() {

        long currentTime = System.currentTimeMillis();
        if (Utility.isBlank(SessionManager.getInstance().getSessionToken()) || Utility.isBlank(SessionManager.getInstance().getBukuwarungToken()) || (currentTime - lastRefreshAttempt) < 3000)
            return;
        if (SessionManager.getInstance().isRefreshingToken()) return;

        lastRefreshAttempt = currentTime;

        RequestQueue queue = Volley.newRequestQueue(Application.getAppContext());
        Map<String, String> params = new HashMap<>();
        params.put("token", SessionManager.getInstance().getSessionToken());
        params.put("userId", User.getUserId());
        params.put("clientSecret", BukuWarungKeys.INSTANCE.getClientSecret());
        params.put("client", BukuWarungKeys.INSTANCE.getClientId());
        params.put("deviceBrand", Build.MANUFACTURER);
        params.put("register", "false");
        params.put("deviceId", SessionManager.getInstance().getDeviceGUID());
        params.put("deviceModel", Build.MODEL);
        params.put("androidId", DeviceUtils.INSTANCE.getAndroidId());
        params.put("imeiNumber", DeviceUtils.INSTANCE.getImeiNumber());
        params.put("wideVineId", DeviceUtils.INSTANCE.getWideVineId());
        params.put("advertisingId", SessionManager.getInstance().getAdvertisingId());

        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put("androidId", DeviceUtils.INSTANCE.getAndroidId());
        prop.put("imeiNumber", DeviceUtils.INSTANCE.getImeiNumber());
        prop.put("wideVineId", DeviceUtils.INSTANCE.getWideVineId());
        prop.put("advertisingId", SessionManager.getInstance().getAdvertisingId());
        if (!AppConst.PRODUCTION_FLAVOR.equals(BuildConfig.FLAVOR)) {
            AppAnalytics.trackEvent("device_fingerprint_bacon", prop);
        }

        final String URL = AppConfigManager.getInstance().getOTPApi() + "/api/v2/auth/users/bacon";
        SessionManager.getInstance().setRefreshingToken(true);
        JsonObjectRequest req = new JsonObjectRequest(URL, new JSONObject(params),
                response -> {
                    long refreshTimeDiff = (System.currentTimeMillis() - SessionManager.getInstance().getSessionStart());
                    try {
                        String idToken = response.getString("idToken");
                        if (!Utility.isBlank(idToken)) {
                            SessionManager.getInstance().setBukuwarungToken(idToken);
                            SessionManager.getInstance().setSessionToken(response.getString("refreshToken"));
                            JWT parsedJWT = new JWT(idToken);
                            Map<String, Claim> claim = parsedJWT.getClaims();
                            if (claim.get("user_uuid") != null) {
                                SessionManager.getInstance().setUUID(claim.get("user_uuid").asString());
                                AppAnalytics.setUserAttr();
                            }
                            SessionManager.getInstance().setSessionStart();
                            if(AppBookSyncManager.getInstance().getMigrationStatus() != Constants.MIGRATION_SUCCESS){
                                AccountingMigrationHelper.Companion.initialDataMigration(Application.getAppContext());
                            }
                        }
                    } catch (Exception e) {
                        FirebaseCrashlytics.getInstance().recordException(e);
                    }
                    SessionManager.getInstance().setRefreshingToken(false);
                }, error -> {
                    SessionManager.getInstance().setRefreshingToken(false);
                    FirebaseCrashlytics.getInstance().recordException(error);
                    try {
                        if (error != null && error.networkResponse != null && error.networkResponse.statusCode == AppConst.NETWORK_CODE_301) {
                            Intent intent = new Intent(Application.getAppContext(), MaintenanceActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            Application.getAppContext().startActivity(intent);
                        } else if (error != null && error.networkResponse != null && (error.networkResponse.statusCode == AppConst.NETWORK_CODE_403 || error.networkResponse.statusCode == AppConst.NETWORK_CODE_400)) {
                            Utilities.INSTANCE.clearDataAndLogout();
                        }
                    }catch (Exception e){
                        FirebaseCrashlytics.getInstance().recordException(e);
                    }
        }) {
            @Override
            public Map<String, String> getHeaders() {
                return getRequestHeaders();
            }
        };
        req.setRetryPolicy(new DefaultRetryPolicy(0, -1, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));
        queue.add(req);
    }

    public static void refreshUserSession() {
        //refresh Id token if older than 30min
        long tokenDelta = (System.currentTimeMillis() - SessionManager.getInstance().getSessionStart());
        boolean tokenExpired = tokenDelta > AuthHelper.SESSION_TIME;
        if (tokenExpired) {
            newSession();
        }
    }

    public static boolean isValidSessionOperation() {
        if (SessionManager.getInstance().isGuestUser() && TransactionRepository.getInstance(Application.getAppContext()).getGuestTransactionCount() >= AppConfigManager.getInstance().getGuestTxnLimit()) {
            return false;
        }
        return true;
    }

    public static void callBureau(String eventName, String entryPoint, String sessionToken, String uuid) {

        String sessionId = AppIdGenerator.resourceUUID();

        // Send bureau session id to amplitude
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put(AnalyticsConst.BW_SESSION_ID, sessionId);
        prop.put(AnalyticsConst.ENTRY_POINT2, entryPoint);
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_BW_SEND_SESSION_ID, prop);


        if (!Utility.isBlank(uuid)) {
            BureauAPI.INSTANCE.setUserId(uuid);
            BureauAPI.INSTANCE.submit(new DataCallback() {
                @Override
                public void onResult(@NonNull SubmitResponse submitResponse) {
                    String eventId = submitResponse.getEventId();
                    Log.d("Bureau Session id", eventId);
                    SessionManager.getInstance().setBureauSessionId(eventId);
                    if (!Utility.isBlank(eventName)) callAuthEvent(eventName, sessionId, sessionToken, uuid);

                    // Bureau success amplitude event
                    AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                    prop.put(AnalyticsConst.BW_SESSION_ID, sessionId);
                    prop.put(AnalyticsConst.BUREAU_RESPONSE, AnalyticsConst.SUCCESSFUL);
                    prop.put(AnalyticsConst.ENTRY_POINT2, entryPoint);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_BUREAU_SESSION_ID_ACKNOWLEDGEMENT, prop);
                    AppAnalytics.setUserProperty(AnalyticsConst.BW_SESSION_ID, sessionId);
                }

                @Override
                public void onError(@NonNull ErrorResponse errorResponse) {
                    // Bureau error amplitude event
                    AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                    prop.put(AnalyticsConst.BW_SESSION_ID, sessionId);
                    prop.put(AnalyticsConst.ENTRY_POINT2, entryPoint);
                    prop.put(AnalyticsConst.BUREAU_RESPONSE, AnalyticsConst.FAILED);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_BUREAU_SESSION_ID_ACKNOWLEDGEMENT, prop);
                }
            });
        }
    }

    public static void callAuthEvent(String eventName, String sessionId, String sessionToken, String uuid) {
        if(Utility.isBlank(SessionManager.getInstance().getBureauSessionId()))
            return;
        final String URL = BuildConfig.API_BASE_URL + "/api/v1/auth/event/track";

        RequestQueue queue = Volley.newRequestQueue(Application.getAppContext());
        Map<String, String> params = new HashMap<>();
        params.put("eventType", eventName);
        params.put("userId", uuid);
        params.put("deviceModel", Build.MODEL);
        params.put("androidVersion", String.valueOf(Build.VERSION.SDK_INT));

        // Event status tracking on amplitude
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put(AnalyticsConst.BW_SESSION_ID, sessionId);
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FRONTEND_SEND_EVENT_API, prop);

        JsonObjectRequest req = new JsonObjectRequest(URL, new JSONObject(params),
                response -> {
                    prop.put(AnalyticsConst.BACKEND_RESPONSE, AnalyticsConst.STATUS_SUCCESS);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_BACKEND_SEND_API_ACKNOWLEDGEMENT, prop);
                }, error -> {
                    prop.put(AnalyticsConst.BACKEND_RESPONSE, AnalyticsConst.FAILED);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_BACKEND_SEND_API_ACKNOWLEDGEMENT, prop);
//                    FirebaseCrashlytics.getInstance().recordException(error);
        }) {
            @Override
            public Map<String, String> getHeaders() {
                Map<String, String> params = new HashMap<>();
                params.put("Content-Type", "application/json; charset=UTF-8");
                params.put("Authorization", "Bearer " + sessionToken);
                params.put("X-APP-VERSION-CODE", String.valueOf(BuildConfig.VERSION_CODE));
                params.put("X-APP-VERSION-NAME", BuildConfig.VERSION_NAME);
                params.put("x-session-id", SessionManager.getInstance().getBureauSessionId());
                return params;
            }
        };

        req.setRetryPolicy(new DefaultRetryPolicy(0, -1, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));
        queue.add(req);
    }

    public static void refreshMaintenanceState() {
        String appMaintenance = RemoteConfigUtils.INSTANCE.getAppMaintenanceData();
        Gson gson = new GsonBuilder().create();
        Type jsonType =  new TypeToken<AppMaintenanceData>(){}.getType();
        AppMaintenanceData appMaintenanceData = gson.fromJson(appMaintenance, jsonType);
        AppConfigManager.getInstance().isAppUnderMaintenance(appMaintenanceData.getShowAppMaintenance());
    }

    public static Map<String, String> getRequestHeaders() {
        Map<String, String> params = new HashMap<>();
        params.put("Content-Type", "application/json; charset=UTF-8");
        params.put("Authorization", "Bearer " + SessionManager.getInstance().getBukuwarungToken());
        params.put("X-APP-VERSION-NAME", BuildConfig.VERSION_NAME);
        params.put("buku-origin", "bukuwarung-app");
        params.put("X-CLIENT-NAME", "BW-ANDROID");
        params.put("X-APP-VERSION-CODE", String.valueOf(BuildConfig.VERSION_CODE));
        params.put("X-TIMEZONE", TimeZone.getDefault().getID());
        params.put("x-session-id", SessionManager.getInstance().getBureauSessionId());
        return params;
    }
}
