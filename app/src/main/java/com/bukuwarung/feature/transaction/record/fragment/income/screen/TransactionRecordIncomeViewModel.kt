package com.bukuwarung.feature.transaction.record.fragment.income.screen

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.remote.api.NetworkMonitor
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.customer.api.CustomerRepository
import com.bukuwarung.data.image.api.ImageRepository
import com.bukuwarung.data.product.api.ProductRepository
import com.bukuwarung.data.transaction.api.TransactionRepository
import com.bukuwarung.data.transaction.api.model.*
import com.bukuwarung.data.transaction.implementation.mapper.toCashTransactionEntity
import com.bukuwarung.data.user.api.UserRepository
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.domain.cash.CashDto
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.feature.transaction.record.screen.SelectedTransactionCategory
import com.bukuwarung.feature.transaction.record.screen.TransactionRecord
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getFormattedId
import com.bukuwarung.utils.isNotNullOrEmpty
import dagger.Lazy
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class TransactionRecordIncomeViewModel @Inject constructor(
    stateManager: StateManager<TransactionRecordIncomeState>,
    private val lector: Lector,
    private val networkMonitor: NetworkMonitor,
    private val customerRepository: CustomerRepository,
    private val imageRepository: ImageRepository,
    private val productRepository: ProductRepository,
    private val transactionRepository: TransactionRepository,
    private val userRepository: UserRepository,
    private val cashUseCase: Lazy<CashUseCase>,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<TransactionRecordIncomeState>(stateManager) {
    fun fetchCashTransaction(record: TransactionRecord) {
        val cashTransactionId = record.cashTransactionId
        val cashCategoryId = record.cashCategoryId

        viewModelScope.launch {
            val transaction = retrieveCashTransaction(cashTransactionId)
            if (transaction != null) {
                val selectedCategories = retrieveCategories().map { category ->
                    SelectedTransactionCategory(category, isSelected = false)
                }
                val cashCategory = retrieveCashCategory(cashCategoryId = transaction.cashCategoryId)
                val selectedProducts = retrieveTransactionProducts(cashTransactionId = transaction.id)
                val incomeBalance = retrieveIncomeBalance(transaction)
                val capitalBalance = retrieveCapitalBalance(transaction)
                val customer = retrieveCustomer(customerId = transaction.customerId)
                val isImageShown = isImageShown()

                produce { state ->
                    state.copy(
                        isEdit = true,
                        cashTransactionId = cashTransactionId,
                        selectedCategories = selectedCategories,
                        selectedProducts = selectedProducts,
                        incomeBalance = incomeBalance,
                        overwriteIncomeExpression = incomeBalance > 0.0,
                        capitalBalance = capitalBalance,
                        overwriteCapitalExpression = capitalBalance > 0.0,
                        date = transaction.date,
                        paymentStatus = transaction.paymentStatus,
                        isExtraInfoExpanded = true,
                        imageUrl = transaction.image,
                        isImageShown = isImageShown,
                        note = transaction.note,
                        customer = customer,
                    )
                }

                selectCategory(name = cashCategory?.name, reorder = true)
                setAutoFocusBalanceForm()
            } else {
                val selectedCategories = retrieveCategories().map { category ->
                    SelectedTransactionCategory(category, isSelected = false)
                }
                val cashCategory = retrieveCashCategory(cashCategoryId)
                val isImageShown = isImageShown()

                produce { state ->
                    state.copy(
                        selectedCategories = selectedCategories,
                        isImageShown = isImageShown,
                    )
                }

                selectCategory(name = cashCategory?.name)
                setAutoFocusBalanceForm()
            }
        }
    }

    private suspend fun retrieveCashTransaction(id: String?): CashTransaction? {
        id ?: return null
        return transactionRepository.retrieveCashTransactionById(id)
    }

    private suspend fun retrieveCategories(): List<TransactionCategory> {
        val response = transactionRepository.fetchIncomeCategories()
        if (response !is Response.Success) return listOf()

        return response.data
    }

    private suspend fun retrieveCashCategory(cashCategoryId: String?): CashCategory? {
        cashCategoryId ?: return null
        return transactionRepository.retrieveCashCategory(cashCategoryId)
    }

    private suspend fun retrieveTransactionProducts(cashTransactionId: String?): List<TransactionProduct> {
        cashTransactionId ?: return listOf()
        return transactionRepository.retrieveTransactionProducts(cashTransactionId)
    }

    private suspend fun retrieveIncomeBalance(cashTransaction: CashTransaction): Double {
        val category = retrieveCashCategory(cashCategoryId = cashTransaction.cashCategoryId)
        if (category?.type == 1) return cashTransaction.balance

        val selectedProducts = retrieveTransactionProducts(cashTransactionId = cashTransaction.id)
        return selectedProducts.sumOf { (product, quantity) ->
            (product.unitPrice ?: 0.0) * quantity
        }
    }

    private suspend fun retrieveCapitalBalance(cashTransaction: CashTransaction): Double {
        val category = retrieveCashCategory(cashCategoryId = cashTransaction.cashCategoryId)
        if (category?.type == 1) return cashTransaction.capitalBalance

        val selectedProducts = retrieveTransactionProducts(cashTransactionId = cashTransaction.id)
        return selectedProducts.sumOf { (product, quantity) ->
            (product.capitalPrice ?: 0.0) * quantity
        }
    }

    private suspend fun retrieveCustomer(customerId: String?): Contact? {
        customerId ?: return null
        return customerRepository.retrieveContact(customerId)
    }

    private suspend fun isImageShown(): Boolean {
        return transactionRepository.fetchShowImageTransaction()
    }

    fun selectCategory(name: String?, reorder: Boolean = false) {
        produce { state ->
            val selectedCategories = buildSelectedCategories(
                selectedCategories = state.selectedCategories,
                categoryName = name,
                reorder = reorder,
            )
            val selectedCategory = (selectedCategories.find {
                it.category.name.lowercase() == name?.lowercase()
            } ?: selectedCategories.firstOrNull())?.category

            when (selectedCategory?.id?.lowercase()) {
                "penjualan" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = true,
                    isCapitalBalanceShown = true,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "pendapatan" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = true,
                    isCapitalBalanceShown = true,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "jasa" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = true,
                    isCapitalBalanceShown = true,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "penambahan" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isCapitalBalanceShown = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "lending" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isCapitalBalanceShown = false,
                    isPaymentStatusEnabled = false,
                    paymentStatus = PaymentStatus.NotPaid,
                    isCustomerRequired = true,
                )
                "piutang" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isCapitalBalanceShown = false,
                    isPaymentStatusEnabled = false,
                    paymentStatus = PaymentStatus.Paid,
                    isCustomerRequired = true,
                )
                "pendapatandiluarusaha" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isCapitalBalanceShown = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                else -> state
            }
        }
    }

    private fun buildSelectedCategories(
        selectedCategories: List<SelectedTransactionCategory>?,
        categoryName: String?,
        reorder: Boolean,
    ): List<SelectedTransactionCategory> {
        selectedCategories ?: return listOf()

        val target = categoryName ?: selectedCategories.firstOrNull()?.category?.name
        val selectedIndex = selectedCategories.indexOfFirst { (category, _) ->
            category.name.lowercase() == target?.lowercase()
        }.takeIf { it != -1 } ?: 0

        val reorderedCategories = mutableListOf<SelectedTransactionCategory>()
        if (!reorder) {
            for (index in selectedCategories.indices) {
                reorderedCategories.add(
                    SelectedTransactionCategory(
                        category = selectedCategories[index].category,
                        isSelected = index == selectedIndex,
                    )
                )
            }
        } else {
            reorderedCategories.add(
                SelectedTransactionCategory(
                    category = selectedCategories[selectedIndex].category,
                    isSelected = true,
                )
            )

            for (index in selectedCategories.indices) {
                if (index == selectedIndex) continue

                reorderedCategories.add(
                    SelectedTransactionCategory(
                        category = selectedCategories[index].category,
                        isSelected = false,
                    )
                )
            }
        }
        return reorderedCategories
    }

    fun setAutoFocusBalanceForm() {
        produce { state ->
            val autoFocusBalanceForm = "primary".takeIf {
                state.isAddProductEnabled && state.selectedProducts.isNotEmpty()
            } ?: "main"

            state.copy(
                autoFocusBalanceForm = autoFocusBalanceForm,
                overwriteIncomeExpression = true,
                overwriteCapitalExpression = true,
            )
        }
    }

    fun clearAutoFocusBalanceForm() {
        produce { state ->
            state.copy(autoFocusBalanceForm = null)
        }
    }

    fun clearReorderCategories() {
        produce { state ->
            state.copy(reorderCategories = false)
        }
    }

    fun toggleSelectedProductsExpansion() {
        produce { state ->
            state.copy(isSelectedProductsExpanded = !state.isSelectedProductsExpanded)
        }
    }

    fun updateIncomeBalance(balance: Double) {
        produce { state ->
            state.copy(incomeBalance = balance)
        }
    }

    fun updateCapitalBalance(balance: Double) {
        produce { state ->
            state.copy(capitalBalance = balance)
        }
    }

    fun updateDate(date: Date) {
        produce { state ->
            state.copy(date = date)
        }
    }

    fun updatePaymentStatus(paid: Boolean) {
        produce { state ->
            state.copy(
                paymentStatus = PaymentStatus.Paid.takeIf { paid } ?: PaymentStatus.NotPaid,
                customerErrorMessage = "",
            )
        }
    }

    fun updateExtraInfoExpansion(isExpanded: Boolean) {
        produce { state ->
            state.copy(isExtraInfoExpanded = isExpanded)
        }
    }

    fun updateImage(uri: Uri?) {
        uri?.let(::uploadImage)

        produce { state ->
            state.copy(imageUrl = uri?.toString())
        }
    }

    private fun uploadImage(uri: Uri) {
        viewModelScope.launch {
            val userId = userRepository.readUserId().takeIf(String::isNotBlank) ?: "user_upload"
            val path = "ocr_demo/$userId/${Utility.uuid()}"
            val imageUrl = imageRepository.uploadImage(uri, path)

            produce { state ->
                state.copy(imageUrl = imageUrl.takeIf(String::isNotBlank) ?: state.imageUrl)
            }
        }
    }

    fun updateNote(note: String) {
        produce { state ->
            state.copy(note = note)
        }
    }

    fun updateCustomer(info: Pair<Contact?, String>) {
        val (customer, source) = info
        produce { state ->
            state.copy(
                customer = customer,
                contactSource = source,
                customerErrorMessage = "",
            )
        }
    }

    fun updateSelectedProducts(selectedProductIds: List<Pair<String, Int>>) {
        viewModelScope.launch {
            val selectedProducts = selectedProductIds.map { (id, quantity) ->
                val product = productRepository.retrieveProductById(
                    productId = id
                ) ?: return@map null
                TransactionProduct(product, quantity)
            }.filterNotNull()

            val incomeBalance = selectedProducts.sumOf { (product, quantity) ->
                (product.unitPrice ?: 0.0) * quantity
            }
            val capitalBalance = selectedProducts.sumOf { (product, quantity) ->
                (product.capitalPrice ?: 0.0) * quantity
            }

            produce { state ->
                state.copy(
                    selectedProducts = selectedProducts,
                    incomeBalance = incomeBalance,
                    overwriteIncomeExpression = true,
                    capitalBalance = capitalBalance,
                    overwriteCapitalExpression = true,
                )
            }

            setAutoFocusBalanceForm()
        }
    }

    fun removeOverwriteIncomeExpression() {
        produce { state ->
            state.copy(overwriteIncomeExpression = false)
        }
    }

    fun removeOverwriteCapitalExpression() {
        produce { state ->
            state.copy(overwriteCapitalExpression = false)
        }
    }

    fun clearErrorMessage() {
        produce { state ->
            state.copy(errorMessage = "")
        }
    }

    // TODO: Refactor implementation to Aero architecture to support testing
    fun saveTransaction(
        isFromDailyBusiness: Boolean,
        isEdit: Boolean,
        cashTransactionId: String?,
        category: TransactionCategory?,
        isAddProductEnabled: Boolean,
        selectedProducts: List<TransactionProduct>,
        incomeBalance: Double,
        capitalBalance: Double,
        date: Date,
        paymentStatus: PaymentStatus,
        isExtraInfoExpanded: Boolean,
        imageUrl: String?,
        note: String,
        customer: Contact?,
        contactSource: String,
        isCustomerRequired: Boolean,
    ) {
        if (checkIfCustomerFormInvalid(paymentStatus, customer, isCustomerRequired)) return
        if (checkIfNetworkDisconnected()) return

        viewModelScope.launch {
            var newCashTransactionId = cashTransactionId

            val userId = userRepository.readUserId()
            val incomeCategory = Category().apply {
                categoryId = category?.id
                category_name_en = category?.name
                category_name_id = category?.name
                category_type = 1
            }
            val status = 1.takeIf { paymentStatus == PaymentStatus.Paid } ?: 0
            val strDate = Utility.getStorableDateString(date)
            val products = selectedProducts.map {
                TransactionItemDto().apply {
                    transactionId = cashTransactionId
                    productId = it.product.productId
                    productName = it.product.name
                    quantity = it.quantity.toDouble()
                    sellingPrice = it.product.unitPrice
                    buyingPrice = it.product.capitalPrice
                    measurementUnit = it.product.unit
                }
            }.takeIf { isAddProductEnabled }.orEmpty()
            val totalSellingPrice = products.sumOf { it.sellingPrice * it.quantity }
            val totalBuyingPrice = products.sumOf { it.buyingPrice * it.quantity }
            val equality = when {
                incomeBalance == totalSellingPrice -> "equals"
                incomeBalance > totalSellingPrice -> "greater"
                else -> "less"
            }
            val recordDebt = 1.takeIf {
                isCustomerRequired || incomeCategory.getIdString() == "penambahan" && status == 0
            } ?: 0

            if (!isEdit) {
                Utility.trackTransactionCount()

                val data = CashDto(
                    category = incomeCategory,
                    contact = customer,
                    status = status,
                    transactionDate = strDate,
                    amount = incomeBalance,
                    buyingPrice = capitalBalance,
                    note = note,
                    currentCashTransaction = null,
                    transactingProducts = products,
                )
                newCashTransactionId = cashUseCase.get().createCashTransaction(
                    dto = data,
                    categorySelectedStatus = recordDebt,
                    attachment = imageUrl,
                    trxType = 1,
                )

                val isFirstTransaction =
                    cashUseCase.get().getCashTrxCountForCurrentBusinessId() <= 1
                val isFirstTransactionOnCategory = cashUseCase.get()
                    .getCashCategoryTrxCountForCurrentBusinessId(incomeCategory.getFormattedId()) <= 1

                val isDefaultExpenseCategory = transactionRepository.fetchIsDefaultExpenseCategory()
                val isCategoryMandatory = transactionRepository.fetchIsCategoryMandatory()

                recordAnalytics(
                    event = "transaction_added",
                    parameter = mapOf(
                        "added_by" to userId,
                        "date" to Date(),
                        "feature" to "transaksi",
                        "amount" to incomeBalance,
                        "buying_modal" to capitalBalance,
                        "type" to ("credit".takeIf { incomeBalance - capitalBalance > 0 } ?: "debit"),
                    ),
                )
                recordAnalytics(
                    event = ("create_new_cash_transaction".takeIf {
                        isFirstTransaction || isFirstTransactionOnCategory
                    } ?: "customer_detail_tap_saved_cash"),
                    parameter = mapOf(
                        "buying_price" to capitalBalance,
                        "entry_point" to ("BW_story".takeIf { isFromDailyBusiness } ?: "non_BW_story"),
                        "product_count" to products.size,
                        "transaction_id" to newCashTransactionId,
                        "category_filled" to (category?.name?.takeIf { it != "Penjualan" } ?: "none"),
                        "trx_category_source" to "category_page",
                        "default_category" to "Penjualan",
                        "default_category_choosen" to (category?.name == "Penjualan"),
                        "default_category_expenses" to isDefaultExpenseCategory,
                        "mandatory_category_enabled" to isCategoryMandatory,
                        "customer_filled" to customer?.name.orEmpty().isNotEmpty(),
                        "customer_source" to contactSource,
                        "total_penjualan" to incomeBalance,
                        "send_sms_reminder" to false,
                        "collapse_menu_open" to isExtraInfoExpanded,
                        "description_filled" to note.isNotEmpty(),
                        "cashTransaction_type" to "credit",
                        "sum_of_item_subtotal" to totalSellingPrice,
                        "cashTransaction_amount" to "$incomeBalance",
                        "sum_product_buying_price" to totalBuyingPrice,
                        "total_penjualan_vs_item_subtotal" to equality,
                        "fully_paid" to (status == 1),
                        "UI_form" to "new_UI_post_june2022",
                        "nota_standard_enabled" to RemoteConfigUtils.shouldShowNewPosInvoice()
                    ),
                )
            } else {
                val cashTransaction = retrieveCashTransaction(newCashTransactionId)

                val data = CashDto(
                    category = incomeCategory,
                    contact = customer,
                    status = status,
                    transactionDate = strDate,
                    amount = incomeBalance,
                    buyingPrice = capitalBalance,
                    note = note,
                    currentCashTransaction = cashTransaction?.toCashTransactionEntity(),
                    transactingProducts = products,
                )
                cashUseCase.get().updateCashTransaction(
                    dto = data,
                    categorySelectedStatus = recordDebt,
                    attachment = imageUrl,
                    trxType = 1,
                )

                recordAnalytics(
                    event = "transaction_updated",
                    parameter = mapOf(
                        "added_by" to userId,
                        "date" to Date(),
                        "feature" to "transaksi",
                        "amount" to incomeBalance,
                        "buying_modal" to capitalBalance,
                        "type" to ("credit".takeIf { incomeBalance - capitalBalance > 0 } ?: "debit"),
                    ),
                )
                recordAnalytics(
                    event = "update_cash_transaction",
                    parameter = mapOf(
                        "buying_price" to capitalBalance,
                        "cashTransaction_amount" to "$incomeBalance",
                        "cashTransaction_type" to "credit",
                        "category_filled" to (category?.name?.takeIf { it != "Penjualan" } ?: "none"),
                        "collapse_menu_open" to isExtraInfoExpanded,
                        "customer_filled" to customer?.name.orEmpty().isNotEmpty(),
                        "description_filled" to note.isNotEmpty(),
                        "action_by" to ("payments".takeIf { cashTransaction?.orderId.isNotNullOrEmpty() } ?: "accounting"),
                        "fully_paid" to (status == 1),
                        "transaction_id" to newCashTransactionId.orEmpty(),
                        "update_type" to "edit",
                        "customer_id" to customer?.customerId.orEmpty(),
                        "order_id" to cashTransaction?.orderId.orEmpty(),
                    ),
                )
            }

            postCashTransaction(newCashTransactionId)

            produce { state ->
                state.copy(
                    cashTransactionId = newCashTransactionId,
                    isRecordCompleted = true,
                )
            }
        }
    }

    private fun postCashTransaction(id: String?) {
        viewModelScope.launch {
            val cashTransaction = retrieveCashTransaction(id) ?: return@launch
            transactionRepository.postCashTransaction(cashTransaction)
            transactionRepository.saveLastTransactionType(type = 1)
        }
    }

    private fun checkIfCustomerFormInvalid(
        paymentStatus: PaymentStatus,
        customer: Contact?,
        isCustomerRequired: Boolean,
    ): Boolean {
        if ((paymentStatus == PaymentStatus.NotPaid || isCustomerRequired) && customer == null) {
            produce { state ->
                state.copy(
                    customerErrorMessage = lector.literate(resId = R.string.add_contact_message),
                )
            }
            return true
        }
        return false
    }

    private fun checkIfNetworkDisconnected(): Boolean {
        if (!networkMonitor.isConnected) {
            produce { state ->
                state.copy(errorMessage = lector.literate(resId = R.string.no_internet_error))
            }
            return true
        }
        return false
    }

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }
}
