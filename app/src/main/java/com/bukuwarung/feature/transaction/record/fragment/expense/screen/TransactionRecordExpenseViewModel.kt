package com.bukuwarung.feature.transaction.record.fragment.expense.screen

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.remote.api.NetworkMonitor
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.customer.api.CustomerRepository
import com.bukuwarung.data.image.api.ImageRepository
import com.bukuwarung.data.product.api.ProductRepository
import com.bukuwarung.data.transaction.api.TransactionRepository
import com.bukuwarung.data.transaction.api.model.CashCategory
import com.bukuwarung.data.transaction.api.model.CashTransaction
import com.bukuwarung.data.transaction.api.model.PaymentStatus
import com.bukuwarung.data.transaction.api.model.TransactionCategory
import com.bukuwarung.data.transaction.api.model.TransactionProduct
import com.bukuwarung.data.transaction.implementation.mapper.toCashTransactionEntity
import com.bukuwarung.data.user.api.UserRepository
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.domain.cash.CashDto
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.feature.transaction.record.screen.SelectedTransactionCategory
import com.bukuwarung.feature.transaction.record.screen.TransactionRecord
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getFormattedId
import com.bukuwarung.utils.isNotNullOrEmpty
import dagger.Lazy
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject
import kotlin.math.abs

@HiltViewModel
class TransactionRecordExpenseViewModel @Inject constructor(
    stateManager: StateManager<TransactionRecordExpenseState>,
    private val lector: Lector,
    private val networkMonitor: NetworkMonitor,
    private val customerRepository: CustomerRepository,
    private val imageRepository: ImageRepository,
    private val productRepository: ProductRepository,
    private val transactionRepository: TransactionRepository,
    private val userRepository: UserRepository,
    private val cashUseCase: Lazy<CashUseCase>,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<TransactionRecordExpenseState>(stateManager) {
    fun fetchCashTransaction(record: TransactionRecord) {
        val cashTransactionId = record.cashTransactionId
        val cashCategoryId = record.cashCategoryId

        viewModelScope.launch {
            val transaction = retrieveCashTransaction(cashTransactionId)
            if (transaction != null) {
                val selectedCategories = retrieveCategories().map { category ->
                    SelectedTransactionCategory(category, isSelected = false)
                }
                val cashCategory = retrieveCashCategory(cashCategoryId = transaction.cashCategoryId)
                val selectedProducts = retrieveTransactionProducts(cashTransactionId = transaction.id)
                val expenseBalance = retrieveExpenseBalance(transaction)
                val customer = retrieveCustomer(customerId = transaction.customerId)
                val isImageShown = isImageShown()

                produce { state ->
                    state.copy(
                        isEdit = true,
                        cashTransactionId = cashTransactionId,
                        selectedCategories = selectedCategories,
                        selectedProducts = selectedProducts,
                        expenseBalance = expenseBalance,
                        overwriteExpenseExpression = expenseBalance > 0.0,
                        date = transaction.date,
                        paymentStatus = transaction.paymentStatus,
                        isExtraInfoExpanded = true,
                        imageUrl = transaction.image,
                        isImageShown = isImageShown,
                        note = transaction.note,
                        customer = customer,
                    )
                }

                selectCategory(name = cashCategory?.name, reorder = true)
                setAutoFocusBalanceForm()
            } else {
                val selectedCategories = retrieveCategories().map { category ->
                    SelectedTransactionCategory(category, isSelected = false)
                }
                val cashCategory = retrieveCashCategory(cashCategoryId)
                val isImageShown = isImageShown()

                produce { state ->
                    state.copy(
                        selectedCategories = selectedCategories,
                        isImageShown = isImageShown,
                    )
                }

                selectCategory(name = cashCategory?.name)
                setAutoFocusBalanceForm()
            }
        }
    }

    private suspend fun retrieveCashTransaction(id: String?): CashTransaction? {
        id ?: return null
        return transactionRepository.retrieveCashTransactionById(id)
    }

    private suspend fun retrieveCategories(): List<TransactionCategory> {
        val response = transactionRepository.fetchExpenseCategories()
        if (response !is Response.Success) return listOf()

        return response.data
    }

    private suspend fun retrieveCashCategory(cashCategoryId: String?): CashCategory? {
        cashCategoryId ?: return null
        return transactionRepository.retrieveCashCategory(cashCategoryId)
    }

    private suspend fun retrieveTransactionProducts(cashTransactionId: String?): List<TransactionProduct> {
        cashTransactionId ?: return listOf()
        return transactionRepository.retrieveTransactionProducts(cashTransactionId)
    }

    private suspend fun retrieveExpenseBalance(cashTransaction: CashTransaction): Double {
        val category = retrieveCashCategory(cashCategoryId = cashTransaction.cashCategoryId)
        if (category?.type == -1) return abs(cashTransaction.balance)

        val selectedProducts = retrieveTransactionProducts(cashTransactionId = cashTransaction.id)
        return selectedProducts.sumOf { (product, quantity) ->
            (product.capitalPrice ?: 0.0) * quantity
        }
    }

    private suspend fun retrieveCustomer(customerId: String?): Contact? {
        customerId ?: return null
        return customerRepository.retrieveContact(customerId)
    }

    private suspend fun isImageShown(): Boolean {
        return transactionRepository.fetchShowImageTransaction()
    }

    fun selectCategory(name: String?, reorder: Boolean = false) {
        produce { state ->
            val selectedCategories = buildSelectedCategories(
                selectedCategories = state.selectedCategories,
                categoryName = name,
                reorder = reorder,
            )
            val selectedCategory = (selectedCategories.find {
                it.category.name.lowercase() == name?.lowercase()
            } ?: selectedCategories.firstOrNull())?.category

            when (selectedCategory?.id?.lowercase()) {
                "stok" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = true,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "baku" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = true,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "biayaoperasional" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "lain" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "pembarayan" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = false,
                    paymentStatus = PaymentStatus.Paid,
                    isCustomerRequired = true,
                )
                "pemberian" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = false,
                    paymentStatus = PaymentStatus.NotPaid,
                    isCustomerRequired = true,
                )
                "pengeluarandiluarusaha" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                "gajikaryawan" -> state.copy(
                    selectedCategories = selectedCategories,
                    selectedCategory = selectedCategory,
                    reorderCategories = reorder,
                    isAddProductEnabled = false,
                    isPaymentStatusEnabled = true,
                    isCustomerRequired = false,
                )
                else -> state
            }
        }
    }

    private fun buildSelectedCategories(
        selectedCategories: List<SelectedTransactionCategory>?,
        categoryName: String?,
        reorder: Boolean,
    ): List<SelectedTransactionCategory> {
        selectedCategories ?: return listOf()

        val target = categoryName ?: selectedCategories.firstOrNull()?.category?.name
        val selectedIndex = selectedCategories.indexOfFirst { (category, _) ->
            category.name.lowercase() == target?.lowercase()
        }.takeIf { it != -1 } ?: 0

        val reorderedCategories = mutableListOf<SelectedTransactionCategory>()
        if (!reorder) {
            for (index in selectedCategories.indices) {
                reorderedCategories.add(
                    SelectedTransactionCategory(
                        category = selectedCategories[index].category,
                        isSelected = index == selectedIndex,
                    )
                )
            }
        } else {
            reorderedCategories.add(
                SelectedTransactionCategory(
                    category = selectedCategories[selectedIndex].category,
                    isSelected = true,
                )
            )

            for (index in selectedCategories.indices) {
                if (index == selectedIndex) continue

                reorderedCategories.add(
                    SelectedTransactionCategory(
                        category = selectedCategories[index].category,
                        isSelected = false,
                    )
                )
            }
        }
        return reorderedCategories
    }

    fun setAutoFocusBalanceForm() {
        produce { state ->
            val autoFocusBalanceForm = "primary".takeIf {
                state.isAddProductEnabled && state.selectedProducts.isNotEmpty()
            } ?: "main"

            state.copy(
                autoFocusBalanceForm = autoFocusBalanceForm,
                overwriteExpenseExpression = true,
            )
        }
    }

    fun clearAutoFocusBalanceForm() {
        produce { state ->
            state.copy(autoFocusBalanceForm = null)
        }
    }

    fun clearReorderCategories() {
        produce { state ->
            state.copy(reorderCategories = false)
        }
    }

    fun toggleSelectedProductsExpansion() {
        produce { state ->
            state.copy(isSelectedProductsExpanded = !state.isSelectedProductsExpanded)
        }
    }

    fun updateExpenseBalance(balance: Double) {
        produce { state ->
            state.copy(expenseBalance = balance)
        }
    }

    fun updateDate(date: Date) {
        produce { state ->
            state.copy(date = date)
        }
    }

    fun updatePaymentStatus(paid: Boolean) {
        produce { state ->
            state.copy(
                paymentStatus = PaymentStatus.Paid.takeIf { paid } ?: PaymentStatus.NotPaid,
                customerErrorMessage = "",
            )
        }
    }

    fun updateExtraInfoExpansion(isExpanded: Boolean) {
        produce { state ->
            state.copy(isExtraInfoExpanded = isExpanded)
        }
    }

    fun updateImage(uri: Uri?) {
        uri?.let(::uploadImage)

        produce { state ->
            state.copy(imageUrl = uri?.toString())
        }
    }

    private fun uploadImage(uri: Uri) {
        viewModelScope.launch {
            val userId = userRepository.readUserId().takeIf(String::isNotBlank) ?: "user_upload"
            val path = "ocr_demo/$userId/${Utility.uuid()}"
            val imageUrl = imageRepository.uploadImage(uri, path)

            produce { state ->
                state.copy(imageUrl = imageUrl.takeIf(String::isNotBlank) ?: state.imageUrl)
            }
        }
    }

    fun updateNote(note: String) {
        produce { state ->
            state.copy(note = note)
        }
    }

    fun updateCustomer(info: Pair<Contact?, String>) {
        val (customer, source) = info
        produce { state ->
            state.copy(
                customer = customer,
                contactSource = source,
                customerErrorMessage = "",
            )
        }
    }

    fun updateSelectedProducts(selectedProductIds: List<Pair<String, Int>>) {
        viewModelScope.launch {
            val selectedProducts = selectedProductIds.map { (id, quantity) ->
                val product = productRepository.retrieveProductById(
                    productId = id
                ) ?: return@map null
                TransactionProduct(product, quantity)
            }.filterNotNull()

            val expenseBalance = selectedProducts.sumOf { (product, quantity) ->
                (product.capitalPrice ?: 0.0) * quantity
            }

            produce { state ->
                state.copy(
                    selectedProducts = selectedProducts,
                    expenseBalance = expenseBalance,
                    overwriteExpenseExpression = true,
                )
            }

            setAutoFocusBalanceForm()
        }
    }

    fun removeOverwriteExpenseExpression() {
        produce { state ->
            state.copy(overwriteExpenseExpression = false)
        }
    }

    fun clearErrorMessage() {
        produce { state ->
            state.copy(errorMessage = "")
        }
    }

    // TODO: Refactor implementation to Aero architecture to support testing
    fun saveTransaction(
        isFromDailyBusiness: Boolean,
        isEdit: Boolean,
        cashTransactionId: String?,
        category: TransactionCategory?,
        isAddProductEnabled: Boolean,
        selectedProducts: List<TransactionProduct>,
        expenseBalance: Double,
        date: Date,
        paymentStatus: PaymentStatus,
        isExtraInfoExpanded: Boolean,
        imageUrl: String?,
        note: String,
        customer: Contact?,
        contactSource: String,
        isCustomerRequired: Boolean,
    ) {
        if (checkIfCustomerFormInvalid(paymentStatus, customer, isCustomerRequired)) return
        if (checkIfNetworkDisconnected()) return

        viewModelScope.launch {
            var newCashTransactionId = cashTransactionId

            val userId = userRepository.readUserId()
            val incomeCategory = Category().apply {
                categoryId = category?.id
                category_name_en = category?.name
                category_name_id = category?.name
                category_type = -1
            }
            val status = 1.takeIf { paymentStatus == PaymentStatus.Paid } ?: 0
            val strDate = Utility.getStorableDateString(date)
            val products = selectedProducts.map {
                TransactionItemDto().apply {
                    transactionId = cashTransactionId
                    productId = it.product.productId
                    productName = it.product.name
                    quantity = it.quantity.toDouble()
                    sellingPrice = it.product.unitPrice
                    buyingPrice = it.product.capitalPrice
                    measurementUnit = it.product.unit
                }
            }.takeIf { isAddProductEnabled }.orEmpty()
            val totalSellingPrice = products.sumOf { it.sellingPrice * it.quantity }
            val totalBuyingPrice = products.sumOf { it.buyingPrice * it.quantity }
            val equality = when {
                -expenseBalance == totalSellingPrice -> "equals"
                -expenseBalance > totalSellingPrice -> "greater"
                else -> "less"
            }
            val recordDebt = 2.takeIf { isCustomerRequired } ?: 0

            if (!isEdit) {
                Utility.trackTransactionCount()

                val data = CashDto(
                    category = incomeCategory,
                    contact = customer,
                    status = status,
                    transactionDate = strDate,
                    amount = -expenseBalance,
                    buyingPrice = 0.0,
                    note = note,
                    currentCashTransaction = null,
                    transactingProducts = products,
                )
                newCashTransactionId = cashUseCase.get().createCashTransaction(
                    dto = data,
                    categorySelectedStatus = recordDebt,
                    attachment = imageUrl,
                    trxType = -1,
                )

                val isFirstTransaction =
                    cashUseCase.get().getCashTrxCountForCurrentBusinessId() <= 1
                val isFirstTransactionOnCategory = cashUseCase.get()
                    .getCashCategoryTrxCountForCurrentBusinessId(incomeCategory.getFormattedId()) <= 1

                val isDefaultExpenseCategory = transactionRepository.fetchIsDefaultExpenseCategory()
                val isCategoryMandatory = transactionRepository.fetchIsCategoryMandatory()

                recordAnalytics(
                    event = "transaction_added",
                    parameter = mapOf(
                        "added_by" to userId,
                        "date" to Date(),
                        "feature" to "transaksi",
                        "amount" to expenseBalance,
                        "type" to "debit",
                    ),
                )
                recordAnalytics(
                    event = ("create_new_cash_transaction".takeIf {
                        isFirstTransaction || isFirstTransactionOnCategory
                    } ?: "customer_detail_tap_saved_cash"),
                    parameter = mapOf(
                        "buying_price" to 0.0,
                        "entry_point" to ("BW_story".takeIf { isFromDailyBusiness } ?: "non_BW_story"),
                        "product_count" to products.size,
                        "transaction_id" to newCashTransactionId,
                        "category_filled" to (category?.name?.takeIf { it != "Pembelian Stok" } ?: "none"),
                        "trx_category_source" to "category_page",
                        "default_category" to "Pembelian Stok",
                        "default_category_choosen" to (category?.name == "Pembelian Stok"),
                        "default_category_expenses" to isDefaultExpenseCategory,
                        "mandatory_category_enabled" to isCategoryMandatory,
                        "customer_filled" to customer?.name.orEmpty().isNotEmpty(),
                        "customer_source" to contactSource,
                        "total_penjualan" to -expenseBalance,
                        "send_sms_reminder" to false,
                        "collapse_menu_open" to isExtraInfoExpanded,
                        "description_filled" to note.isNotEmpty(),
                        "cashTransaction_type" to "debit",
                        "sum_of_item_subtotal" to totalSellingPrice,
                        "cashTransaction_amount" to "${-expenseBalance}",
                        "sum_product_buying_price" to totalBuyingPrice,
                        "total_penjualan_vs_item_subtotal" to equality,
                        "fully_paid" to (status == 1),
                        "UI_form" to "new_UI_post_june2022",
                        "nota_standard_enabled" to RemoteConfigUtils.shouldShowNewPosInvoice()
                    ),
                )
            } else {
                val cashTransaction = retrieveCashTransaction(newCashTransactionId)

                val data = CashDto(
                    category = incomeCategory,
                    contact = customer,
                    status = status,
                    transactionDate = strDate,
                    amount = -expenseBalance,
                    buyingPrice = 0.0,
                    note = note,
                    currentCashTransaction = cashTransaction?.toCashTransactionEntity(),
                    transactingProducts = products,
                )
                cashUseCase.get().updateCashTransaction(
                    dto = data,
                    categorySelectedStatus = recordDebt,
                    attachment = imageUrl,
                    trxType = -1,
                )

                recordAnalytics(
                    event = "transaction_updated",
                    parameter = mapOf(
                        "added_by" to userId,
                        "date" to Date(),
                        "feature" to "transaksi",
                        "amount" to expenseBalance,
                        "type" to "debit",
                    ),
                )
                recordAnalytics(
                    event = "update_cash_transaction",
                    parameter = mapOf(
                        "buying_price" to 0.0,
                        "cashTransaction_amount" to "${-expenseBalance}",
                        "cashTransaction_type" to "debit",
                        "category_filled" to (category?.name?.takeIf { it != "Pembelian Stok" } ?: "none"),
                        "collapse_menu_open" to isExtraInfoExpanded,
                        "customer_filled" to customer?.name.orEmpty().isNotEmpty(),
                        "description_filled" to note.isNotEmpty(),
                        "action_by" to ("payments".takeIf { cashTransaction?.orderId.isNotNullOrEmpty() } ?: "accounting"),
                        "fully_paid" to (status == 1),
                        "transaction_id" to newCashTransactionId.orEmpty(),
                        "update_type" to "edit",
                        "customer_id" to customer?.customerId.orEmpty(),
                        "order_id" to cashTransaction?.orderId.orEmpty(),
                    ),
                )
            }

            postCashTransaction(newCashTransactionId)

            produce { state ->
                state.copy(
                    cashTransactionId = newCashTransactionId,
                    isRecordCompleted = true,
                )
            }
        }
    }

    private fun postCashTransaction(id: String?) {
        viewModelScope.launch {
            val cashTransaction = retrieveCashTransaction(id) ?: return@launch
            transactionRepository.postCashTransaction(cashTransaction)
            transactionRepository.saveLastTransactionType(type = -1)
        }
    }

    private fun checkIfCustomerFormInvalid(
        paymentStatus: PaymentStatus,
        customer: Contact?,
        isCustomerRequired: Boolean,
    ): Boolean {
        if ((paymentStatus == PaymentStatus.NotPaid || isCustomerRequired) && customer == null) {
            produce { state ->
                state.copy(
                    customerErrorMessage = lector.literate(resId = R.string.add_contact_message),
                )
            }
            return true
        }
        return false
    }

    private fun checkIfNetworkDisconnected(): Boolean {
        if (!networkMonitor.isConnected) {
            produce { state ->
                state.copy(errorMessage = lector.literate(resId = R.string.no_internet_error))
            }
            return true
        }
        return false
    }

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }
}
