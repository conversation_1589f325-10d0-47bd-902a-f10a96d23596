package com.bukuwarung.activities.expense.category

import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType
import kotlinx.android.parcel.Parcelize
import android.os.Parcelable

@Parcelize
class Category() : SelectableObject(), Parcelable {

    var category_name_en: String? = null
    var category_name_id: String? = null
    var categoryId: String? = null
    var category_type: Int = 0

    init {
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: String?, category_name_en: String?, category_name_id: String?) : this() {
        this.categoryId = id
        this.category_name_en = category_name_en
        this.category_name_id = category_name_id
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: String?, category_name_en: String?, category_name_id: String?, category_type: Int) : this() {
        this.categoryId = id
        this.category_name_en = category_name_en
        this.category_name_id = category_name_id
        this.category_type = category_type
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT)
    }

    constructor(id: String?, category_name_en: String?, category_name_id: String?, category_type: Int, type: Int) : this() {
        this.categoryId = id
        this.category_name_en = category_name_en
        this.category_name_id = category_name_id
        this.category_type = category_type
        setType(type)
    }

    override fun getId(): Int = categoryId?.hashCode() ?: 0

    override fun getIdString(): String? = categoryId

    override fun getName(): String? = category_name_id

    override fun setName(newName: String?) {
        this.category_name_id = newName
    }



    fun setId(newId: String?) {
        this.categoryId = newId
    }

    override fun getOrder(): Int = 0

    override fun equals(other: Any?): Boolean {
        return other is Category && other.getName()?.equals(getName(), ignoreCase = true) == true
    }

    override fun hashCode(): Int = getName()?.hashCode() ?: 0
}
