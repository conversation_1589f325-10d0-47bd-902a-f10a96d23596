apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion buildConfig.compileSdk

    defaultConfig {
        minSdkVersion buildConfig.minSdk
        targetSdkVersion buildConfig.targetSdk
    }

    dataBinding {
        enabled = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.bukuwarung.base_android'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "androidx.core:core-ktx:1.6.0"

    implementation "androidx.recyclerview:recyclerview:1.0.0"
    implementation "com.google.android.material:material:1.9.0"
    implementation "androidx.browser:browser:1.2.0"

    implementation "androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02"
    implementation "androidx.navigation:navigation-ui-ktx:2.3.0-alpha02"

    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.4.0"
}